<template>
  <div class="execution">
    <basic-container>
      <el-tabs type="border-card" v-model="tabValue" @tab-click="tabClick">
        <el-tab-pane name="1" label="个人列表">
          <avue-crud ref="crud"
                     :page.sync="joinPage"
                     :data="joinData"
                     :permission="permissionList"
                     :table-loading="tableLoading"
                     :option="tableOption"
                     v-model="form"
                     @on-load="getJoinPage"
                     @refresh-change="refreshChangeJoin"
                     @row-update="handleJoinUpdate"
                     @sort-change="sortJoinChange"
                     @search-change="searchChange">
            <template slot-scope="scope" slot="menuLeft">
              <el-input v-model="searchValue" :maxlength="64" placeholder="可以输入电话号码、昵称、openId" style="width:250px;padding-right: 5px"
                        size="small">
              </el-input>
              <el-button type="primary"
                         icon="el-icon-search"
                         size="small"
                         @click.stop="search">搜索
              </el-button>
              <el-button type="primary"
                         icon="el-icon-refresh-left"
                         size="small"
                         @click.stop="reset">重置
              </el-button>
            </template>
            <template slot-scope="scope" slot="status">
              <el-tag v-if="scope.row.status == '0'" type="warning" size="mini" effect="light">拼团中</el-tag>
              <el-tag v-if="scope.row.status == '1'" type="success" size="mini" effect="light">拼团成功</el-tag>
            </template>
            <template slot-scope="scope" slot="isLeader">
              <el-tag v-if="scope.row.isLeader == '0'" effect="light" size="mini">团员</el-tag>
              <el-tag v-if="scope.row.isLeader == '1'" type="success" effect="dark" size="mini">团主</el-tag>
            </template>
            <template slot-scope="scope" slot="trueFlag">
              <el-tag v-if="scope.row.trueFlag == '0'" type="success" size="mini">是</el-tag>
              <el-tag v-if="scope.row.trueFlag == '1'" type="danger" effect="dark" size="mini">否</el-tag>
            </template>

            <!-- 自定义订单状态列显示 -->
            <template slot="orderStatusDesc" slot-scope="scope">
              <el-tag
                :type="getOrderStatusTagType(scope.row.orderInfo ? scope.row.orderInfo.statusDesc : '')"
                size="small">
                {{ scope.row.orderInfo ? scope.row.orderInfo.statusDesc : '-' }}
              </el-tag>
            </template>

            <!-- 自定义退款状态列显示 -->
            <template slot="refundStatus" slot-scope="scope">
              <el-tag
                v-if="getLatestRefundStatus(scope.row)"
                :type="getRefundStatusTagType(getLatestRefundStatus(scope.row))"
                size="small">
                {{ getLatestRefundStatus(scope.row) }}
              </el-tag>
              <span v-else>-</span>
            </template>

            <!-- 自定义购买详情列显示 -->
            <template slot="purchaseDetail" slot-scope="scope">
              <span v-if="getPurchaseDetail(scope.row)">
                {{ getPurchaseDetail(scope.row) }}
              </span>
              <span v-else>-</span>
            </template>
          </avue-crud>
        </el-tab-pane>
        <el-tab-pane name="2" label="所有团队">
          <all-group-data ref="allGroupData" :id="this.id"></all-group-data>
        </el-tab-pane>
        <el-tab-pane name="3" label="未成团队">
          <un-group-data ref="unGroupData" :id="this.id"></un-group-data>
        </el-tab-pane>
        <el-tab-pane name="4" label="已成团队">
          <finish-group-data ref="finishGroupData" :id="this.id"></finish-group-data>
        </el-tab-pane>
      </el-tabs>
    </basic-container>
  </div>
</template>

<script>
import {getPage, putObj, delObj} from '@/api/viewgen/wxgrouponuser'
import {tableOption} from '@/const/crud/viewgen/wxgrouponuser'
import {mapGetters} from 'vuex'
import allGroupData from  '@/views/viewgen/groupondata/allgroupdata'
import unGroupData from  '@/views/viewgen/groupondata/ungroupdata'
import finishGroupData from  '@/views/viewgen/groupondata/finishgroupdata'


export default {
  name: 'wxgrouponuser',
  components: {
    allGroupData,
    unGroupData,
    finishGroupData,
  },
  data() {
    return {
      tabValue: '1',
      id:'',//页面Id来的
      form: {},
      tableData: [],
      joinData: [],
      groupPage: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      joinPage: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      searchValue: "",//搜索值

    }
  },
  created() {
    if (this.$route.params.id) {
      this.id = this.$route.params.id;
      window.localStorage.setItem('grouponDataId', this.id);
    } else {
      //只存不删... 可能此页面后期独立出来 暂不处理
      this.id = window.localStorage.getItem('grouponDataId');
    }
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.permissions['weixinapi:wxspellgroupuser:add'] ? true : false,
        delBtn: this.permissions['weixinapi:wxspellgroupuser:del'] ? true : false,
        editBtn: this.permissions['weixinapi:wxspellgroupuser:edit'] ? true : false,
        viewBtn: this.permissions['weixinapi:wxspellgroupuser:get'] ? true : false
      };
    }
  },
  methods: {
    tabClick(tab){
      if(tab.name=="1"){
        this.refreshChangeJoin()
      }else if(tab.name=="2"){
        this.$refs.allGroupData.refreshChange();
      }else if(tab.name=="3"){
        this.$refs.unGroupData.refreshGroup();
      }else if(tab.name=="4"){
        this.$refs.finishGroupData.refreshGroup();
      }
    },
    searchChange(params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getJoinPage(this.page, params)
      done()
    },
    sortJoinChange(val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.joinPage.descs = []
        this.joinPage.ascs = prop
      } else if (val.order == 'descending') {
        this.joinPage.ascs = []
        this.joinPage.descs = prop
      } else {
        this.joinPage.ascs = []
        this.joinPage.descs = []
      }
      this.getJoinPage(this.joinPage)
    },
    getJoinPage(page, params) {
      this.tableLoading = true
      if(!params){
        params={id:this.id};
      }
      getPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.joinPage.descs,
        ascs: this.joinPage.ascs,
      }, params, this.paramsSearch)).then(res => {
        console.log("分页",res);
        this.joinData = res.data.data.records
        this.joinPage.total = res.data.data.total
        this.joinPage.currentPage = page.currentPage
        this.joinPage.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleJoinUpdate: function (row, index, done, loading) {
      putObj(row).then(res => {
        this.$message({
          showClose: true,
          message: '修改成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * 刷新回调
     */
    refreshChangeJoin(page) {
      this.getJoinPage(this.joinPage,{id:this.id})
    },
    search() {
      this.paramsSearch = {
        id: this.id,
        status: this.status,
        nickName: this.searchValue,
        phone: this.searchValue,
        openId: this.searchValue
      }
      this.groupPage.currentPage = 1
      this.getJoinPage(this.groupPage)
    },
    reset() {
      this.searchValue = "";
      this.paramsSearch = {}
      this.groupPage.currentPage = 1
      this.getJoinPage(this.groupPage)
    },
    // 根据订单状态返回对应的标签类型
    getOrderStatusTagType(statusDesc) {
      if (statusDesc === '已完成') {
        return 'success'
      } else if (statusDesc === '已取消') {
        return 'danger' // Element UI的el-tag使用danger表示错误状态
      } else {
        return '' // 默认样式
      }
    },
    // 获取最新的退款状态
    getLatestRefundStatus(row) {
      // 从订单信息中获取退款状态
      if (row.orderInfo && row.orderInfo.listOrderRefunds && row.orderInfo.listOrderRefunds.length > 0) {
        const latestRefund = row.orderInfo.listOrderRefunds[row.orderInfo.listOrderRefunds.length - 1]
        return latestRefund.statusDesc || latestRefund.status || '-'
      }
      return null
    },
    // 根据退款状态返回对应的标签类型
    getRefundStatusTagType(statusDesc) {
      if (statusDesc === '退款申请中') {
        return 'warning' // 黄色
      } else if (statusDesc === '同意退款') {
        return 'danger' // 红色
      } else if (statusDesc === '拒绝') {
        return '' // primary蓝色（默认）
      } else {
        return 'info' // 其他状态用info灰色
      }
    },
    // 获取购买详情
    getPurchaseDetail(row) {
      // 从订单信息中获取商品详情
      if (row.orderInfo && row.orderInfo.listOrderItem && row.orderInfo.listOrderItem.length > 0) {
        const firstItem = row.orderInfo.listOrderItem[0]
        return `${firstItem.spuName}|${firstItem.specInfo}`
      }
      return null
    }

  }
}
</script>

<style lang="scss" scoped>
</style>
