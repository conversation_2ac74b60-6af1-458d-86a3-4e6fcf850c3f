<!--拍摄档期-->
<template>
  <div class="execution">
    <basic-container>
      <el-row :gutter="24" type="flex" justify="start" style="margin-bottom: 5px">
        <el-col :span="2">
          <el-button type="primary" size="small" @click="shopDrawerVisible = true">场地管理</el-button>
        </el-col>
        <el-col :span="5">
          <el-cascader
            style="width: 100%;"
            size="small"
            v-model="selectedLocation"
            :options="locationOptions"
            :props="cascaderProps"
            placeholder="请选择场地"
            clearable
            @change="handleLocationChange">
          </el-cascader>
        </el-col>
        <el-col :span="2">
          <el-button type="primary" size="small" icon="el-icon-search" @click="searchByLocation">搜索档期</el-button>
        </el-col>
        <el-col :span="2">
          <el-button size="small" icon="el-icon-refresh" @click="resetSearch">重置</el-button>
        </el-col>
      </el-row>
      <el-calendar v-model="value" >
        <template slot="dateCell" slot-scope="{date, data}">
          <div class="calendar_div" :class="getClass(data)" slot="reference" @click="openDetail(data)">
            <div>{{ data.day.split("-").slice(1).join("-") }}{{ data.isSelected ? '✔️' : '' }}</div>
          </div>
        </template>
      </el-calendar>
    </basic-container>

    <!--    店铺和场地抽屉-->
    <el-drawer
      :append-to-body="true"
      title="档期管理"
      size="55%"
      :visible.sync="shopDrawerVisible"
      :direction="'rtl'">
      <calendar-manager></calendar-manager>
    </el-drawer>

    <!--    档期详情抽屉-->
    <el-drawer
      :append-to-body="true"
      title="档期详情"
      size="75%"
      :visible.sync="detailDrawerVisible"
      :direction="'rtl'"
      @open="handleDetailDrawerOpen"
      @close="handleDetailDrawerClose">
      <calendar-detail 
        ref="calendarDetail"
        :day="selectedDay" 
        :selected-location="detailDrawerVisible ? selectedLocation : []" 
        @dateChange="dateChange()">
      </calendar-detail>
    </el-drawer>
  </div>
</template>

<script>
import {getPage, addObj, putObj, delObj,getMonth} from '@/api/wxmp/auctioncalendar'
import {getShopAndRoomTree} from '@/api/wxmp/shopinfo'
import {mapGetters} from 'vuex'
import {dateFormat} from "@/util/date";
import calendarManager from '@/views/viewgen/auctioncalendar/calendarmanager'
import calendarDetail from '@/views/viewgen/auctioncalendar/calendardetail'

export default {
  name: 'auctioncalendar',
  components: {calendarManager, calendarDetail,},
  data() {
    return {
      value:'',//日历值
      dateMap:{},//每天的数量
      selectedDay: '',//选中时间
      detailDrawerVisible: false,//档期详情
      shopDrawerVisible: false,
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: '',
      // 新增的场地筛选相关数据
      locationOptions: [], // 场地选择器的选项
      selectedLocation: [], // 选中的场地ID
      cascaderProps: {
        checkStrictly: false, // 父子节点不独立选择
        expandTrigger: 'hover',
        value: 'id',
        label: 'name',
        children: 'roomList',
        emitPath: true,
        multiple: true // 支持多选
      }
    }
  },
  created() {
    this.$nextTick(() => {
      // 点击前一个月
      let prevBtn = document.querySelector(
        ".el-calendar__button-group .el-button-group>button:nth-child(1)"
      );
      prevBtn.addEventListener("click", e => {
        this.initMonthData(dateFormat(this.value).split(" ")[0]);
      });

      //点击下一个月
      let nextBtn = document.querySelector(
        ".el-calendar__button-group .el-button-group>button:nth-child(3)"
      );
      nextBtn.addEventListener("click", () => {
        this.initMonthData(dateFormat(this.value).split(" ")[0]);
      });

      //点击今天
      let todayBtn = document.querySelector(
        ".el-calendar__button-group .el-button-group>button:nth-child(2)"
      );
      todayBtn.addEventListener("click", () => {
        this.initMonthData(dateFormat(this.value).split(" ")[0]);
      });
    });
    
    // 加载场地数据
    this.loadLocationOptions();
  },
  mounted: function () {
    this.initMonthData()
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.permissions['weixin:auctioncalendar:add'] ? true : false,
        delBtn: this.permissions['weixin:auctioncalendar:del'] ? true : false,
        editBtn: this.permissions['weixin:auctioncalendar:edit'] ? true : false,
        viewBtn: this.permissions['weixin:auctioncalendar:get'] ? true : false
      };
    }
  },
  methods: {
    // 加载场地选项数据
    loadLocationOptions() {
      getShopAndRoomTree().then(res => {
        if (res.data && res.data.data) {
          this.locationOptions = res.data.data;
        }
      }).catch(err => {
        console.error('获取场地数据失败:', err);
      });
    },
    
    // 处理场地选择变化
    handleLocationChange(value) {
      // 不再自动加载，由搜索按钮触发
      // this.initMonthData(dateFormat(this.value).split(" ")[0]);
    },
    
    // 搜索按钮点击处理
    searchByLocation() {
      // 确保使用有效的日期对象
      const currentDate = this.value ? this.value : new Date();
      this.initMonthData(dateFormat(currentDate).split(" ")[0]);
    },
    
    initMonthData(date) {
      if(!date){
        let now = dateFormat(new Date())
        date = now.split(" ")[0]
      }else{
        date = date
      }
      let params = {
        month: date
      }
      
      // 添加场地筛选条件
      if (this.selectedLocation && this.selectedLocation.length > 0) {
        const shopIds = [];
        const roomIds = [];
        
        // 处理多选的情况
        this.selectedLocation.forEach(path => {
          // 如果是店铺和房间的完整路径
          if (path.length === 2) {
            const shopId = path[0];
            const roomId = path[1];
            
            if (!shopIds.includes(shopId)) {
              shopIds.push(shopId);
            }
            
            roomIds.push(roomId);
          } 
          // 如果只选择了店铺
          else if (path.length === 1) {
            const shopId = path[0];
            if (!shopIds.includes(shopId)) {
              shopIds.push(shopId);
            }
          }
        });
        
        /* if (shopIds.length > 0) {
          params.shopIds = shopIds.join(',');
        } */
        
        if (roomIds.length > 0) {
          params.roomIds = roomIds.join(',');
        }
      }
      
      // console.log("请求",params)
      getMonth(params).then(res=>{
        //分组
        let monthList = res.data.data;
        let map ={}
        monthList.forEach(o=>{
          if(!map[o.date]){
            map[o.date] = o.num;
          }
        })
        // 使用深拷贝确保Vue能检测到变化
        this.dateMap = JSON.parse(JSON.stringify(map));
        // console.log("什么结果",this.dateMap)
      }).catch()
    },
    searchChange(params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    sortChange(val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage(page, params) {
      this.tableLoading = true
      
      // 构建请求参数
      const requestParams = {
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
        ...params,
        ...this.paramsSearch
      };
      
      // 添加场地筛选条件
      if (this.selectedLocation && this.selectedLocation.length > 0) {
        const shopIds = [];
        const roomIds = [];
        
        // 处理多选的情况
        this.selectedLocation.forEach(path => {
          // 如果是店铺和房间的完整路径
          if (path.length === 2) {
            const shopId = path[0];
            const roomId = path[1];
            
            if (!shopIds.includes(shopId)) {
              shopIds.push(shopId);
            }
            
            roomIds.push(roomId);
          } 
          // 如果只选择了店铺
          else if (path.length === 1) {
            const shopId = path[0];
            if (!shopIds.includes(shopId)) {
              shopIds.push(shopId);
            }
          }
        });
        
        if (shopIds.length > 0) {
          requestParams.shopIds = shopIds.join(',');
        }
        
        if (roomIds.length > 0) {
          requestParams.roomIds = roomIds.join(',');
        }
      }
      
      getPage(requestParams).then(response => {
        this.tableData = response.data.data.records
        this.page.total = response.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this
      this.$confirm('是否确认删除此数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        return delObj(row.id)
      }).then(data => {
        _this.$message({
          showClose: true,
          message: '删除成功',
          type: 'success'
        })
        this.getPage(this.page)
      }).catch(function (err) {
      })
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      putObj(row).then(response => {
        this.$message({
          showClose: true,
          message: '修改成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      addObj(row).then(response => {
        this.$message({
          showClose: true,
          message: '添加成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page)
    },
    openDetail(val) {
      this.selectedDay = val.day;
      if(val.type == "current-month"){
        this.detailDrawerVisible = true;
      }else{
        this.initMonthData(val.day);
      }
    },
    getClass(obj){
      if(this.dateMap[obj.day]){
        return "appoint_ensure"
      }
      return "appoint_none"
    },
    dateChange(){
      console.log("改了")
      this.initMonthData(this.selectedDay)
    },
    handleDetailDrawerOpen() {
      // 当抽屉打开时，确保组件能获取到最新的场地选择
      // 由于我们已经在模板中通过条件绑定传递了 selectedLocation
      // 这里不需要额外操作
    },
    handleDetailDrawerClose() {
      this.$refs.calendarDetail.clearPhoneParam();
    },
    
    // 重置搜索
    resetSearch() {
      this.selectedLocation = [];
      // 确保使用有效的日期对象
      const currentDate = this.value ? this.value : new Date();
      this.initMonthData(dateFormat(currentDate).split(" ")[0]);
    }
  }
}
</script>
<style lang="scss" scoped>
.calendar_div {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.appoint_ensure {
  background-color: #d4aa5e;
}
.appoint_none {
  background-color: #cce3c0;
}
.appoint_flag {
  margin: 15px 0 15px 0;
}
</style>
