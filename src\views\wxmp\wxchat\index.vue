<template>
  <div>
    <!--    标签框-->
    <basic-container>
      <div class="all_chat_box">
        <el-header style="height: 20%">
          <div>
            <el-collapse accordion>
              <el-collapse-item title="展开搜索栏">
                <el-form ref="form" :model="userCondition" label-width="100px" size="mini">
                  <el-form-item label="所选公众号">
                    <el-select v-model="userCondition.appIdList" multiple placeholder="请选择">
                      <el-option
                        v-for="item in wxAppList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="搜索类型">
                    <el-radio-group v-model="userGetTypeRadio">
                      <el-radio :label="1">用户属性</el-radio>
                      <el-radio :label="2">用户行为</el-radio>
                      <el-radio :label="3">接待客服</el-radio>
                      <el-radio :label="4">关注来源</el-radio>
                      <el-radio :label="5">微信广告</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-row v-show="userGetTypeRadio==1">
                    <el-col :span="6">
                      <el-form-item label="关注时间">
                        <el-date-picker
                          v-model="subscribeTimeList"
                          type="daterange"
                          align="right"
                          unlink-panels
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          value-format="yyyy-MM-dd 00:00:00"
                          @change="subscribeTimeOnPick"
                          :picker-options="attentionDateOptions">
                        </el-date-picker>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row v-show="userGetTypeRadio==1">
                    <el-col :span="5">
                      <el-form-item label="关注来源">
                        <el-select v-model="userCondition.subscribeScene" placeholder="请选择">
                          <el-option
                            v-for="item in attentionFromOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="5">
                      <el-form-item label="是否关注">
                        <el-select v-model="userCondition.subscribe" placeholder="请选择">
                          <el-option
                            v-for="item in isAttentionOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="5">
                      <el-form-item label="是否黑名单">
                        <el-select v-model="userCondition.inBackList" placeholder="请选择">
                          <el-option
                            v-for="item in blackListOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="5">
                      <el-form-item label="是否有手机号">
                        <el-select v-model="userCondition.phoneType" placeholder="请选择">
                          <el-option
                            v-for="item in phoneNumberOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row v-show="userGetTypeRadio==1">
                    <el-col :span="5">
                      <el-form-item label="昵称">
                        <el-input v-model="userCondition.nickName" placeholder="请输入昵称"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="5">
                      <el-form-item label="openId">
                        <el-input v-model="userCondition.openId" placeholder="请输入openId"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="5">
                      <el-form-item label="手机号码">
                        <el-input v-model="userCondition.phone" placeholder="请输入手机号"></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row v-show="userGetTypeRadio==2">
                    <el-form-item label="行为节点">
                      <el-select v-model="userCondition.behaviorNodes" multiple placeholder="请选择">
                        <el-option-group
                          v-for="group in behaviorNodeList"
                          :key="group.label"
                          :label="group.label">
                          <el-option
                            v-for="item in group.options"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                          </el-option>
                        </el-option-group>
                      </el-select>
                    </el-form-item>
                  </el-row>
                  <el-row v-show="userGetTypeRadio==2">
                    <el-col :span="6">
                      <el-form-item label="记录时间">
                        <el-date-picker
                          v-model="behaviorNodeTimeList"
                          type="daterange"
                          align="right"
                          unlink-panels
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          value-format="yyyy-MM-dd 00:00:00"
                          @change="behaviorNodeTimeOnPick"
                          :picker-options="attentionDateOptions">
                        </el-date-picker>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row v-show="userGetTypeRadio==3">
                    <el-form-item label="行为节点">
                      <el-select v-model="userCondition.appIdList" multiple placeholder="请选择">
                        <!--                        <el-option-->
                        <!--                          v-for="item in wxAppList"-->
                        <!--                          :key="item.id"-->
                        <!--                          :label="item.name"-->
                        <!--                          :value="item.id">-->
                        <!--                        </el-option>-->
                      </el-select>
                    </el-form-item>
                  </el-row>
                  <el-row v-show="userGetTypeRadio==4">
                    <el-form-item label="来源类型">
                      <el-select v-model="userCondition.appIdList" multiple placeholder="请选择">
                        <!--                        <el-option-->
                        <!--                          v-for="item in wxAppList"-->
                        <!--                          :key="item.id"-->
                        <!--                          :label="item.name"-->
                        <!--                          :value="item.id">-->
                        <!--                        </el-option>-->
                      </el-select>
                    </el-form-item>
                  </el-row>
                  <el-row v-show="userGetTypeRadio==4">
                    <el-col :span="6">
                      <el-form-item label="关注时间">
                        <el-date-picker
                          v-model="userCondition.attentionDate"
                          type="daterange"
                          align="right"
                          unlink-panels
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          :picker-options="attentionDateOptions">
                        </el-date-picker>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row v-show="userGetTypeRadio==5">
                    <el-col :span="3">
                      <el-form-item label="投放计划">
                        <el-select v-model="userCondition.subscribeScene" placeholder="请选择">
                          <el-option
                            v-for="item in attentionFromOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="3">
                      <el-form-item label="广告id">
                        <el-select v-model="userCondition.attentionFrom" placeholder="请选择">
                          <el-option
                            v-for="item in isAttentionOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row v-show="userGetTypeRadio==5">
                    <el-col :span="6">
                      <el-form-item label="关注时间">
                        <el-date-picker
                          v-model="userCondition.attentionDate"
                          type="daterange"
                          align="right"
                          unlink-panels
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          :picker-options="attentionDateOptions">
                        </el-date-picker>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-form-item size="large">
                    <el-button type="primary" size="small" @click="userSearch">立即搜索</el-button>
                    <el-button type="primary" size="small" @click="resetSearch">重置</el-button>
                  </el-form-item>
                </el-form>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-header>
        <el-main>
          <el-container>
            <!--            左边用户列表容器-->
            <el-scrollbar class="scrollbar_user" ref="userListScrollRef"
                          id="userListScroll">
              <div v-for="item in userList" :key="item.id" class="user_list_box" @click="getCurrentUser(item)">
                  <div @contextmenu.prevent="openRightMenu($event,item)"  >
                    <div :class="item.topFlag=='1'?'user_top':''" :style="{padding:'5px', border:item.selectFlag?'1px red solid':''}" >
                      <el-row >
                        <div class="user_box_title">
                          <p class="user_box_title_appName">{{ formatAppName(item.appId) }}</p>
                          <div  class="user_box_title_tag">
                            <p >{{ item.subscribeScene | formatSubscribeScene }}</p>
                            <el-tag v-if="item.subscribe=='1'" size="mini" type="success">已关注
                            </el-tag>
                            <el-tag  v-if="item.subscribe=='0'" size="mini" type="info">未关注</el-tag>
                          </div>
                        </div>
                      </el-row>
                      <el-row>
                        <div class="user_box_main">
                          <el-col :span="5">
                            <el-badge :value="item.unReadNum" :hidden="item.unReadNum == 0">
                            <el-avatar class="user_box_main_avatar" :src="item.headimgUrl" shape="square"></el-avatar>
                            </el-badge>
                          </el-col>
                          <el-col :span="19">
                            <p class="user_box_main_nickname">{{ item.nickName }}</p>
                            <p class="user_box_main_phone">{{ item.phone }}</p>
                          </el-col>
                        </div>
                      </el-row>
                      <el-row>
                        <div class="user_box_foot">
                          <p>{{ item.country + item.province + item.city }}</p>
                          <el-tag v-if="item.updateTime" class="user_box_foot_updateTime" size="mini">
                            {{ item.updateTime | formatUserUpdateTime }}
                          </el-tag>
                        </div>
                      </el-row>
                    </div>
                  </div>
              </div>
            </el-scrollbar>
            <!--            中间容器-->
            <div class="main_chat">
              <div class="main_chat_head">
                <div class="main_chat_head_div">
                  <el-avatar class="main_chat_head_avatar" :src="currentUser.headimgUrl"></el-avatar>
                  <div class="main_chat_head_top">
                    <el-row>
                      <el-col :span=6>
                        <p>{{ currentUser.nickName }}</p>
                      </el-col>
                      <el-col :span="12">
                        <p>关注时间:
                          <el-tag size="mini">{{ currentUser.subscribeTime }}</el-tag>
                        </p>
                      </el-col>
                      <el-col :span="6">
                        <p>来源:{{ currentUser.subscribeScene | formatSubscribeScene }}</p>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="16">
                        <p>openId:{{ currentUser.openId }}</p>
                      </el-col>
                      <el-col :span="4">
                        <p>当前座席:无</p>
                      </el-col>
                      <el-col :span="4">
                        <p>粉丝类型:无</p>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </div>
              <div class="main_chat_show_msg">
                <div class="msg-div" :id="'msg-div'+nowStr">
                  <div v-loading="tableLoading"></div>
                  <div v-if="!tableLoading">
                    <div class="el-table__empty-block" v-if="loadMore" @click="loadingMore"><span
                      class="el-table__empty-text">点击加载更多</span></div>
                    <div class="el-table__empty-block" v-if="!loadMore"><span class="el-table__empty-text">没有更多了</span>
                    </div>
                  </div>
                  <div class="execution " v-for="item in msgList" :key='"wxchat"+item.id'>
                    <div v-if="item.repType == 'event'" class="msg_event">
                      <div>{{ item.createTime }}</div>
                      <div v-if="item.repEvent == 'subscribe'">
                        <p>
                          <el-tag type="success" size="mini">关注</el-tag>
                        </p>
                      </div>
                      <div v-if="item.repEvent == 'unsubscribe'">
                        <p>
                          <el-tag type="danger" size="mini">取消关注</el-tag>
                        </p>
                      </div>
                      <div v-if="item.repEvent == 'CLICK'">
                        <p >
                          <el-tag size="mini">点击菜单</el-tag>
                          ：【{{ item.repName }}】
                        </p>
                      </div>
                      <div v-if="item.repEvent == 'VIEW'">
                        <el-popover
                          placement="top"
                          width="200"
                          trigger="click"
                          :content="item.repUrl">
                          <p slot="reference" >
                            <el-tag size="mini" >点击菜单链接</el-tag>
                            ：【{{ item.repName}} 点击查看】
                          </p>
                        </el-popover>
                      </div>
                      <div v-if="item.repEvent == 'SCAN'">
                        <p>
                          <el-tag size="mini">扫描二维码</el-tag>
                          ：【{{ item.repName }}】
                        </p>
                      </div>
                      <div v-if="item.repEvent == 'scancode_waitmsg'">
                        <p>
                          <el-tag size="mini">扫码结果：</el-tag>
                          ：【{{ item.repContent }}】
                        </p>
                      </div>
                      <div v-if="item.repEvent == '002'">
                        <p>
                          <el-tag size="mini" >进入页面</el-tag>
                          <el-tag v-if="item.repName" type="success" size="mini" >分享人：{{ item.repName }}</el-tag>
                          ：【{{ item.repContent }}】
                        </p>
                      </div>
                      <div v-if="item.repEvent == '004'">
                        <p>
                          <el-tag size="mini">提交表单</el-tag>
                          ：【{{ item.repContent }}】
                        </p>
                      </div>
                    </div>
                    <div v-if="item.repType != 'event'" class="avue-comment"
                         :class="item.type == '2' ? 'avue-comment--reverse' : ''">
                      <div class="avatar-div">
                        <el-avatar :src="item.type == '1' ? item.headimgUrl : item.appLogo"/>
                        <!--                        <div class="msg_content_author">{{item.type == '1' ? item.nickName : item.appName}}</div>-->
                      </div>
                      <div class="msg_content_main">
                        <div v-if="item.repType != 'event'" class="avue-comment__header">
                          <div class="msg_content_main_create_time">{{ item.createTime }}</div>
                        </div>
                        <div class="msg_comment_main_body" :style="item.type == '2' ? 'background: #6BED72;' : ''">
                          <div v-if="item.repType == 'text'">{{ item.repContent }}</div>
                          <div v-if="item.repType == 'image'">
                            <a target="_blank" :href="item.repUrl"><img :src="item.repUrl" style="width: 100px"></a>
                          </div>
                          <div v-if="item.repType == 'voice'">
                            <WxVoicePlayer :appId="appId" :objData="item"></WxVoicePlayer>
                          </div>
                          <div v-if="item.repType == 'video'" style="text-align: center">
                            <WxVideoPlayer :appId="appId" :objData="item"></WxVideoPlayer>
                          </div>
                          <div v-if="item.repType == 'shortvideo'" style="text-align: center">
                            <WxVideoPlayer :appId="appId" :objData="item"></WxVideoPlayer>
                          </div>
                          <div v-if="item.repType == 'location'">
                            <el-link type="primary" target="_blank"
                                     :href="'https://map.qq.com/?type=marker&isopeninfowin=1&markertype=1&pointx='+item.repLocationY+'&pointy='+item.repLocationX+'&name='+item.repContent+'&ref=gocreateone'">
                              <img
                                :src="'https://apis.map.qq.com/ws/staticmap/v2/?zoom=10&markers=color:blue|label:A|'+item.repLocationX+','+item.repLocationY+'&key='+qqMapKey+'&size=250*180'">
                              <p/><i class="el-icon-map-location"></i>{{ item.repContent }}
                            </el-link>
                          </div>
                          <div v-if="item.repType == 'link'" class="avue-card__detail">
                            <el-link type="success" :underline="false" target="_blank" :href="item.repUrl">
                              <div class="avue-card__title"><i class="el-icon-link"></i>{{ item.repName }}</div>
                            </el-link>
                            <div class="avue-card__info" style="height: unset">{{ item.repDesc }}</div>
                          </div>
                          <div v-if="item.repType == 'news'" style="width: 300px">
                            <WxNews :objData="item.content.articles"></WxNews>
                          </div>
                          <div v-if="item.repType == 'page'" style="width: 300px">
                            <WxNews :objData="item.content.articles"></WxNews>
                          </div>
                          <div v-if="item.repType == 'music'">
                            <el-link type="success" :underline="false" target="_blank" :href="item.repUrl">
                              <div class="avue-card__body"
                                   style="padding:10px;background-color: #fff;border-radius: 5px">
                                <div class="avue-card__avatar"><img :src="item.repThumbUrl" alt=""></div>
                                <div class="avue-card__detail">
                                  <div class="avue-card__title" style="margin-bottom:unset">{{ item.repName }}</div>
                                  <div class="avue-card__info" style="height: unset">{{ item.repDesc }}</div>
                                </div>
                              </div>
                            </el-link>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="main_chat_foot">
                <div class="main_chat_foot_icon">
                  <el-popover
                    ref="facePopoverRef"
                    placement="top-start"
                    width="400"
                    trigger="click"
                    v-model="facePopoverVisible">
                    <!--    微信表情-->
                    <emoji-picker v-model="msgData.repContent" button  height='30vh'/>
                  </el-popover>
                  <el-button v-popover:facePopoverRef  size="mini">表情</el-button>
                  <el-button size="mini" @click="openReplyBox('image')">图片</el-button>
                  <el-button size="mini" @click="openReplyBox('voice')">语音</el-button>
                  <el-button size="mini" @click="openReplyBox('video')">视频</el-button>
                  <el-button size="mini" @click="openReplyBox('news')">图文</el-button>
                  <el-button size="mini" @click="openReplyBox('music')">音乐</el-button>
                  <el-button size="mini" @click="openReplyBox('page')">网页</el-button>
                </div>
                <div class="send_msg_input">
                  <el-input class="msg_input"  type="textarea"  :maxlength="200"
                            resize="none" @keydown.native="shiftEnter"
                            show-word-limit placeholder="请输入内容，按enter键发送" v-model="msgData.repContent">
                  </el-input>
                </div>
              </div>
            </div>
            <!--            右边容器-->
            <div class="left_container">
              <div class="left_info_box">
                <el-tabs type="border-card" @tab-click="baseMsgTabClick">
                  <el-tab-pane name="base" label="基础信息">
                    <el-form label-position="left" label-width="55px" class="user_base_info_form">
                      <el-form-item label="状态:">
                        <el-radio-group v-model="currentUser.status">
                          <el-radio label="默认分配"></el-radio>
                          <el-radio label="不再分配给坐席"></el-radio>
                        </el-radio-group>
                      </el-form-item>
                      <el-form-item label="手机：">
                        <el-input size="mini" v-model="currentUser.phone"></el-input>
                      </el-form-item>
                      <el-form-item label="备注：">
                        <el-input size="mini" v-model="currentUser.remark"></el-input>
                      </el-form-item>
                      <el-form-item label="标签：">
                        <div @click="openUserTagBox" class="user_base_info_form_tag">
                          <el-tag v-for="(tag,index) in someoneTagsList" :key="index" size="medium"
                                  :color="tag.backColor" :style="getFontColor(tag.fontColor)">{{ tag.name }}
                          </el-tag>
                          <el-button icon="el-icon-plus" size="mini"></el-button>
                        </div>
                      </el-form-item>
                    </el-form>
                  </el-tab-pane>
                  <el-tab-pane name="friend" label="认识的好友">
                    <div style="overflow: scroll; height: 30vh;width: 100%">
                      <wx-user-friend ref="wxUserFriend" :appId="currentUser.appId"  :userId ="currentUser.id"> </wx-user-friend>
                    </div>
                  </el-tab-pane>
                  <el-tab-pane name="system" label="系统管理">
                    <el-button @click="openChatSystem">修改系统设置</el-button>
                  </el-tab-pane>
                </el-tabs>
              </div>
              <div class="left_fast_reply_box">
                <el-tabs type="border-card" v-model="fastReplyActive">
                  <el-tab-pane label="个人快捷回复" name="person">
                    <ul class="fast_reply_ul">
                      <div v-for="(item,index) in personFastReply" :key="index" class="fast_reply_ul_div">
                        <li @click="addPreSendMessage(item.content)">
                          <el-tag class="fast_reply_ul_tag" size="mini">{{ item.name }}</el-tag>
                          <el-tooltip :content="item.content" placement="top"><p>{{ item.content }}</p></el-tooltip>
                        </li>
                        <el-divider></el-divider>
                      </div>
                    </ul>
                  </el-tab-pane>
                  <el-tab-pane label="公共快捷回复" name="public">
                    <ul class="fast_reply_ul">
                      <div v-for="(item,index) in publicFastReply" :key="index" class="fast_reply_ul_div">
                        <li @click="addPreSendMessage(item.content)">
                          <el-tag class="fast_reply_ul_tag" size="mini">{{ item.name }}</el-tag>
                          <el-tooltip :content="item.content" placement="top"><p>{{ item.content }}</p></el-tooltip>
                        </li>
                        <el-divider></el-divider>
                      </div>
                    </ul>
                  </el-tab-pane>
                </el-tabs>
              </div>
            </div>
          </el-container>
        </el-main>
      </div>
    </basic-container>

    <!-- 用户标签栏 userTagBoxVisible-->
    <el-dialog
      title="标签"
      :visible.sync="userTagBoxVisible"
      width="50%"
      center>
      <div class="userTagBox">
        <div v-for="(item,index1) in tagsAndTypeList" :key="index1">
          <div class="userTagBox_type">
            <el-divider content-position="left">{{ item.tagTypeName }}</el-divider>
            <div class="" v-for="(tag,index2) in item.tagList" :key="index2">
              <diV @click.prevent="editTagLink(tag,index1,index2)" class="userTagBox_tag">
                <el-checkbox :value="tag.checked"></el-checkbox>
                <el-tag v-popover="tag.id" size="medium" :color="tag.backColor" :style="getFontColor(tag.fontColor)">
                  {{ tag.name }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
    <!-- 消息发送栏 userTagBoxVisible-->
    <el-dialog
      title="消息"
      :visible.sync="replyBoxVisible"
      width="60%"
      @close="closeReplyBox"
      center>
      <wx-chat-reply :wxApp="currentWxApp" :appId="currentUser.appId" :objData="msgData" @sendMsg="sendMsg"></wx-chat-reply>
    </el-dialog>
    <!--    系统设置抽屉-->
    <el-drawer
      title=""
      :visible.sync="chatSystemVisible"
      direction="rtl"
      :with-header="false"
      size=“65%”>
      <el-tabs type="border-card" v-model="systemActive">
        <el-tab-pane label="个人快捷设置" name="person">
          <el-button @click="openFastReplyBox('1','add')">添加</el-button>
          <el-divider></el-divider>
          <el-table
            :data="personFastReply"
            @row-click="personFastReplyRowClick"
            style="width: 100%">
            <el-table-column
              prop="name"
              label="标识名称"
              width="200px">
            </el-table-column>
            <el-table-column
              prop="content"
              label="内容">
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              width="100px">
              <template slot-scope="scope">
                <el-button
                  @click.native.prevent.stop="delFastReply(scope.row.id)"
                  type="text">
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="公共快捷设置" name="public">
          <el-button @click="openFastReplyBox('2','add')">添加</el-button>
          <el-divider></el-divider>
          <el-table
            :data="publicFastReply"
            @row-click="publicFastReplyRowClick"
            style="width: 100%">
            <el-table-column
              prop="name"
              label="标识名称"
              width="200px">
            </el-table-column>
            <el-table-column
              prop="content"
              label="内容">
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              width="100px">
              <template slot-scope="scope">
                <el-button
                  @click.native.prevent.stop="delFastReply(scope.row.id)"
                  type="text">
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="广告回传" name="advertising">广告回传</el-tab-pane>
        <el-tab-pane label="接入与权重" name="allocation">接入与权重</el-tab-pane>
        <el-tab-pane label="行为设定" name="behaviour">行为设定</el-tab-pane>
        <el-tab-pane label="类型设置" name="fanType">类型设置</el-tab-pane>
        <el-tab-pane label="分配记录" name="distribution">分配记录</el-tab-pane>
        <el-tab-pane label="数据报表" name="dataReport">数据报表</el-tab-pane>
        <el-tab-pane label="在线记录" name="onlineRecord">在线记录</el-tab-pane>
        <el-tab-pane label="今日对话" name="dialogue">今日对话</el-tab-pane>
      </el-tabs>
    </el-drawer>
    <!--    添加快捷回复-->
    <el-dialog
      :title="fastReplyBox.title"
      :visible.sync="fastReplyBox.visible"
      width="30%"
      :before-close="fastReplyBoxClose"
      center>
      <el-form :ref="fastReplyFrom" :model="fastReply" :rules="fastReplyRules" label-width="80px">
        <el-form-item label="识别名称" prop="name">
          <el-input v-model="fastReply.name" :maxlength="10" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="消息内容" prop="content">
          <el-input type="textarea" v-model="fastReply.content" :rows="5" :maxlength="200" show-word-limit></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="confirmFastReply">确认</el-button>
          <el-button @click="fastReplyBox.visible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!--    用户右键菜单isCollapse-->
    <ul v-show="rightMenuVisible" :style="{left:rightMenu.left+'px',top:rightMenu.top+'px'}" class="contextmenu">
      <li @click="addBackList">拉入黑名单</li>
      <li @click="putTop(rightClickItem)">{{ (!rightClickItem.topFlag || rightClickItem.topFlag=='0')?"置顶":"取消置顶" }}</li>
    </ul>

  </div>
</template>

<script>
import {getPageAndCon, putTopFlag} from '@/api/wxmp/wxuser'
import {addObj as addBackList} from '@/api/wxmp/wxbacklist'
import {
  getPage as getFastReplyPage,
  addObj as addFastReply,
  delObj as delFastReply,
  putObj as putFastReply
} from '@/api/wxmp/wxfastreply'
import {getList as getWxAppList} from '@/api/wxmp/wxapp'
import {addObj as sendMsg, getPage, getPage as getMsgPage, putUserAllReadFlag, putObj} from '@/api/wxmp/wxmsg'
import {getUserTagAndType, addObj as addTag,} from '@/api/wxmp/wxusertags'
import {getSomeoneTags, editTagLink} from '@/api/wxmp/wxusertaglink'
import {mapGetters} from 'vuex'
import WxReplySelect from '@/components/wx-reply/main.vue'
import store from "@/store";
import SockJS from 'sockjs-client';
import Stomp from 'stompjs';
import WxNews from '@/components/wx-news/main.vue'
import EmojiPicker from '@/components/wx-emoji-picker/main.vue'
import WxChatReply from '@/views/wxmp/wxchat/reply.vue'
import WxUserFriend from '@/views/wxmp/wxchat/wxuserfriend.vue'

export default {
  name: 'wxchat',
  components: {
    WxReplySelect,
    EmojiPicker,
    WxChatReply,
    WxUserFriend,
    WxNews,
  },
  data() {
    return {
      chatTimer: "",//socket定时器
      someoneTagsList: "",//用户绑定标签
      tagsAndTypeList: "",//标签分类
      userTagBoxVisible: false,//标签分类表
      rightMenuVisible: false,
      facePopoverVisible: false,//表情框
      replyBoxVisible: false,//消息回复框
      rightClickItem: '',
      rightMenu: {
        left: '',
        top: '',
      },
      chatSystemVisible: false,//系统设置框
      wxAppList: [],//公众号
      fastReplyBox: {
        visible: false,
        title: "添加快捷回复",//快捷回复框标题
        type: "",//快捷回复框类型  add/put
      },
      //用户列表
      userPage: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 50, // 每页显示多少条
        ascs: [],
        descs: 'subscribe_time'
      },
      //用户查询参数
      userCondition: {
        appIdList: [],//选中公众号查询
      },
      subscribeTimeList: [],
      behaviorNodeTimeList: [],
      userConditionVisible: false,//用户查询参数显示值
      userGetTypeRadio: 1,//查询用户类型选择
      //消息列表
      msgPage: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 40, // 每页显示多少条
        ascs: [],
        descs: 'create_time'
      },
      //快捷回复列表
      fastReplyPage: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        size: 100, // 每页显示多少条
        ascs: [],
        descs: 'sort'
      },
      userList: [],//用户数组
      currentUser: {},//当前用户
      currentWxApp: {},//当前公众号
      nowStr: new Date().getTime(),
      msgData: { //发送的消息
        repType: 'text',
        repContent: ''
      },
      msgList: [],//用户消息数组
      mainLoading: false,
      sendLoading: false,
      tableLoading: false,//不知什么用处
      loadMore: true,
      fastReplyFrom: {},
      fastReply: {},//快捷回复
      personFastReply: [],//个人快捷回复
      publicFastReply: [],//公共快捷回复
      fastReplyParam: {
        create_id: "1279041761051721730",
        type: "1"
      },
      fastReplyActive: "person",//快捷回复卡片激活名称
      systemActive: "person",//系统设置卡片激活名称
      fastReplyRules: {
        name: [
          {required: true, message: '请输入识别名称', trigger: 'blur'},
        ],
        content: [
          {required: true, message: '请输入消息内容', trigger: 'blur'}
        ],
      },
      //时间参数
      attentionDateOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      //用户关注来源参数
      attentionFromOptions: [{
        value: 'ADD_SCENE_SEARCH',
        label: '公众号搜索'
      }, {
        value: 'ADD_SCENE_ACCOUNT_MIGRATION',
        label: '公众号迁移'
      }, {
        value: 'ADD_SCENE_QR_CODE',
        label: '扫描二维码'
      }, {
        value: 'ADD_SCENE_PROFILE_CARD',
        label: '名片分享'
      }, {
        value: 'ADD_SCENEPROFILE_LINK ',
        label: '图文页内名称点击'
      }, {
        value: 'ADD_SCENE_PROFILE_ITEM',
        label: '图文页右上角菜单'
      }, {
        value: 'ADD_SCENE_PAID',
        label: '支付后关注'
      }, {
        value: 'ADD_SCENE_OTHERS',
        label: '其他'
      }
      ],
      // 用户是否关注参数
      isAttentionOptions: [{
        value: '0',
        label: '未关注'
      }, {
        value: '1',
        label: '已关注'
      }, {
        value: '2',
        label: '网页授权'
      }
      ],
      //用户黑名单参数
      blackListOptions: [{
        value: '0',
        label: '是黑名单用户'
      }, {
        value: '1',
        label: '非黑名单用户'
      }
      ],
      //是否存有手机号码参数
      phoneNumberOptions: [{
        value: '0',
        label: '有手机号码'
      }, {
        value: '1',
        label: '无手机号码'
      }
      ],
      // 行为节点
      behaviorNodeList: [{
        label: '热门城市',
        options: [{
          value: 'Shanghai',
          label: '上海'
        }, {
          value: 'Beijing',
          label: '北京'
        }]
      }, {
        label: '城市名',
        options: [{
          value: 'Chengdu',
          label: '成都'
        }, {
          value: 'Shenzhen',
          label: '深圳'
        }, {
          value: 'Guangzhou',
          label: '广州'
        }, {
          value: 'Dalian',
          label: '大连'
        }]
      }
      ],
    };
  },
  watch: {
    //快速回复的tab的watch监听
    fastReplyActive(val, oldVal) {
      if (val != oldVal) {
        if (val == "person") {
          this.getFastReplyList("1");
        } else if (val == "public") {
          this.getFastReplyList("2");
        }
      }
    },
    //快速回复的tab的watch监听
    systemActive(val, oldVal) {
      console.log("system", val)
      if (val != oldVal) {
        if (val == "person") {
          this.getFastReplyList("1");
        } else if (val == "public") {
          this.fastReplyParam.create_id = "";
          this.getFastReplyList("2");
        }
      }
    },
    rightMenuVisible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeRightMenu)
      } else {
        document.body.removeEventListener('click', this.closeRightMenu)
      }
    },
  },
  filters: {
    //格式化用户来源
    formatSubscribeScene: function (val) {
      if ("ADD_SCENE_SEARCH" === val) return '公众号搜索';
      if ("ADD_SCENE_ACCOUNT_MIGRATION" === val) return '公众号迁移';
      if ("ADD_SCENE_PROFILE_CARD" === val) return '名片分享';
      if ("ADD_SCENE_QR_CODE" === val) return '扫描二维码';
      if ("ADD_SCENEPROFILE LINK" === val) return '图文页内名称点击';
      if ("ADD_SCENE_PROFILE_ITEM" === val) return '图文页右上角菜单';
      if ("ADD_SCENE_PAID" === val) return '支付后关注';
      if ("ADD_SCENE_OTHERS" === val) return '其他';
      return "未知来源"
    },
    //格式化用户更新时间
    formatUserUpdateTime: function (val) {
      return val;
    },


  },
  created() {
    this.getWxAppList();
    // this.getFastReplyList();
  },
  mounted: function () {
    document.getElementById('userListScroll').addEventListener('scroll', this.handleUserListScroll, true)
  },
  beforeDestroy() {
    // 页面离开时断开连接,清除定时器
    this.disconnect();
    clearInterval(this.timer);
  },
  computed: {
    ...mapGetters(['permissions',"isCollapse"]),
    permissionList() {
      return {
        addBtn: this.permissions['wxmp:wxmassmsg:add'] ? true : false,
        delBtn: this.permissions['wxmp:wxmassmsg:del'] ? true : false,
        editBtn: this.permissions['wxmp:wxmassmsg:edit'] ? true : false,
        viewBtn: this.permissions['wxmp:wxmassmsg:get'] ? true : false
      };
    }
  },
  methods: {
    getWxAppList() {
      getWxAppList({
      }).then(res => {
        console.log("加载公众号", res)
        this.wxAppList = res.data;
        this.wxAppList.forEach(o => {
          this.userCondition.appIdList.push(o.id);
        })
        this.initUserPage();
      }).catch(e => {
        console.log(e)
      })
    },
    initWebSocket() {
      this.connection();
      //确保只有一个
      if(this.chatTimer){
        clearInterval(this.timer);
      }
      //断开重连机制,尝试发送消息,捕获异常发生时重连
      this.chatTimer = setInterval(() => {
        try {
          this.stompClient.send("心跳一下");
        } catch (err) {
          console.log("断线了: " + err);
          this.connection();
        }
      }, 5000);
    },
    connection() {
      let token = store.getters.access_token
      let headers = {
        'Authorization': 'Bearer ' + token
      }
      // 建立连接对象
      this.socket = new SockJS('/weixin/ws');//连接服务端提供的通信接口，连接以后才可以订阅广播消息和个人消息
      // 获取STOMP子协议的客户端对象
      this.stompClient = Stomp.over(this.socket);

      // 向服务器发起websocket连接
      this.stompClient.connect(headers, () => {
        console.log("向服务器发起websocket连接",headers)
        this.stompClient.subscribe('/weixin/wx_msg' + this.currentUser.id, (msg) => { // 订阅服务端提供的某个topic;
          console.log("查到的消息msg",msg)
          let data = JSON.parse(msg.body)
          this.msgList = [...this.msgList, ...[data]]//刷新消息
          this.scrollToBottom()
          //标记为已读
          if (data.type == '1') {
            putObj({
              id: data.id,
              readFlag: '0'
            }).then(data => {
            })
          }
        });
      }, (err) => {
      });
    },
    disconnect() {
      if (this.stompClient != null) {
        this.stompClient.disconnect();
        // console.log("Disconnected");
      }
      clearInterval(this.chatTimer);
    },
    //初始化搜索用户
    initUserPage() {
      this.userList = [];
      if (this.userCondition.appIdList) {
        let param = Object.assign({
          current: this.userPage.currentPage,
          size: this.userPage.pageSize,
          descs: this.userPage.descs,
          ascs: this.userPage.ascs,
          appType: '2',
        }, this.userCondition)
        console.log("初始化查询", (param));
        getPageAndCon(param).then(res => {
          console.log("y用户结果", res)
          this.userList = res.data.data.records
          this.userPage.total = res.data.data.total
          this.userPage.currentPage = res.data.data.current
          this.userPage.pageSize = res.data.data.size
          this.userPage.pages = res.data.data.pages
        }).catch(err => {
          console.log(err)
        })
      }
    },
    //用户框下滑
    handleUserListScroll() {
      var sh = this.$refs.userListScrollRef.wrap.scrollHeight // 滚动条高度
      var st = this.$refs.userListScrollRef.wrap.scrollTop // 滚动条距离顶部的距离
      var ch = this.$refs.userListScrollRef.wrap.clientHeight // 滚动条外容器的高度
      //.clientHeight - 滚动条外容器的高度
      //每条高123
      //.scrollHeight - 滚动条高度
      //到底了-业务逻辑
      if (st + ch >= sh) {
        console.log("当前页码", this.userPage.currentPage)
        if (this.userPage.currentPage < this.userPage.pages) {
          this.userPage.currentPage += 1;
          this.scrollPullUser();
        }
      }
    },
    //下拉添加用户
    scrollPullUser() {
      console.log("分页参数", this.userPage)
      getPageAndCon(Object.assign({
        current: this.userPage.currentPage,
        size: this.userPage.pageSize,
        descs: this.userPage.descs,
        ascs: this.userPage.ascs,
        appType: '2',
      }, this.userCondition)).then(res => {
        this.userList = this.userList.concat(res.data.data.records)
      }).catch(err => {
        console.log(err)
      })
    },
    //用户多条件搜索
    userSearch() {

      console.log("用户条件搜索", this.userCondition)
      getPageAndCon(Object.assign({
        current: 1,
        size: this.userPage.pageSize,
        descs: this.userPage.descs,
        ascs: this.userPage.ascs,
        appType: '2',
      }, this.userCondition)).then(res => {
        console.log("用户条件搜索res", res)
        this.userList = res.data.data.records;
      }).catch(err => {
        console.log(err)
      })
    },
    //用户多条件搜索
    resetSearch() {
      this.userCondition= {
        appIdList: [],//选中公众号查询
      };
      this.wxAppList.forEach(o => {
        this.userCondition.appIdList.push(o.id);
      })
      this.initUserPage();
    },
    //切换得到当前用户
    getCurrentUser(val) {
      if (val.id === this.currentUser.id) {
        return;
      }
      for (let i = 0; i < this.userList.length; i++) {
        this.userList[i].selectFlag = false;
      }
      val.selectFlag = true;
      console.log("当前用户", val)
      val.unReadNum = 0;
      for (let i = 0; i <this.wxAppList.length ; i++) {
        if(this.wxAppList[i].id == val.appId){
          this.currentWxApp = this.wxAppList[i];
        }
      }
      this.msgPage.currentPage = 1;
      this.currentUser = val;
      this.msgList = [];
      this.disconnect()
      this.initWebSocket()
      this.refreshChange();
      this.getUserTag();
      console.log("请求id",val)
      putUserAllReadFlag({
        appId:val.appId,
        wxUserId:val.id,
      }).then(res => {
        console.log("更新结果",res)
      })
    },
    openRightMenu(e, item) {
      this.rightClickItem = item;
      let x = e.pageX;
      let y = e.pageY;
      this.rightMenu.top = y - 100;
      this.rightMenu.left = x - 50;
      //判断导航栏大的伸缩 true缩小 false展开
      if(!this.isCollapse){
        this.rightMenu.left = this.rightMenu.left-220
      }
      this.rightMenuVisible = true;
    },
    //打开标签栏
    openUserTagBox() {
      if (!this.currentUser.id) {
        this.$message({
          showClose: true,
          message: '请先选择一名用户',
          type: 'warning'
        })
        return;
      }
      this.tagsAndTypeList = "";
      this.userTagBoxVisible = true;
      console.log("当前id",this.currentUser)
      let params ={
        appId:this.currentUser.appId,
      }
      getUserTagAndType(params).then(res => {
        this.tagsAndTypeList = res.data.data;
        //选中用户标签
        for (let i in this.someoneTagsList) {
          for (let j in this.tagsAndTypeList) {
            for (let k in this.tagsAndTypeList[j].tagList) {
              if (this.someoneTagsList[i].id == this.tagsAndTypeList[j].tagList[k].id) {
                this.tagsAndTypeList[j].tagList[k].checked = true;
              }
            }
          }
        }
      })
    },
    //加载用户标签
    getUserTag() {
      this.someoneTagsList = "";
      console.log("加载用户标签", this.currentUser.openId)
      getSomeoneTags({userId: this.currentUser.id, openId: this.currentUser.openId,appId: this.currentUser.appId}).then(res => {
        this.someoneTagsList = res.data.data;
      })
    },
    closeRightMenu() {
      this.rightMenuVisible = false;
    },
    addBackList() {
      addBackList(Object.assign({
        appId: this.rightClickItem.appId,
        openId: this.rightClickItem.openId
      })).then(res => {
        console.log("拉黑用户", res)
      }).catch();
    },
    putTop(obj) {
      console.log("置顶",obj)
      putTopFlag(obj.id).then(res => {
        console.log("res",res);
        this.$message({
          showClose: true,
          message: '修改置顶成功',
          type: 'success'
        })
        this.userPage={
          total: 0, // 总页数
          currentPage: 1, // 当前页数
          pageSize: 50, // 每页显示多少条
          ascs: [],
          descs: 'subscribe_time'
        },
        this.initUserPage();
      }).catch(() => {
      })
    },
    //拉取消息列表
    getMsgPage(page, params) {
      console.log("请求消息")
      if (this.currentUser) {
        this.tableLoading = true;
        this.loadMore = true;
        getMsgPage(Object.assign({
          current: this.msgPage.currentPage,
          size: this.msgPage.pageSize,
          descs: this.msgPage.descs,
          ascs: this.msgPage.ascs,
          appId: this.currentUser.appId,
          wxUserId: this.currentUser.id
        }, params, this.paramsSearch)).then(res => {
          console.log("res",res)
          this.tableLoading = false
          let msgDiv = document.getElementById('msg-div' + this.nowStr)
          let scrollHeight = 0
          if (msgDiv) {
            scrollHeight = msgDiv.scrollHeight
            console.log("定位了")
          }
          let data = res.data.data.records.reverse()
          this.msgList = [...data, ...this.msgList]
          this.msgPage.total = res.data.data.total
          if (this.msgPage.currentPage  == res.data.data.pages || res.data.data.total ==0) {
            this.loadMore = false
          }

          if (this.msgPage.currentPage == 1) {//定位到消息底部
            console.log("定位到消息底部")
            this.scrollToBottom()
          } else {
            console.log("定位滚动条")
            if (data.length != 0) {
              this.$nextTick(() => {//定位滚动条
                if (scrollHeight != 0) {
                  msgDiv.scrollTop = document.getElementById('msg-div' + this.nowStr).scrollHeight - scrollHeight - 100
                }
              })
            }
          }
          this.msgPage.currentPage = page.currentPage
          this.msgPage.pageSize = page.pageSize
        }).catch(() => {

        })
      }
    },
    //回车发送消息
    shiftEnter(e) {
      if (!e.shiftKey && e.keyCode == 13) {
        e.cancelBubble = true; //ie阻止冒泡行为
        e.stopPropagation();//Firefox阻止冒泡行为
        e.preventDefault(); //取消事件的默认动作*换行
        //以下处理发送消息代码
        // onSendMsg();
        console.log("发送的消息", this.msgData);
        this.sendMsg();
      }
    },
    //发送消息
    sendMsg(obj) {
      console.log(this.msgData,111)
      if (!this.currentUser.id) {
        this.$message({
          showClose: true,
          message: '请先选择一名用户',
          type: 'warning'
        })
        return;
      }
      if (this.msgData) {
        if (this.msgData.repType == 'news') {
          this.msgData.content.articles = [this.msgData.content.articles[0]]
          this.$message({
            showClose: true,
            message: '图文消息条数限制在1条以内，已默认发送第一条',
            type: 'success'
          })
        }
        this.sendLoading = true
        sendMsg(Object.assign({
          appId: this.currentUser.appId,
          wxUserId: this.currentUser.id
        }, this.msgData)).then(data => {
          this.sendLoading = false
          data = data.data.data
          this.msgList = [...this.msgList, ...[data]]
          this.scrollToBottom()
          this.msgData = {
            repType: 'text'
          }
        }).catch(() => {
          this.sendLoading = false
        })
      }
    },
    //滚动到底部
    scrollToBottom() {
      this.$nextTick(() => {
        let div = document.getElementById('msg-div' + this.nowStr)
        div.scrollTop = div.scrollHeight
      })
    },
    //拉取更多消息
    loadingMore() {
      this.msgPage.currentPage++
      this.getMsgPage(this.msgPage)
    },
    //刷新消息回调
    refreshChange() {
      this.getMsgPage(this.msgPage)
    },
    //打开系统配置
    openChatSystem() {
      this.chatSystemVisible = true;
    },
    //打开快捷回复框
    openFastReplyBox(fastReplyType, boxType, obj) {
      if (boxType == 'add') {
        this.fastReplyBox.title = '添加快捷回复';
        this.fastReplyBox.Type = 'add';
        this.fastReply = {};
      } else if (boxType == 'put') {
        this.fastReplyBox.title = '修改快捷回复';
        this.fastReplyBox.Type = 'put';
        this.fastReply = Object.assign({}, obj);
      }
      this.fastReply.type = fastReplyType;
      this.fastReplyBox.visible = true;
    },
    //确认快捷回复
    confirmFastReply() {
      if (this.fastReplyBox.Type === 'add') {
        this.$refs[this.fastReplyFrom].validate((valid) => {
          if (valid) {
            addFastReply(this.fastReply).then(res => {
              if (res.data.code === 0) {
                this.$message({
                  showClose: true,
                  message: '添加成功',
                  type: 'success'
                })
                this.fastReplyBox.visible = false;
                this.getFastReplyList(this.fastReplyParam.type);
              }
            }).catch(err => {
              console.log(err)
            });
          } else {
            return false;
          }
        });
      } else if (this.fastReplyBox.Type === 'put') {
        putFastReply(this.fastReply).then(res => {
          if (res.data.code === 0) {
            this.$message({
              showClose: true,
              message: '修改成功',
              type: 'success'
            })
            this.fastReplyBox.visible = false;
            this.getFastReplyList(this.fastReplyParam.type);
          }
        }).catch(err => {
          console.log(err)
        });
      }
    },
    //关闭快捷回复
    fastReplyBoxClose() {
      this.fastReplyBox.visible = false
      this.$refs[this.fastReplyFrom].resetFields();
    },
    //获得快捷回复
    getFastReplyList(val) {
      if (val) {
        this.fastReplyParam.type = val;
      }
      // console.log("获得快捷回复请求", this.fastReplyParam)
      getFastReplyPage(Object.assign(this.fastReplyPage, this.fastReplyParam)).then(res => {
        console.log("获得快捷回复请求", res)
        if (res.data.code === 0) {
          if (this.fastReplyParam.type === "1") {
            this.personFastReply = res.data.data.records;
          } else if (this.fastReplyParam.type === "2") {
            this.publicFastReply = res.data.data.records;
          }
        }
      }).catch(err => {
        console.log(err)
      });
    },
    //删除快捷回复
    delFastReply(id) {
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delFastReply(id).then(res => {
          if (res.data.code === 0) {
            this.$message({
              showClose: true,
              message: '删除成功',
              type: 'success'
            })
          }
          this.getFastReplyList();
        }).catch(res => {
          console.log(res);
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    //点击添加预发送消息
    addPreSendMessage(val) {
      if (!this.currentUser.id) {
        this.$message({
          showClose: true,
          message: '请先选择一名用户',
          type: 'warning'
        })
        return;
      }
      this.msgData.repContent = val;
    },
    personFastReplyRowClick(row, column, event, cell) {
      this.openFastReplyBox('1', 'put', row);
    },
    publicFastReplyRowClick(row, column, event, cell) {
      this.openFastReplyBox('2', 'put', row);
    },
    //关注时间
    subscribeTimeOnPick() {
      if (this.subscribeTimeList) {
        this.userCondition.subscribeTimeBegin = this.subscribeTimeList[0];
        this.userCondition.subscribeTimeEnd = this.subscribeTimeList[1];
      } else {
        this.userCondition.subscribeTimeBegin = "";
        this.userCondition.subscribeTimeEnd = "";
      }
    },
    //行为时间
    behaviorNodeTimeOnPick() {
      if (this.behaviorNodeTimeList) {
        this.userCondition.behaviorNodeTimeBegin = this.behaviorNodeTimeList[0];
        this.userCondition.behaviorNodeTimeEnd = this.behaviorNodeTimeList[1];
      } else {
        this.userCondition.behaviorNodeTimeBegin = "";
        this.userCondition.behaviorNodeTimeEnd = "";
      }
    },
    // 格式化公众号名称
    formatAppName(appId) {
      let list = this.wxAppList;
      for (let i in list) {
        if (list[i].id == appId)
          return list[i].name;
      }
      return ""
    },
    //检查用户所选标签
    checkUserTag(id) {
      for (let i in this.someoneTagsList) {
        if (id == this.someoneTagsList[i].id) {
          return true;
        }
      }
      return false;
    },
    //转化得到字体颜色
    getFontColor(val) {
      if (!val) {
        return;
      }
      return "color:" + val;
    },
    //编辑用户所选标签
    editTagLink(tag, index1, index2) {
      this.$set(this.tagsAndTypeList[index1].tagList[index2], "checked", !tag.checked);
      editTagLink({
        userId: this.currentUser.id,
        appId: this.currentUser.appId,
        openId: this.currentUser.openId,
        delFlag: !tag.checked ? "0" : "1",
        tagId: tag.id
      }).then(res => {
        this.$message.success("操作成功")
        this.getUserTag();
      })
    },
    openReplyBox(type){
      this.msgData.repType = type;
      this.replyBoxVisible = true;
    },
    closeReplyBox(){
      this.msgData = { //发送的消息
        repType: 'text',
        repContent: ''
      };
    },
    //基础信息tab点击事件
    baseMsgTabClick(tab){
      if(tab.name=="base"){

      }else if(tab.name=="friend"){
        this.$refs.wxUserFriend.refreshChange();
      }else if(tab.name=="system"){
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.userTagBox {
  height: 400px;
  overflow: scroll
}

.userTagBox_type {
  overflow: auto;
}

.userTagBox_tag {
  display: block;
  float: left;
  padding: 10px;
}

.user_base_info_form  {

  .el-form-item {
    margin-bottom: 0;
  }

}
.user_base_info_form input {
  width: 100px;
}

//此处是标签的高度
.user_base_info_form_tag {
  height: 20vh;
  overflow:auto
}

.all_chat_box {
  height: 100%;
  width: 100%;
}

.main_chat {
  float: left;
  width: 60vw;
  height: 80vh;
  border: 1px solid #EBEEF5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);
}

.main_chat_show_msg {
  height: 60vh;
  width: 100%;
  padding: 0px;
  border-top: 1px solid #EBEEF5;
  border-bottom: 1px solid #EBEEF5;
}


.main_chat_head_top {
  float: left;
  width: 40vw;
  font-size: 11px;

  p {
    display: inline;
    font-size: 11px;
  }
}

.main_chat_head {
  height: 5vh;
  padding: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);
}

.main_chat_head_div {
  height: 5vh;
  display: flex;
  align-items: center;
}

.main_chat_head_avatar {
  float: left;
  margin: 2px 15px 0px 5px;
}

.scrollbar_user {
  padding: 5px ;
  height: 80vh;
  //width: 260px;
  width: 20vw;
  border-left: 1px solid #EBEEF5;
  border-top: 1px solid #EBEEF5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);
}

.user_list_box {
  border-bottom: 1px solid #E2E2E2;
  display: block;
  padding: 5px 5px;
  margin: 5px 0;
  height: 110px;
}

.user_box_title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 20px;
}

.user_top{
  background: #eea9a9;
}

.user_box_title p {
  font-size: 10px;
  display: inline-block;
}

.user_box_title_appName {
  text-align: center;
  float: left;
}

.user_box_title_subscribeScene {
  float: right;
}

.user_box_title_tag {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user_box_main {
  height: 45px;
  padding-top: 8px;
  display: block;
}

.user_box_main_avatar {
  float: left;
}

.user_box_main_nickname {
  font-size: 16px;
}

.user_box_main_phone {
  font-size: 14px;
}

.user_box_foot {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}


.user_box_foot_updateTime {
  float: right;
}

.msg_content_author {
  display: inline;
  font-size: 12px;
  overflow: hidden;
}

.msg_event {
  display: flex; /**/
  width: 100%;
  margin: 2px 0;
  justify-content: center; /*水平居中*/
  align-items: Center; /*垂直居中*/
  font-size: 10px;
  box-shadow: 0 0px 0px #ccc;
  background-color: #eaeaea;
  div {
    white-space: nowrap;
    display: inline-block;
    p {
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}

.msg_content_main {
  width: auto;
  display: inline;
  background-color: #9eea6a;
  color: #333;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
}

.msg_content_main_create_time {
  text-align: left;
  font-size: 12px;
}

.msg_comment_main_body {
  text-align: center;
  padding: 10px;
  overflow: hidden;
  background: #fff;
  color: #333;
  font-size: 14px;
}

.comment_main {
  flex: unset !important;
  border-radius: 5px !important;
  margin: 0 8px !important;
}

.comment_header {
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}

.comment_create_time {
  font-size: 9px;
  color: #EBEEF5;
}

.comment_body {
  text-align: center;
  border-bottom-right-radius: 5px;
  border-bottom-left-radius: 5px;
  background-color: #9eea6a;
  border: 1px solid gray;
}

.avatar-div {
  text-align: center;
  width: 50px;
}

.main_chat_foot {
  display: block;
  padding: 0px;
  margin: 0px;
  height: 15vh;
}

.main_chat_foot_icon {
  height: 20%;
}
.main_chat_foot_icon button {
  margin: 0;
}

.left_container {
  padding: 0;
  height: 80vh;
  width: 20vw;
}
.send_msg_input{
  position: relative;
  height: 80%;
}

.send_msg_input /deep/ .el-textarea__inner {
  border: 0;
}
.msg_input{
  height:100%;
}.msg_input /deep/ textarea{
  height:100%;
}
.left_info_box {
  padding: 0px;
  float: left;
  width: 20vw;
  height: 40vh;
}

.left_fast_reply_box {
  float: left;
  height: 40vh;
  padding: 0px;
  width: 20vw;
  overflow: hidden;
}

.fast_reply_ul_div {
  padding: 0px;
  overflow-y: auto;
  overflow-x: hidden;
}

.fast_reply_ul {
  height: 40vh; //个人回复高度
  overflow-y: auto;
  /*overflow-x:hidden;*/
  /*overflow:auto*/
}

.fast_reply_ul li {
  text-align: center;
  /*overflow:auto*/
}


.fast_reply_ul_tag {
  display: inline-block;
  float: left;
  width: 25%;
  overflow: hidden;
}

.fast_reply_ul p {
  line-height: 100%;
  text-align: left;
  display: inline-block;
  width: 70%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.contextmenu {
  margin: 0;
  background: #fff;
  z-index: 3000;
  position: absolute;
  list-style-type: none;
  padding: 5px 0;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #333;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
}

.msg-div {
  height: 60vh;
  overflow: auto;
  background-color: #eaeaea;
}

.contextmenu li {
  margin: 0;
  padding: 7px 16px;
  cursor: pointer;
}
.contextmenu li:hover {
  background: #eee;
}
</style>

