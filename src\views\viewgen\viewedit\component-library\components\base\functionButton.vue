<template>
  <div class="functionButtonComponent"
       :style="{marginBottom: `${setData.pageMarginBottom}px`,marginTop: `${setData.pageMarginTop}px`}">
    <!--    {{ setData }}-->
    <div class="button_item">

      <div v-for="(item,index) in setData.buttonList" :key="index" :style="{width: `${item.width}%`}">
        <div v-if="item.showType==1"
        class="button_content"
        :style="{color: `${item.fontColor}`,
        fontSize: `${item.fontSize}px`,
        height: `${setData.height}px`,
        letterSpacing: `${item.fontSpacing}px`,
        fontWeight:`${item.fontWeight?'bold':'normal'}`,
        backgroundColor:`${item.backColor}`}">{{ item.title }}
        </div>
        <div v-if="item.showType==2"
             class="bg-img"
             :style="{'background-image':'url('+`${item.imgUrl}`+')',height: `${setData.height}px`}">
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';


export default {
  data() {
    return {};
  },
  components: {},
  props: {
    theme: {type: Object | Array},
    setData: {type: Object | Array},
    cId: {type: Number},
    noEditor: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {
  },
  mounted() {
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
  },
  watch: {
    setData(newVal, oldVal) {
    },
    componentsList(newVal, oldVal) {          //添加的时候出发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    }
  },
  beforeDestroy() {
    // this.$root.Bus.$off('addHotSpot')
  }
};
</script>
<style lang='less' scoped>

@import '../../colorui/main.css';
@import '../../colorui/icon.css';

.button_item {
  //display: flex;
  //justify-content: center;
  align-items: center;
  //text-align: center;
  display: flex;
  text-align: center;
  //垂直方向元素居中，两边留白
}

.button_content{
  display: flex;
  justify-content:center;
  align-items: center;
}

</style>
