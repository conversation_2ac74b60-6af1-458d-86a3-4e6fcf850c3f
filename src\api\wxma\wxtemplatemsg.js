import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/weixin/wxtemplatemsg/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/weixin/wxtemplatemsg',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/weixin/wxtemplatemsg/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/weixin/wxtemplatemsg/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/weixin/wxtemplatemsg',
        method: 'put',
        data: obj
    })
}

export function sendTemplateMsg(obj) {
  return request({
    url: '/weixin/wxtemplatemsg/send/mp/'+obj.appId,
    method: 'post',
    data: obj
  })
}
