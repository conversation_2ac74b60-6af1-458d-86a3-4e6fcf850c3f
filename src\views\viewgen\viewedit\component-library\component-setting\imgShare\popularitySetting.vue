<template>
  <div class="popularitySetting">
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">人气热度</p>
      <div slot="mainContent">
        <el-form ref="form" label-width="100px" :model="formData">
          <el-divider>基础属性</el-divider>
          <el-form-item label="上边界">
            <el-input v-model="formData.pageMarginTop" size="mini" :mini="0" type="number" style="margin-top: 5px"
                      placeholder="与上面元素的间隔">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="下边界">
            <el-input v-model="formData.pageMarginBottom" size="mini" :mini="0" type="number" style="margin-top: 5px"
                      placeholder="与下面元素的间隔">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="左边界">
            <el-input v-model="formData.pageMarginLeft" size="mini" :mini="0" type="number" style="margin-top: 5px"
                      placeholder="组件内部与左边界的间隔">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="右边界">
            <el-input v-model="formData.pageMarginRight" size="mini" :mini="0" type="number" style="margin-top: 5px"
                      placeholder="组件内部与右边界的间隔">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-divider>内容设置</el-divider>
          <el-form-item label="字体颜色">
            <el-input v-model="formData.fontColor" size="small" style="margin-top: 5px">
              <template slot="append">
                <el-color-picker size="mini" v-model="formData.fontColor"></el-color-picker>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="图标颜色">
            <el-input v-model="formData.iconColor" size="small" style="margin-top: 5px">
              <template slot="append">
                <el-color-picker size="mini" v-model="formData.iconColor"></el-color-picker>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="分割颜色">
            <el-input v-model="formData.borderColor" size="small" style="margin-top: 5px">
              <template slot="append">
                <el-color-picker size="mini" v-model="formData.borderColor"></el-color-picker>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="背景颜色">
            <el-input v-model="formData.backColor" size="small" style="margin-top: 5px">
              <template slot="append">
                <el-color-picker size="mini" v-model="formData.backColor"></el-color-picker>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="上圆角设置">
            <el-slider v-model="formData.topBorderRadius" :min="0" :max="40"></el-slider>
          </el-form-item>
          <el-form-item label="下圆角设置">
            <el-slider v-model="formData.bottomBorderRadius" :min="0" :max="40"></el-slider>
          </el-form-item>
        </el-form>
      </div>
    </settingSlot>
    <el-dialog
      title="内部数据"
      :visible.sync="interiorBoxVisible "
      :close-on-click-modal="false"
      center
      :append-to-body="true"
      lock-scroll
      width="80%">
      <div style="overflow: hidden">
        <interiorData :pageId="pageId" v-on:ensureInterior="ensureInterior" @backFun="ensureInterior"></interiorData>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';
import draggable from "vuedraggable";
import settingSlot from '../settingSlot'
import store from "@/store";
import interiorData from "@/components/interior-data/main";

export default {
  components: {settingSlot, draggable, interiorData},
  data() {
    return {
      formDataCopy: {
        pageMarginTop: 0,
        pageMarginBottom: 0,
        pageMarginLeft: 0,
        pageMarginRight: 0,
        fontColor: '#000000',
        iconColor: '#000000',
        borderColor: '#000000',
        backColor: '#FFFFFF',
        borderRadius: 0,
      },
      formData: {},
      interiorBoxVisible: false
    };
  },
  props: {
    thememobile: {type: Object | Array},
    showData: {
      type: Object,
      default: () => {
      }
    },
  },
  watch: {
    showData: {
      handler(newVal, oldVal) {
        this.formData = newVal ? newVal : this.formData;
      },
      deep: true
    },

  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
      clickComIndex: state => state.divpage.clickComIndex,
    })
  },
  created() {
    this.pageId = window.localStorage.getItem('viewEditId');
    console.log("滚动列表", this.pageId)
  },
  mounted() {
    let that = this;
    if (that.IsEmptyObj(that.showData)) {

      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
    } else {
      that.formData = that.showData
    }

    that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
    // that.updateData({
    //   componentsList: that.componentsList
    // })
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    // 删除按钮
    cancel() {
      this.$emit('cancel')
    },
    reset() {
      let that = this;
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      that.componentsList[that.clickComIndex]['data'] = this.formData;
    },
    confirm() {
      this.$emit('confirm', this.formData)
    },
    delete() {
      this.$emit('delete')
    },
    openInteriorBox() {
      this.interiorBoxVisible = true;
    },
    ensureInterior() {
    },
  },
};
</script>
<style lang='less' scoped>

.el-form-item {
  margin-bottom: 0;
}
</style>
