<template>
    <div class="goodsComponent" :style="{marginBottom: `${setData.pageSpacing}px`}">
      <div class="cu-bar justify-center" style="min-height: 40px;">
        <div class="action text-bold" :style="{color: `${setData.titleColor}`}" style="font-size: 13px;" >
          <div class="cuIcon-move"></div> <div style="margin: 0 5px;" :class="setData.titleIcon"></div>{{setData.title}}<div class="cuIcon-move"></div>
        </div>
      </div>
      <div style="margin-top: -10px;">
        <div  v-if="setData.showType=='row'" class="cu-card article no-card" style="padding:0 10px;margin-bottom: 10px;">
          <div class="cu-item goods-item solid-bottom" v-for="(item, index) in setData.goodsList" :key="index" style="padding-bottom: 10px;">
            <div >
              <div class="content">
                <img :src="item.picUrls[0] ? item.picUrls[0] : noPic" mode="aspectFill" class="row-img">
                <div class="desc block">
                  <div class="text-black margin-top-sm overflow-2">{{item.name}}</div>
                  <div class="text-gray text-sm margin-top-sm overflow-1">{{item.sellPoint}}</div>
                  <div class="flex margin-top-sm">
                    <div class="cu-tag bg-red radius sm" v-if="item.freightTemplat&&item.freightTemplat.type == '2'">包邮</div>
                    <div class="cu-tag bg-orange radius sm" v-if="item.pointsGiveSwitch == '1'">积分</div>
                    <div class="text-gray text-sm padding-lr-sm">已售{{item.saleNum}}</div>
                  </div>
                  <div class="flex justify-between margin-tb-sm">
                    <div class="text-price text-bold text-lg text-red">{{item.priceDown}}</div>
                    <div class="round buy text-sm " :class="'bg-red'"><div>立即购买</div></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else-if="setData.showType=='card'" class="goods-container flex">
          <div class="goods-box" v-for="(item, index) in setData.goodsList" :key="index">
            <div >
              <div class="img-box">
                <img :src="item.picUrls[0] ? item.picUrls[0] : noPic" mode="aspectFill"  class="card-img">
              </div>
              <div class="text-black margin-top-xs padding-lr-sm overflow-2">{{item.name}}</div>
              <div class="flex justify-between margin-top-sm align-center">
                <div class="text-price text-bold text-red text-lg padding-lr-sm">{{item.priceDown}}</div>
                <div class="cu-tag bg-red radius sm" v-if="item.freightTemplat&&item.freightTemplat.type == '2'">包邮</div>
                <div class="text-gray text-sm padding-lr-sm">已售{{item.saleNum}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>

<script>
import { mapState , mapGetters , mapMutations , mapActions } from 'vuex';
import placeholderImg from "../pages/page-components/placeholderImg";


export default {
    data() {
        return {
          noPic: require('../assets/images/icon/<EMAIL>')
        };
    },
    components: { placeholderImg },
    props: {
        theme : { type: Object | Array },
        setData : { type: Object | Array },
        cId     : { type: Number },
        noEditor: {
            type: Boolean,
            default: false,
        }
    },
    computed: {
        ...mapState({
            componentsList: state => state.divpage.componentsList,
        }),
    },
    created() {
    },
    mounted() {
    },
    methods: {
        ...mapMutations([
            'updateData'
        ]),
    },
    watch:{
        setData(newVal, oldVal){},
        componentsList(newVal, oldVal){          //添加的时候出发（解决没有移动的时候不emit数据）
            let that = this;
            that.updateData({componentsList: that.componentsList})
        }
    },
    beforeDestroy(){
        // this.$root.Bus.$off('addHotSpot')
    }
};
</script>
<style lang='less' scoped>

  @import '../colorui/main.css';
  @import '../colorui/icon.css';
  .goodsComponent {
    position: relative;
    display: block;
    width: 100%;

    .goods-item{
      margin: auto !important;
      margin-top: 10px !important;
    }

    .row-img {
      margin-top: 13px;
      margin-left: -17px;
      margin-right: 10px;
      width: 120px !important;
      height: 120px !important;
      border-radius: 5px
    }
    .card-img {
      width: 100% !important;
      height: 100% !important;
      border-radius: 5px
    }

    .buy{
      padding: 3px 10px 5px 10px;
    }

    .goods-container {
      justify-content: space-between;
      flex-wrap: wrap;
      box-sizing: content-box;
      padding: 10px;
    }

    .goods-box {
      width: 170px;
      height: 265px;
      background-color: #fff;
      overflow: hidden;
      margin-bottom: 10px;
      border-radius: 5px;
      box-shadow:0px 10px 10px #e5e5e5;
    }

    .goods-box .img-box {
      width: 100%;
      height: 175px;
      overflow: hidden;
    }

    .goods-box .img-box image {
      width: 100%;
      height: 265px;
    }
  }

  .overflow-1 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
  .overflow-2 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
</style>
