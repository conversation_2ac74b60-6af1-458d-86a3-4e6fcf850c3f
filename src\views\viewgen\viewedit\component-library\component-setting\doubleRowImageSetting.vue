<template>
  <div class="compSetting">
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">双列图片</p>
      <div slot="hint">
      </div>
      <div slot="mainContent">
        <el-form ref="form"  label-width="100px" :model="formData">
          <el-divider>基础属性</el-divider>
          <el-form-item label="组件上边距">
            <el-input-number v-model="formData.pagePaddingTop" size="mini"  style="margin-top: 5px" placeholder="组件上边距">
            </el-input-number> px
          </el-form-item>
          <el-form-item label="组件下边距">
            <el-input-number v-model="formData.pagePaddingBottom" size="mini" :min="0" style="margin-top: 5px" placeholder="组件下边距">
            </el-input-number> px
          </el-form-item>
          <el-form-item label="组件左边距">
            <el-input-number v-model="formData.pagePaddingLeft" size="mini" :min="0" style="margin-top: 5px" placeholder="组件下边距">
            </el-input-number> px
          </el-form-item>
          <el-form-item label="组件右边距">
            <el-input-number v-model="formData.pagePaddingRight" size="mini" :min="0" style="margin-top: 5px" placeholder="组件下边距">
            </el-input-number> px
          </el-form-item>
          <el-form-item label="图片间隔">
            <el-input-number v-model="formData.interval" size="mini" :min="0" style="margin-top: 5px" placeholder="组件下边距">
            </el-input-number> px
          </el-form-item>
          <el-divider>左图</el-divider>
          <el-form-item label="圆角设置">
            <el-slider v-model="formData.firstBorderRadius" :max="40"></el-slider>
          </el-form-item>
          <el-form-item label="图片">
            <MaterialList :value="formData.firstImageUrl?[formData.firstImageUrl]:[]"  @sureSuccess="formData.firstImageUrl = $event?$event[0]:''" @deleteMaterial="formData.firstImageUrl = ''"
                          type="image"  :divStyle="'width:100%;margin-bottom:0px;height:90px;line-height: 90px;'" :num=1 ></MaterialList>
          </el-form-item>
          <el-form-item label="跳转链接" >
            <wx-page-select :isSystemUrl="formData.firstIsSystemUrl" @switchChange="formData.firstIsSystemUrl=$event" :page="formData.firstPageUrl" @change="formData.firstPageUrl=$event"></wx-page-select>
          </el-form-item>
          <el-divider>右图</el-divider>
          <el-form-item label="圆角设置">
            <el-slider v-model="formData.secondBorderRadius" :max="40"></el-slider>
          </el-form-item>
          <el-form-item label="图片">
            <MaterialList :value="formData.secondImageUrl?[formData.secondImageUrl]:[]"  @sureSuccess="formData.secondImageUrl = $event?$event[0]:''" @deleteMaterial="formData.secondImageUrl = ''"
                          type="image"  :divStyle="'width:100%;margin-bottom:0px;height:90px;line-height: 90px;'" :num=1 ></MaterialList>
          </el-form-item>
          <el-form-item label="跳转链接" >
            <wx-page-select :isSystemUrl="formData.secondIsSystemUrl" @switchChange="formData.secondIsSystemUrl=$event"   :page="formData.secondPageUrl" @change="formData.secondPageUrl=$event"></wx-page-select>
          </el-form-item>
        </el-form>
      </div>
    </settingSlot>
    <!--    <p style="display:none">{{getData}}</p>-->
  </div>
</template>

<script>

  import { mapState , mapGetters , mapMutations , mapActions } from 'vuex';

  import settingSlot from './settingSlot'
  import MaterialList from '@/components/material/wxlist.vue'
  import WxPageSelect from '@/components/wx-page-select/Index.vue'

  export default {
    components: { settingSlot, MaterialList, WxPageSelect  },
    data() {
      return {
        formDataCopy : {
          pagePaddingTop:0,
          pagePaddingBottom:0,
          pagePaddingLeft:0,
          pagePaddingRight:0,
          interval:0,
          firstBorderRadius:0,
          firstImageUrl: '',
          firstPageUrl: '',
          firstIsSystemUrl: true,
          secondBorderRadius:0,
          secondImageUrl: '',
          secondPageUrl: '',
          secondIsSystemUrl: true
        },
        formData : {}
      };
    },
    props: {
      clientType: [String],
      showData:{
        type: Object,
        default: ()=> {}
      },
      config   : {
        type: Object,
        default: ()=> {}
      }
    },
    computed: {
      ...mapState({
        componentsList: state => state.divpage.componentsList,
        clickComIndex : state => state.divpage.clickComIndex,
      })
    },
    mounted(){
      let that = this;
      if(that.IsEmptyObj(that.showData)){
        that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      } else {
        that.formData = that.showData
      }
      that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
      // that.updateData({
      //   componentsList: that.componentsList
      // })
    },
    methods: {
      ...mapMutations([
        'updateData'
      ]),
      // 删除按钮
      delBtn(index){
        let that = this;
        that.$confirm('是否删除该按钮?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText : '取消',
          type : 'warning'
        }).then(()=>{
          that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
          that.updateData({ componentsList: that.componentsList });
        }).catch(()=>{})
      },
      cancel(){
        this.$emit('cancel')
      },
      reset(){
        let that = this;
        that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
        that.componentsList[that.clickComIndex]['data'] = this.formData;
      },
      confirm(){
        this.$emit('confirm', this.formData)
      },
      delete(){
        this.$emit('delete')
      }
    },
    watch: {
      showData: {
        handler(newVal, oldVal) {
          this.formData = newVal ? newVal : this.formData;
        },
        deep: true
      },
      clientType(){}
    }
  };
</script>
<style lang='less' scoped>
  .compSetting{
    /deep/ .el-form .el-form-item{
      //margin-bottom: 8px;
    }

  }
  .el-form-item{
    margin-bottom: 0;
  }
</style>
