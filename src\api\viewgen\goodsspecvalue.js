import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/wxgoodsspecvalue/page',
    method: 'get',
    params: query
  })
}

export function getList(query) {
  return request({
    url: '/weixin/wxgoodsspecvalue/list',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/weixin/wxgoodsspecvalue',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/weixin/wxgoodsspecvalue/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/wxgoodsspecvalue/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/wxgoodsspecvalue',
    method: 'put',
    data: obj
  })
}
