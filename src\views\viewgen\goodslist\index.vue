<template>
  <div class="execution">
    <basic-container>
      <el-form label-width="100px" class="search_form" :model="searchForm">
        <el-row type="flex" justify="start">
<!--          <el-form-item label="公众号:">
            <el-select size="small" v-model="selectedAppId" placeholder="请选择公众号" @change="getTagAndTypeList">
              <el-option
                v-for="item in wxAppList"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>-->
          <el-form-item label="标签筛选:">
            <el-cascader
              size="small"
              v-model="searchTagId"
              :options="cascaderOptions"
              :props="{
                checkStrictly: false,
                expandTrigger: 'hover',
                value: 'id',
                label: 'name',
                children: 'children',
                emitPath: false,
                showAllLevels: true,
                multiple: true
              }"
              placeholder="请选择标签"
              clearable>
            </el-cascader>
          </el-form-item>
          <el-form-item label="上架状态:">
            <el-select size="small" v-model="searchShelfFlag" placeholder="请选择" clearable>
              <el-option label="上架" value="0"></el-option>
              <el-option label="下架" value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button size="small" type="primary" @click="searchGoods">确认搜索</el-button>
            <el-button size="small" @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-row>
      </el-form>

      <avue-crud ref="crud"
                 :page="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 @selection-change="handleSelectionChange"
                 v-model="form"
                 @on-load="getPage"
                 @refresh-change="refreshChange"
                 @row-update="handleUpdate"
                 @row-del="handleDel"
                 @sort-change="sortChange"
                 @search-change="searchChange">
        <template slot-scope="scope" slot="menuLeft">
          <!-- 非选择模式下显示新增和批量排序按钮 -->
            <el-button type="primary"
                      icon="el-icon-plus"
                      size="small"
                      @click.stop="openGoodsBox('add')">新增
            </el-button>
            <template v-if="!selectMode">
            <el-button
              size="small"
              type="primary"
              icon="el-icon-sort"
              @click="startBatchSort">批量排序
            </el-button>
          </template>

          <!-- 选择模式下显示排序相关操作UI元素 -->
          <template v-else>
            <el-input
              v-model="batchSortValue"
              size="small"
              placeholder="请输入排序值"
              style="width: 150px; margin-right: 10px;"
              type="number">
            </el-input>
            <el-button size="small" type="primary" @click="confirmBatchSort">确认顺序</el-button>
            <el-button size="small" @click="cancelBatchSort">取消</el-button>
            <el-button size="small" type="success" @click="submitBatchSort">提交</el-button>
          </template>
        </template>
        <template slot-scope="scope" slot="menu">
          <el-button icon="el-icon-edit" type="text" size="small" @click="openGoodsBox('put',scope.row)">编辑</el-button>
          <el-button icon="el-icon-delete" type="text" size="small" @click="handleDel(scope.row)">删除</el-button>
        </template>
        <template slot="coverUrls" slot-scope="scope">
          <el-image
            style="width: 80px; height: 80px"
            :src="JSON.parse(scope.row.coverUrls)[1].url"
            :preview-src-list="[JSON.parse(scope.row.coverUrls)[1].url]"
            fit="cover">
          </el-image>
        </template>
        <template slot="sortValue" slot-scope="scope">
          <el-tag effect="plain" @click="editSortValue">
            {{ scope.row.sortValue }}   <i class="el-icon-edit"></i>
          </el-tag>
        </template>
        <template slot="shelfFlag" slot-scope="scope">
          <el-switch
            active-value="0"
            inactive-value="1"
            v-model="scope.row.shelfFlag"
            active-color="#13ce66"
            inactive-color="#ff4949"
            @change="changeShelf(scope.row)">
          </el-switch>
        </template>
      </avue-crud>
    </basic-container>

    <!--      提交、修改弹出框-->
    <el-dialog
      :title="goodsForm.title"
      :visible.sync="goodsBoxVisible"
      :close-on-click-modal="false"
      width="60%">
      <el-form :rules="goodsRules" :ref="goodsForm" :model="goodsForm" label-width="80px" :destroy-on-close="true">
        <el-form-item label="作品名称" prop="name">
          <el-input v-model="goodsForm.name" :maxlength="25" style="width:450px" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="是否上架">
          <el-switch
            v-model="goodsForm.shelfFlag"
            active-color="#13ce66"
            active-value="0"
            inactive-value="1"
            active-text="上架"
            inactive-text="下架">
          </el-switch>
        </el-form-item>
        <el-form-item label="上架时间">
          <el-row>
            <el-col :span="6">
              <el-switch
                v-model="goodsForm.shelfSwitch"
                active-color="#13ce66"
                inactive-color="#409eff"
                active-text="定期上架"
                inactive-text="长期上架">
              </el-switch>
            </el-col>
            <el-col :span="6">
              <el-date-picker
                v-show="goodsForm.shelfSwitch"
                v-model="goodsForm.shelfTime"
                type="datetimerange"
                align="right"
                unlink-panels
                value-format="yyyy-MM-dd hh:mm:ss"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="pickerOptions">
              </el-date-picker>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="拍摄时间">
          <el-row>
            <el-col :span="6">
              <el-switch
                v-model="goodsForm.useSwitch"
                active-color="#13ce66"
                inactive-color="#409eff"
                active-text="定期拍摄"
                inactive-text="长期拍摄">
              </el-switch>
            </el-col>
            <el-col :span="6">
              <el-date-picker
                v-show="goodsForm.useSwitch"
                v-model="goodsForm.useTime"
                type="datetimerange"
                align="right"
                unlink-panels
                value-format="yyyy-MM-dd hh:mm:ss"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="pickerOptions">
              </el-date-picker>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="作品标签">
          <div @click="openTagBox">
            <el-tag class="goods_form_tag" v-for="(tag,index) in goodsForm.tagList" :key="index" size="medium"
                    :color="tag.backColor"
                    :style="getFontColor(tag.fontColor)">{{ tag.name }}
            </el-tag>
            <el-button icon="el-icon-plus" size="mini"></el-button>
          </div>
        </el-form-item>
        <el-form-item label="封面图片（宽:高)">
          <div class="image_preview" v-for="(item,index) in goodsForm.coverImgList" :key="'coverImg'+index">
            <el-image
              v-show="item.url"
              class="image_preview_image"
              fit="fill"
              :src="item.url"
              :preview-src-list="goodsForm.preCoverImgList">
            </el-image>
            <el-upload
              :disabled="item.uploadDisabled"
              :file-list="item.uploadList"
              :show-file-list="false"
              v-show="!item.url"
              :action="serverUrl"
              :headers="header"
              :before-upload="(file)=>{return beforeUpload(file,item)}"
              accept=".jpg,.jpeg,.png,.bmp,.JPG,.JPEG,.PNG,.BMP"
              :on-success="(res,file,fileList)=>{return uploadSuccess(res,file,fileList,'cover',item)}"
              :on-error="(res)=>{return uploadError(res,item)}"
              :on-change="(file,fileList)=>{return handleUploadChange(file,fileList,item)}"
              :limit="1">
              <el-image class="waite_upload_img">
                <div style="margin-top:32%" slot="error">
                  <i v-show="item.uploadIconVisible" class="el-icon-upload2">点击上传</i>
                  <el-progress v-show="item.processIconVisible" type="circle" :width="55"
                               :percentage="item.percentage"></el-progress>
                </div>
              </el-image>
            </el-upload>
            <div v-show="item.url" class="img_dialog">
                <span
                  v-if="true"
                  class=""
                  @click="handleRemove('cover',item,index)">
                  <i class="el-icon-delete"></i>
                </span>
            </div>
            <p style="text-align: center; line-height: normal">{{ item.size }}</p>
          </div>
        </el-form-item>
        <el-form-item label="分享图片">
          <div class="image_preview">
            <el-image
              v-show="goodsForm.shareImg.url"
              class="image_preview_image"
              fit="fill"
              :src="goodsForm.shareImg.url"
              :preview-src-list="[goodsForm.shareImg.url]">
            </el-image>
            <el-upload
              :disabled="goodsForm.shareImg.uploadDisabled"
              :file-list="goodsForm.shareImg.uploadList"
              :show-file-list="false"
              v-show="!goodsForm.shareImg.url"
              :action="serverUrl"
              :headers="header"
              :before-upload="(file)=>{return beforeUpload(file,goodsForm.shareImg)}"
              accept=".jpg,.jpeg,.png,.bmp,.JPG,.JPEG,.PNG,.BMP"
              :on-success="(res,file,fileList)=>{return uploadSuccess(res,file,fileList,'share',goodsForm.shareImg)}"
              :on-error="(res)=>{return uploadError(res,goodsForm.shareImg)}"
              :on-change="(file,fileList)=>{return handleUploadChange(file,fileList,goodsForm.shareImg)}"
              :limit="1">
              <el-image class="waite_upload_img">
                <div style="margin-top:32%" slot="error">
                  <i v-show="goodsForm.shareImg.uploadIconVisible" class="el-icon-upload2">点击上传</i>
                  <el-progress v-show="goodsForm.shareImg.processIconVisible" type="circle" :width="55"
                               :percentage="goodsForm.shareImg.percentage"></el-progress>
                </div>
              </el-image>
            </el-upload>
            <div v-show="goodsForm.shareImg.url" class="img_dialog">
                <span
                  @click="handleRemove('share',goodsForm.shareImg,null)">
                  <i class="el-icon-delete"></i>
                </span>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="排序号" prop="orderNum">
          <el-input-number v-model="goodsForm.orderNum" :min="0" :max="999999"  :step="20" style="width:200px"></el-input-number>
          <span class="form-tip">数字越小越靠前</span>
        </el-form-item>
        <el-form-item label="内容图片">
          <draggable v-model="goodsForm.contentImgList" @start="datadragStart" @update="datadragUpdate"
                     @end="datadragEnd"
                     :disabled="false" :move="datadragMove" :options="{animation:500}">
            <transition-group>
              <div class="image_preview" v-for="(item,index) in goodsForm.contentImgList"
                   :key="'contentImg'+index">
                <el-image
                  class="image_preview_image"
                  fit="fill"
                  :src="item.url"
                  :preview-src-list="goodsForm.preContentImgList">
                </el-image>
                <div class="img_dialog">
                    <span
                      @click="handleRemove('content',null,index)">
                    <i class="el-icon-delete"></i>
                  </span>
                </div>
              </div>
            </transition-group>
          </draggable>
          <el-upload
            :file-list="goodsForm.uploadContent.list"
            :show-file-list="false"
            :action="serverUrl"
            :headers="header"
            :before-upload="(file)=>{return beforeUpload(file)}"
            accept=".jpg,.jpeg,.png,.bmp,.JPG,.JPEG,.PNG,.BMP"
            :on-success="(res,file,fileList)=>{return uploadSuccess(res,file,fileList,'content',goodsForm.uploadContent)}"
            :on-change="(file,fileList)=>{return handleUploadChange(file,fileList,goodsForm.uploadContent)}"
            multiple>
            <el-image class="waite_upload_img">
              <div style="margin-top:32%" slot="error">
                <i v-show="goodsForm.uploadContent.uploadIconVisible" class="el-icon-upload2">点击上传</i>
                <el-progress v-show="goodsForm.uploadContent.processIconVisible" type="circle" :width="55"
                             :percentage="goodsForm.uploadContent.percentage"></el-progress>
              </div>
            </el-image>
          </el-upload>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="goodsSubmit">{{ goodsForm.submitButton }}</el-button>
          <el-button @click="goodsBoxVisible = false ">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 商品标签栏 goodsTagBoxVisible-->
    <el-dialog
      :append-to-body="false"
      title="作品标签"
      :visible.sync="goodsTagBoxVisible"
      width="80%"
      center>
      <goods-tag-select ref="goodsTagSelect" :selectTagList="selectTagList"></goods-tag-select>
    </el-dialog>
  </div>
</template>

<script>
import {getPage, getObj, addObj as addGoods, putObj as putGoods, putShelf, delObj} from '@/api/viewgen/wxgoods'
import {tableOption} from '@/const/crud/viewgen/wxgoods.js'
import {mapGetters} from 'vuex'
import draggable from "vuedraggable";
import store from "@/store";
import {getSomeoneTags} from "@/api/viewgen/wxgoodstaglink";
import goodsTagSelect from "@/views/viewgen/goodslist/goodsTagSelect";
import {getTagAndType} from "@/api/viewgen/wxgoodstag";
import {getList as getWxAppList} from "@/api/wxmp/wxapp";
import request from '@/router/axios'

// 新增根据标签ID获取商品列表接口
function getPageByTagId(query) {
  // 将数组转换为逗号分隔的字符串，如果传入的是数组
  if (query.tagId && Array.isArray(query.tagId)) {
    query.tagId = query.tagId.join(',');
  }
  
  return request({
    url: '/weixin/wxgoods/pageByTagId',
    method: 'get',
    params: query
  })
}

// 新增批量更新排序值接口
function batchUpdateOrderNum(data) {
  return request({
    url: '/weixin/wxgoods/batchOrderNum',
    method: 'put',
    data: data
  })
}

export default {
  name: 'wxgoods',
  components: {
    draggable,
    goodsTagSelect,
  },
  data() {
    return {
      searchForm: {},
      selectedAppId: '', // 选中的公众号ID
      wxAppList: [], // 公众号列表
      cascaderOptions: [], // 级联选择器的选项
      searchTagId: [], // 搜索用的标签ID (改为数组以支持多选)
      searchShelfFlag: null, // 搜索用的上架状态
      allTagsList: [], // 所有可用标签
      selectTagList: [],//选中的标签
      goodsTagBoxVisible: false,//标签分类表
      serverUrl: '/upms/file/upload?fileType=image&dir=weixin/goods/', // 这里写你要上传的图片服务器地址
      header: {Authorization: 'Bearer ' + store.getters.access_token}, // 有的图片服务器要求请求头需要有token
      goodsBoxVisible: false,
      imagePreviewUrl: '',
      form: {},
      // 添加多选和排序相关的数据
      selectMode: false, // 是否处于批量排序模式
      batchSortValue: 0, // 批量排序的值
      selectedRows: [], // 已选中的行
      originalSortValues: [], // 保存原始排序值，用于取消操作
      goodsForm: {//真正的提交表单
        title: "新建作品",
        submitButton: "立即创建",
        name: '',
        shelfFlag: '0',
        shelfTime: [],//上架时间选择
        shelfSwitch: false,// true定期上架 false长期上架
        useTime: [],//使用时间选择
        useSwitch: false,// true定期上架 false长期上架
        orderNum: 0, // 新增排序字段
        coverImgList: [//封面图片
          {index: 0, sizeType: 1, size: '比例一', url: '', uploadIconVisible: true, uploadList: []},
          {index: 1, sizeType: 2, size: '比例二', url: '', uploadIconVisible: true, uploadList: []},
          {index: 2, sizeType: 3, size: '比例三', url: '', uploadIconVisible: true, uploadList: []},
          {index: 3, sizeType: 4, size: '比例四', url: '', uploadIconVisible: true, uploadList: []}
        ],
        preCoverImgList: [],//预览封面图片list
        shareImg: {
          url: '',
          uploadIconVisible: true,
          uploadList: []
        },
        contentImgList: [],//内容图片
        preContentImgList: [],//预览内容图片list
        uploadContent: {
          list: [],
          uploadIconVisible: true,
          processIconVisible: false,
        },//上传按钮没有绑定图片 所以单独拿出来
        tagList: [],//标签列表
      },
      defaultGoodsForm: {
        name: '',
        shelfFlag: '0',
        shelfTime: [],//上架时间选择
        shelfSwitch: false,// true定期上架 false长期上架
        useTime: [],//使用时间选择
        useSwitch: false,// true定期上架 false长期上架
        orderNum: 0, // 默认排序号为0
        coverImgList: [//封面图片
          {index: 0, sizeType: 1, size: '比例一', url: '', uploadIconVisible: true, uploadList: []},
          {index: 1, sizeType: 2, size: '比例二', url: '', uploadIconVisible: true, uploadList: []},
          {index: 2, sizeType: 3, size: '比例三', url: '', uploadIconVisible: true, uploadList: []},
          {index: 3, sizeType: 4, size: '比例四', url: '', uploadIconVisible: true, uploadList: []}
        ],
        preCoverImgList: [],//预览封面图片list
        shareImg: {
          url: '',
          uploadIconVisible: true,
          uploadList: []
        },
        contentImgList: [],//内容图片
        preContentImgList: [],//预览内容图片list
        uploadContent: {
          list: [],
          uploadIconVisible: true,
          processIconVisible: false,
        },//上传按钮没有绑定图片 所以单独拿出来
        tagList: [],//标签列表
      },
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      goodsRules: {
        name: [
          {required: true, message: '请输入作品名称', trigger: 'blur'},
          {max: 25, message: '长度在25个字符内', trigger: 'blur'}
        ],
      },
    }
  },
  created() {
    this.initTagData();
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.permissions['weixin:wxgoods:add'] ? true : false,
        delBtn: this.permissions['weixin:wxgoods:del'] ? true : false,
        editBtn: this.permissions['weixin:wxgoods:edit'] ? true : false,
        viewBtn: this.permissions['weixin:wxgoods:get'] ? true : false
      };
    }
  },
  methods: {
    // 初始化标签数据
    initTagData() {
      // 获取所有公众号
      getWxAppList({
        appType: '2'
      }).then(res => {
        this.wxAppList = res.data;
        // 获取所有公众号的标签
        this.getAllAppTags();
      }).catch(() => {});
    },

    // 获取所有公众号的标签
    getAllAppTags() {
      const promises = [];
      const appData = {};

      // 为每个公众号创建一个获取标签的Promise
      this.wxAppList.forEach(app => {
        const promise = getTagAndType({appId: app.id}).then(res => {
          appData[app.id] = {
            appInfo: app,
            tagsData: res.data.data
          };
        });
        promises.push(promise);
      });

      // 所有Promise完成后处理数据
      Promise.all(promises).then(() => {
        // 生成级联选择器所需数据格式
        this.generateCascaderOptions(appData);
      }).catch(err => {
        console.error('获取标签数据失败:', err);
      });
    },

    // 生成级联选择器数据
    generateCascaderOptions(appData) {
      const options = [];

      Object.keys(appData).forEach(appId => {
        const app = appData[appId].appInfo;
        const tagsData = appData[appId].tagsData;

        // 创建公众号级别
        const appOption = {
          id: app.id,
          name: app.name,
          children: []
        };

        // 添加标签分类级别
        if (tagsData && tagsData.length > 0) {
          tagsData.forEach(typeItem => {
            const typeOption = {
              id: typeItem.tagTypeId,
              name: typeItem.tagTypeName,
              children: []
            };

            // 添加标签级别
            if (typeItem.tagList && typeItem.tagList.length > 0) {
              typeItem.tagList.forEach(tag => {
                typeOption.children.push({
                  id: tag.id,
                  name: tag.name,
                  backColor: tag.backColor,
                  fontColor: tag.fontColor
                });
              });

              // 只有标签分类下有标签时才添加此分类
              if (typeOption.children.length > 0) {
                appOption.children.push(typeOption);
              }
            }
          });

          // 只有公众号下有标签分类时才添加此公众号
          if (appOption.children.length > 0) {
            options.push(appOption);
          }
        }
      });
      console.log(JSON.stringify(options))
      this.cascaderOptions = options;
    },

    // 根据标签搜索商品
    searchGoods() {
      this.page.currentPage = 1;
      this.getPage(this.page);
    },

    // 重置搜索条件
    resetSearch() {
      this.searchTagId = [];
      this.searchShelfFlag = null;
      this.page.currentPage = 1;
      this.getPage(this.page);
    },

    searchChange(params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },

    sortChange(val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },

    getPage(page, params) {
      this.tableLoading = true

      // 使用标签ID查询
      if (this.searchTagId && this.searchTagId.length > 0) {
        getPageByTagId(Object.assign({
          current: page.currentPage,
          size: page.pageSize,
          descs: this.page.descs,
          ascs: this.page.ascs,
          tagId: this.searchTagId, // 已经是数组，支持多个标签ID
          shelfFlag: this.searchShelfFlag || null
        }, params)).then(response => {
          console.log("根据标签查询结果:", response)
          this.tableData = response.data.data.records
          this.page.total = response.data.data.total
          this.page.currentPage = page.currentPage
          this.page.pageSize = page.pageSize
          this.tableLoading = false
        }).catch(() => {
          this.tableLoading = false
        })
      }
      // 常规查询（未指定标签）
      else {
        getPage(Object.assign({
          current: page.currentPage,
          size: page.pageSize,
          descs: this.page.descs,
          ascs: this.page.ascs,
          shelfFlag: this.searchShelfFlag || null
        }, params, this.paramsSearch)).then(response => {
          console.log("常规查询结果:", response)
          this.tableData = response.data.data.records
          this.page.total = response.data.data.total
          this.page.currentPage = page.currentPage
          this.page.pageSize = page.pageSize
          this.tableLoading = false
        }).catch(() => {
          this.tableLoading = false
        })
      }
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this
      this.$confirm('是否确认删除此数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        return delObj(row.id)
      }).then(data => {
        _this.$message({
          showClose: true,
          message: '删除成功',
          type: 'success'
        })
        this.getPage(this.page)
      }).catch(function (err) {
      })
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page)
    },
    // 拖动的事件等等=======================================================>
    datadragStart(e) {
      let that = this;
      // that.updateData({clickComIndex: null})
      that.showSetBlock = false;
    },
    datadragUpdate(e) {
      e.preventDefault();
    },
    datadragEnd(e) {
      // console.log(e, "拖动结束");
    },
    datadragMove(e, originalEve) {
      return (e.draggedContext.element.text !== 'Gold（不可拖动元素）');
    },
    //====================================================================>
    //打开标签栏
    openTagBox() {
      this.selectTagList = this.goodsForm.tagList
      this.goodsTagBoxVisible = true;
    },
    getFontColor(val) {
      if (!val) {
        return;
      }
      return "color:" + val;
    },
    // 图片上传start =======================================================>
    handleRemove(type, item, index) {
      if (type == "cover" || type == "share") {
        item.uploadList = [];
        item.uploadIconVisible = true;
        item.processIconVisible = false;
        item.uploadDisabled = false;
      }
      if (type == "cover") {
        console.log(this.goodsForm.preCoverImgList)
        let del;
        for (let i = 0; i < this.goodsForm.preCoverImgList.length; i++) {
          if (this.goodsForm.preCoverImgList[i] == item.url) {
            this.goodsForm.preCoverImgList.splice(i, 1)
            break;
          }
        }
        item.url = '';
      }
      if (type == "share") {
        item.url = '';
      }
      if (type == "content") {
        this.goodsForm.contentImgList.splice(index, 1);
        this.goodsForm.preContentImgList.splice(index, 1)
      }
    },
    uploadSuccess(res, file, fileList, type, item) {
      if (type == 'cover') {
        item.url = res.link;
        this.goodsForm.preCoverImgList.push(res.link);
      }
      if (type == 'share') {
        item.url = res.link;
      }
      if (type == 'content') {
        this.goodsForm.contentImgList.push({url: res.link});
        this.goodsForm.preContentImgList.push(res.link);
      }
      this.$message.success("上传成功")
    },
    uploadError(res, item) {
      item.uploadIconVisible = true;
      item.processIconVisible = false;
      item.uploadDisabled = false;
      this.$message.error("上传失败")
    },
    handleUploadChange(file, fileList, item) {
      if (file.status == "ready") {
        item.percentage = file.percentage;
        item.uploadDisabled = true;
        item.uploadIconVisible = false;
        item.processIconVisible = true;
      } else if (file.status == "success") {
        item.uploadIconVisible = true;
        item.processIconVisible = false;
        item.uploadDisabled = false;
      }
    },
    beforeUpload(file, item) {
      const isLt1M = file.size / 1024 / 1024 < 1;
      if (!isLt1M) {
        this.$message.error('上传图片大小不能超过 1MB!');
        item.uploadIconVisible = true;
        item.processIconVisible = false;
        item.uploadDisabled = false;
      }
      return isLt1M;
    },
    // 图片上传end =======================================================>
    openGoodsBox(type, obj) {
      if (type == 'add') {
        this.goodsForm = {//真正的提交表单
          title: "新建作品",
          submitButton: "立即创建",
          name: '',
          shelfFlag: '0',
          shelfTime: [],//上架时间选择
          shelfSwitch: false,// true定期上架 false长期上架
          useTime: [],//使用时间选择
          useSwitch: false,// true定期上架 false长期上架
          orderNum: 0, // 新增字段，默认为0
          coverImgList: [//封面图片
            {index: 0, sizeType: 1, size: '比例一', url: '', uploadIconVisible: true, uploadList: []},
            {
              index: 1,
              sizeType: 2,
              size: '比例二',
              url: '',
              uploadIconVisible: true,
              uploadList: []
            },
            {
              index: 2,
              sizeType: 3,
              size: '比例三',
              url: '',
              uploadIconVisible: true,
              uploadList: []
            },
            {
              index: 3,
              sizeType: 4,
              size: '比例四',
              url: '',
              uploadIconVisible: true,
              uploadList: []
            }
          ],
          preCoverImgList: [],//预览封面图片list
          shareImg: {
            url: '',
            uploadIconVisible: true,
            uploadList: []
          },
          contentImgList: [],//内容图片
          preContentImgList: [],//预览内容图片list
          uploadContent: {
            list: [],
            uploadIconVisible: true,
            processIconVisible: false,
          },//上传按钮没有绑定图片 所以单独拿出来
          tagList: [],//标签列表
        };
        this.goodsBoxVisible = true;
      }
      if (type == 'put') {
        this.echoObj(obj)
      }

    },
    goodsSubmit() {
      this.$refs[this.goodsForm].validate((valid) => {
        if (valid) {
          console.log(this.goodsForm)
          if (!this.goodsForm.id) {
            addGoods(Object.assign(this.formatGoodFrom())).then(res => {
              console.log(res)
              this.goodsBoxVisible = false;
              this.$message.success("提交成功");
              this.refreshChange();
            }).catch()
          } else {
            putGoods(Object.assign(this.formatGoodFrom())).then(res => {
              console.log(res)
              this.goodsBoxVisible = false;
              this.$message({
                showClose: true,
                message: '修改成功',
                type: 'success'
              })
              this.refreshChange();
            }).catch(() => {
            })
          }
        } else {
          return false;
        }
      });
    },
    formatGoodFrom() {
      let res = {
        id: this.goodsForm.id,
        name: this.goodsForm.name,
        shelfStartTime: this.goodsForm.shelfTime[0],
        shelfEndTime: this.goodsForm.shelfTime[1],
        useStartTime: this.goodsForm.useTime[0],
        useEndTime: this.goodsForm.useTime[1],
        shelfFlag: this.goodsForm.shelfFlag,
        orderNum: this.goodsForm.orderNum, // 添加排序字段
        coverUrls: [],
        shareUrls: this.goodsForm.shareImg.url,
        contentUrls: [],
        tagList: [],
      };
      for (let i in this.goodsForm.coverImgList) {
        res.coverUrls.push({
          sizeType: this.goodsForm.coverImgList[i].sizeType,
          url: this.goodsForm.coverImgList[i].url,
        });
      }
      for (let i in this.goodsForm.contentImgList) {
        res.contentUrls.push({url: this.goodsForm.contentImgList[i].url});
      }
      if (!this.goodsForm.shelfSwitch) {
        res.shelfStartTime = null;
        res.shelfEndTime = null;
      }
      if (!this.goodsForm.useSwitch) {
        res.useStartTime = null;
        res.useEndTime = null;
      }
      for (let i in this.goodsForm.tagList) {
        res.tagList.push(this.goodsForm.tagList[i].id);
      }
      return res;
    },
    //回显数据
    echoObj(obj) {
      let tagList = [];
      getSomeoneTags({id: obj.id}).then(res => {
        tagList = res.data.data;
        let coverList = JSON.parse(obj.coverUrls)
        let contentList = JSON.parse(obj.contentUrls)
        let preCoverImgList = [];
        for (let i in coverList) {
          if (coverList[i].url) {
            preCoverImgList.push(coverList[i].url)
          }
        }
        let preContentList = [];
        for (let i in contentList) {
          preContentList.push(contentList[i].url)
        }
        this.goodsForm = {
          title: "修改作品",
          submitButton: "确认修改",
          id: obj.id,
          name: obj.name,
          shelfFlag: obj.shelfFlag,
          shelfTime: [obj.shelfStartTime ? obj.shelfStartTime : '', obj.shelfEndTime ? obj.shelfEndTime : '',],//上架时间选择
          shelfSwitch: obj.shelfStartTime ? true : false,// true定期上架 false长期上架
          useTime: [obj.useStartTime ? obj.useStartTime : '', obj.useEndTime ? obj.useEndTime : ''],//使用时间选择
          useSwitch: obj.useStartTime ? true : false,// true定期上架 false长期上架
          orderNum: obj.orderNum || 0, // 添加排序字段，如果为空则默认为0
          coverImgList: [//封面图片
            {
              index: 0,
              sizeType: 1,
              size: '比例一',
              url: coverList[0].url,
              uploadIconVisible: coverList[0].url ? false : true,
              uploadList: []
            },
            {
              index: 1,
              sizeType: 2,
              size: '比例二',
              url: coverList[1].url,
              uploadIconVisible: coverList[1].url ? false : true,
              uploadList: []
            },
            {
              index: 2,
              sizeType: 3,
              size: '比例三',
              url: coverList[2].url,
              uploadIconVisible: coverList[2].url ? false : true,
              uploadList: []
            },
            {
              index: 3,
              sizeType: 4,
              size: '比例四',
              url: coverList[3].url,
              uploadIconVisible: coverList[3].url ? false : true,
              uploadList: []
            }
          ],
          preCoverImgList: preCoverImgList,//预览封面图片list
          shareImg: {
            url: obj.shareUrls,
            uploadIconVisible: obj.shareUrls ? false : true,
            uploadList: []
          },
          contentImgList: JSON.parse(obj.contentUrls),//内容图片
          preContentImgList: preContentList,//预览内容图片list
          uploadContent: {
            list: [],
            uploadIconVisible: true,
            processIconVisible: false,
          },//上传按钮没有绑定图片 所以单独拿出来
          tagList: tagList,//标签列表
        };
        this.goodsBoxVisible = true;
      });
    },
    //上下架
    changeShelf(row){
      putShelf({id:row.id, shelfFlag:row.shelfFlag}).then(res => {
        console.log(res)
        this.goodsBoxVisible = false;
        this.$message({
          showClose: true,
          message: '修改成功',
          type: 'success'
        })
        this.refreshChange();
      }).catch(() => {
      })
    },
    //上下架
    editSortValue(row){

    },
    // 处理表格多选变化
    handleSelectionChange(selection) {
      this.selectedRows = selection;
      // 保存原始排序值，用于取消操作
      this.originalSortValues = selection.map(row => {
        return {
          id: row.id,
          orderNum: row.orderNum || 0
        };
      });
    },

    // 开始批量排序
    startBatchSort() {
      this.selectMode = true;
      // 默认排序值设为0
      this.batchSortValue = 0;
    },

    // 确认批量排序
    confirmBatchSort() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请至少选择一条记录');
        return;
      }

      // 更新选中行的排序值
      this.selectedRows.forEach(row => {
        row.orderNum = this.batchSortValue;
      });

      this.$message.success(`已将${this.selectedRows.length}条记录的排序值设置为${this.batchSortValue}`);
    },

    // 取消批量排序
    cancelBatchSort() {
      // 恢复原始排序值
      if (this.originalSortValues.length > 0) {
        this.selectedRows.forEach(row => {
          const originalRow = this.originalSortValues.find(item => item.id === row.id);
          if (originalRow) {
            row.orderNum = originalRow.orderNum;
          }
        });
      }

      // 退出批量排序模式
      this.selectMode = false;
      // 清空选择
      this.$refs.crud.selectClear();
    },

    // 提交批量排序
    submitBatchSort() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请至少选择一条记录');
        return;
      }

      // 构建API请求参数数组
      const updateData = this.selectedRows.map(row => {
        return {
          id: row.id,
          orderNum: this.batchSortValue
        };
      });

      // 显示加载提示
      this.tableLoading = true;

      // 调用批量更新API
      batchUpdateOrderNum(updateData).then(res => {
        this.$message.success(`已成功更新${this.selectedRows.length}条记录的排序值为：${this.batchSortValue}`);

        // 刷新数据
        this.refreshChange();

        // 退出批量排序模式
        this.selectMode = false;

        // 清空选择
        this.$refs.crud.selectClear();
      }).catch(error => {
        console.error('批量更新排序失败:', error);
        this.$message.error('批量更新排序失败，请重试');
      }).finally(() => {
        this.tableLoading = false;
      });
    },
  }
}
</script>

<style lang="scss" scoped>

.goods_form_tag {
  margin-right: 10px;
}

.form-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.userTagBox {
  height: 400px;
  overflow: scroll
}

.userTagBox_type {
  overflow: auto;
}

.userTagBox_tag {
  display: block;
  float: left;
  padding: 10px;
}

.img_dialog {
  position: absolute;
  left: 80%;
  top: 0;
  width: 30px;
  opacity: 0;
}

.image_preview {
  position: relative;
  float: left;
  display: inline;
  margin: 0px 15px 10px 0px;

  .image_preview_image {
    border: 1px solid transparent;
    width: 150px;
    height: 150px;
  }

  &:hover .img_dialog {
    text-align: center;
    cursor: default;
    text-align: center;
    color: #fff;
    opacity: 1;
    font-size: 20px;
    background-color: rgba(0, 0, 0, .5);
    transition: opacity .3s;
  }
}

.content_image_preview {
  position: relative;
  float: left;
  display: inline;
  margin-right: 15px;

  &:hover .img_dialog {
    text-align: center;
    cursor: default;
    text-align: center;
    color: #fff;
    opacity: 1;
    font-size: 20px;
    background-color: rgba(0, 0, 0, .5);
    transition: opacity .3s;
  }
}

.waite_upload_img {
  border: 1px #8c939d dashed;
  border-radius: 6px;
  width: 150px;
  height: 150px
}

.search_form {
}

/* 新增样式 */
.batch-actions {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.batch-actions .el-button {
  margin-left: 10px;
}
</style>
