import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/payapi/payconfig/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/payapi/payconfig',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/payapi/payconfig/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/payapi/payconfig/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/payapi/payconfig',
        method: 'put',
        data: obj
    })
}

export function getByType(type) {
  return request({
    url: '/payapi/payconfig/bytype/' + type,
    method: 'get'
  })
}
