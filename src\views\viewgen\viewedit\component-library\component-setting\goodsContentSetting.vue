<template>
  <div>
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">作品内容</p>
      <div slot="hint">
      </div>
      <div slot="mainContent">
        <el-form ref="form"  label-width="100px" :model="formData">
          <el-alert
            title="使用提示"
            type="warning"
            :closable="false"
            description="此组件会自动加载作品内容图片,需要外部链接进入才会展示。">
          </el-alert>
          <el-divider >基础属性</el-divider>
          <el-form-item label="上下间距">
            <el-input v-model="formData.topAndBottomSpacing" size="mini" type="number" :min="0"  style="margin-top: 5px" placeholder="与下面元素的间隔">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="左右间距">
            <el-input v-model="formData.leftAndRightSpacing" size="mini" type="number" :min="0" style="margin-top: 5px" placeholder="与下面元素的间隔">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
    </settingSlot>
  </div>
</template>

<script>

  import { mapState , mapGetters , mapMutations , mapActions } from 'vuex';

  import settingSlot from './settingSlot'
  import MaterialList from '@/components/material/wxlist.vue'
  import AppPageSelect from '@/components/app-page-select/Index.vue'
  import {getTagAndType} from "@/api/viewgen/wxgoodstag";

  export default {
    components: { settingSlot, MaterialList, AppPageSelect  },
    data() {
      return {
        tagsAndTypeList:[],
        formDataCopy: {
          topAndBottomSpacing: 1,
          leftAndRightSpacing: 1,
        },
        formData : {}
      };
    },
    props: {
      clientType: [String],
      showData:{
        type: Object,
        default: ()=> {}
      },
      config   : {
        type: Object,
        default: ()=> {}
      }
    },
    created() {
    },
    computed: {
      ...mapState({
        componentsList: state => state.divpage.componentsList,
        clickComIndex : state => state.divpage.clickComIndex,
      })
    },
    watch: {
      showData: {
        handler(newVal, oldVal) {
          this.formData = newVal ? newVal : this.formData;
        },
        deep: true
      },
    },
    mounted(){
      let that = this;
      if(that.IsEmptyObj(that.showData)){
        that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      } else {
        that.formData = that.showData
      }
      that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
      // that.updateData({
      //   componentsList: that.componentsList
      // })
    },
    methods: {
      ...mapMutations([
        'updateData'
      ]),

      // 添加项目
      addItem(){
        let that = this;
        that.pushItem()
      },
      pushItem(){
        let that = this;
        if(that.formData.noticeList.length >=10){
          that.$message.error("项目不能超过10条")
          return false;
        }
        that.formData.noticeList.push({
          id       : Math.random(),
          imageUrl : '',
          imgWidth : 0,
          imgHeight: 0,
          pageUrl  : '',
          content  : '',
          tag: ''
        })
      },
      // 删除项目
      delItem(index){
        let that = this;
        if(that.formData.swiperList.length<=1){
          that.$message.error("请至少保留一条项目")
          return false;
        }
        that.$confirm('是否删除该项目?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText : '取消',
          type : 'warning'
        }).then(()=>{
          that.$delete(that.formData.noticeList, index)
        }).catch(()=>{})
      },
      // 删除按钮
      delBtn(index){
        let that = this;
        that.$confirm('是否删除该按钮?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText : '取消',
          type : 'warning'
        }).then(()=>{
          that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
          that.updateData({ componentsList: that.componentsList });
        }).catch(()=>{})
      },
      cancel(){
        this.$emit('cancel')
      },
      reset(){
        let that = this;
        that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
        that.componentsList[that.clickComIndex]['data'] = this.formData;
      },
      confirm(){
        this.$emit('confirm', this.formData)
      },
      delete(){
        this.$emit('delete')
      },
      loadTag(){
        console.log("架子啊")
        getTagAndType().then(res => {
          this.tagsAndTypeList = res.data.data;
          console.log(" this.tagsAndTypeList ", this.tagsAndTypeList)
          console.log(this.goodsForm.tagList)
          // //选中商品标签
          // for (let i in this.goodsForm.tagList) {
          //   for (let j in this.tagsAndTypeList) {
          //     for (let k in this.tagsAndTypeList[j].tagList) {
          //       if (this.goodsForm.tagList[i].id == this.tagsAndTypeList[j].tagList[k].id) {
          //         this.tagsAndTypeList[j].tagList[k].checked = true;
          //       }
          //     }
          //   }
          // }
        })
      },
      tagAllSelect(){
        let list = [];
        list = this.formData.tagList;
        for (let i in this.tagsAndTypeList) {
          for (let j in  this.tagsAndTypeList[i].tagList) {
              list.push(this.tagsAndTypeList[i].tagList[j].id)
          }
        }
      },
      tagInvSelect(){
        //todo  还有问题
        let list = [];
        let result = [];
        list = this.formData.tagList;
        for (let i in this.tagsAndTypeList) {
          for (let j in  this.tagsAndTypeList[i].tagList) {
            for (let k in list) {
              if(this.tagsAndTypeList[i].tagList[j].id === list[k]){
                break;
              }
              if( k == list.length-1){
                result.push(this.tagsAndTypeList[i].tagList[j].id);
              }
            }
          }
        }
        this.formData.tagList = [];
        this.formData.tagList= result;
      },
      tagEmpty(){
          this.formData.tagList = [];
      },

    },

  };
</script>
<style lang='less' scoped>

  @import '../colorui/main.css';
  .el-form-item{
    margin-bottom: 0;
  }
</style>
