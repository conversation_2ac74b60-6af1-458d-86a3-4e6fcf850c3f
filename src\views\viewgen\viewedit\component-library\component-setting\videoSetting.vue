<template>
  <div class="videoSetting">
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">视频组件设置</p>
      <div slot="hint">
      </div>

      <div slot="mainContent">
        <el-form ref="form" label-width="90px" :model="formData">
          <div>
            <el-form-item label="视频地址">
              <el-input v-model="formData.videoUrl" size="mini" placeholder="请输入视频URL地址"></el-input>
            </el-form-item>
            <el-form-item label="封面图片">
              <el-input v-model="formData.coverUrl" size="mini" placeholder="请输入封面图片URL地址"></el-input>
            </el-form-item>
            <el-form-item label="背景颜色">
              <bg-color-select :thememobile="thememobile" :bgValue="formData.background" @onChange="formData.background = $event"></bg-color-select>
            </el-form-item>
            <el-form-item label="内边距">
              <el-input v-model="formData.padding" size="mini" type="number" placeholder="内边距大小"></el-input>
            </el-form-item>
            <el-form-item label="圆角角度">
              <el-input v-model="formData.radius" size="mini" type="number" placeholder="圆角角度"></el-input>
            </el-form-item>
            <el-form-item label="视频高度">
              <el-input v-model="formData.height" size="mini" type="number" placeholder="视频高度，不填则自适应"></el-input>
            </el-form-item>
            <el-form-item label="显示控制栏">
              <el-switch v-model="formData.showControls"></el-switch>
            </el-form-item>
            <el-form-item label="自动播放">
              <el-switch v-model="formData.autoplay"></el-switch>
            </el-form-item>
            <el-form-item label="循环播放">
              <el-switch v-model="formData.loop"></el-switch>
            </el-form-item>
            <el-form-item label="静音播放">
              <el-switch v-model="formData.muted"></el-switch>
            </el-form-item>
            <el-form-item label="显示标题">
              <el-switch v-model="formData.showTitle"></el-switch>
            </el-form-item>
            <template v-if="formData.showTitle">
              <el-form-item label="视频标题">
                <el-input v-model="formData.title" size="mini" placeholder="请输入视频标题"></el-input>
              </el-form-item>
              <el-form-item label="标题大小">
                <el-input v-model="formData.titleSize" size="mini" type="number" placeholder="标题字体大小"></el-input>
              </el-form-item>
              <el-form-item label="标题颜色">
                <el-tooltip effect="dark" content="色值代码，如#333333" placement="top">
                  <el-input v-model="formData.titleColor" size="small">
                    <template slot="append">
                      <el-color-picker size="mini" v-model="formData.titleColor"></el-color-picker>
                    </template>
                  </el-input>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="标题位置">
                <el-radio-group v-model="formData.titlePosition">
                  <el-radio label="left">左对齐</el-radio>
                  <el-radio label="center">居中</el-radio>
                  <el-radio label="right">右对齐</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="标题加粗">
                <el-switch v-model="formData.titleBold"></el-switch>
              </el-form-item>
            </template>
          </div>
        </el-form>
      </div>
    </settingSlot>
  </div>
</template>

<script>
  import { mapState, mapMutations } from 'vuex';
  import settingSlot from './settingSlot';
  import bgColorSelect from "../pages/page-components/bgColorSelect";

  export default {
    components: { settingSlot, bgColorSelect },
    data() {
      return {
        formDataCopy: {
          videoUrl: '',
          coverUrl: '',
          background: '#ffffff',
          padding: 10,
          radius: 0,
          height: 200,
          showControls: true,
          autoplay: false,
          loop: false,
          muted: false,
          showTitle: false,
          title: '视频标题',
          titleSize: 14,
          titleColor: '#333333',
          titlePosition: 'center',
          titleBold: false
        },
        formData: {}
      };
    },
    props: {
      thememobile: { type: Object | Array },
      showData: {
        type: Object,
        default: () => {}
      },
      clientType: {
        type: String,
        default: ''
      }
    },
    computed: {
      ...mapState({
        componentsList: state => state.divpage.componentsList,
        clickComIndex: state => state.divpage.clickComIndex,
      })
    },
    mounted() {
      let that = this;
      if (that.IsEmptyObj(that.showData)) {
        that.formData = JSON.parse(JSON.stringify(that.formDataCopy));
      } else {
        that.formData = that.showData;
      }
      that.$set(that.componentsList[that.clickComIndex], 'data', this.formData);
    },
    methods: {
      ...mapMutations([
        'updateData'
      ]),
      cancel() {
        this.$emit('cancel');
      },
      reset() {
        let that = this;
        that.formData = JSON.parse(JSON.stringify(that.formDataCopy));
        that.componentsList[that.clickComIndex]['data'] = this.formData;
      },
      confirm() {
        this.$emit('confirm', this.formData);
      },
      delete() {
        this.$emit('delete');
      },
      IsEmptyObj(obj) {
        return Object.keys(obj).length === 0;
      }
    },
    watch: {
      showData: {
        handler(newVal, oldVal) {
          this.formData = newVal ? newVal : this.formData;
        },
        deep: true
      },
      thememobile() {}
    }
  };
</script>
<style lang='less' scoped>
  .videoSetting {
    /deep/ .el-form .el-form-item {
      margin-bottom: 8px;
    }

    .el-form-item {
      margin-bottom: 0;
    }
  }
</style> 