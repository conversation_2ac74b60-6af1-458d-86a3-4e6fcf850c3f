<!--排序选取-->
<template>
  <div class="execution">
    <basic-container>
      <avue-crud ref="crud"
                 :page="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 v-model="form"
                 @on-load="getPage"
                 @refresh-change="refreshChange"
                 @row-update="handleUpdate"
                 @row-save="handleSave"
                 @row-del="handleDel"
                 @sort-change="sortChange"
                 @row-click="rowClick"
                 @search-change="searchChange">
        <template slot-scope="scope" slot="menuLeft">
          <el-button type="primary"
                     icon="el-icon-plus"
                     size="small"
                     @click.stop="openFormBox('add')">新增
          </el-button>
        </template>
        <template slot-scope="scope" slot="menu">

          <el-button @click="goDetail(scope.row)" icon="el-icon-edit" type="text"
                     size="small">进入
          </el-button>
          <el-button v-if="permissionList.editBtn" @click="openFormBox('put',scope.row)" icon="el-icon-edit" type="text"
                     size="small">编辑
          </el-button>
          <el-button v-if="permissionList.delBtn" @click="handleDel(scope.row,scope.index)" icon="el-icon-delete"
                     type="text" size="small">删除
          </el-button>
        </template>
      </avue-crud>
    </basic-container>

    <!--     提交表单-->
    <el-dialog
      title="新建排序"
      :visible.sync="addBoxVisible"
      :close-on-click-modal="false"
      @close="resetForm()"
      center
      :append-to-body="true"
      lock-scroll
      width="40%">
      <div style="overflow: hidden">
        <el-form :rules="formRules" :ref="form" :model="form" label-width="auto">
          <el-form-item label="排序名称" prop="name">
            <el-input v-model="form.name" :maxlength="25" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="公众号" prop="appId">
            <el-select :disabled="form.appIdDisabled" v-model="form.appId" clearable placeholder="请选择">
              <el-option
                v-for="item in wxAppList"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="confirmFrom">确认</el-button>
            <el-button @click="addBoxVisible = false">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {getPage, getObj, addObj, putObj, delObj} from '@/api/viewgen/wxgoodssort'
import {selectTableOption} from '@/const/crud/viewgen/wxgoodssort'
import {mapGetters} from 'vuex'
import {getList as getWxAppList} from "@/api/wxmp/wxapp";

export default {
  data() {
    return {
      wxAppList: [],
      addBoxVisible: false,
      form: {
        name: '',
        appId: '',
        appIdDisabled: false,
      },
      formRules: {
        name: [
          {required: true, message: '请输入排序名称', trigger: 'submit'},
        ],
        appId: [
          {required: true, message: '请选择公众号', trigger: 'submit'}
        ],
      },
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: selectTableOption
    }
  },
  created() {
  },
  mounted() {
    this.getWxApp();
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.permissions['weixin:wxgoodssort:add'] ? true : false,
        delBtn: this.permissions['weixin:wxgoodssort:del'] ? true : false,
        editBtn: this.permissions['weixin:wxgoodssort:edit'] ? true : false,
        viewBtn: this.permissions['weixin:wxgoodssort:get'] ? true : false
      };
    }
  },
  methods: {
    getWxApp() {
      getWxAppList({
        appType: '2'
      }).then(res => {
        let data = res.data
        this.wxAppList = data;
        //默认加载第一个公众号的素材
        this.appIdList.push(data[0].id);
        for (let i = 0; i < data.length; i++) {
          tableOption.column[3].dicData.push({label: data[i].name, value: data[i].id});
        }
      }).catch(() => {
      })
    },
    searchChange(params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    sortChange(val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage(page, params) {
      this.tableLoading = true
      getPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
      }, params, this.paramsSearch)).then(response => {
        this.tableData = response.data.data.records
        this.page.total = response.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this
      this.$confirm('是否确认删除此数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        return delObj(row.id)
      }).then(data => {
        _this.$message({
          showClose: true,
          message: '删除成功',
          type: 'success'
        })
        this.getPage(this.page)
      }).catch(function (err) {
      })
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      putObj(row).then(response => {
        this.$message({
          showClose: true,
          message: '修改成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      addObj(row).then(response => {
        this.$message({
          showClose: true,
          message: '添加成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page)
    },
    resetForm() {
      console.log("guanle ")
      this.$refs[this.form].resetFields();
      console.log("this.form", this.form)
    },
    openFormBox(type, obj) {
      if ('put' == type) {
        //编辑
        this.form.id = obj.id;
        this.form.name = obj.name;
        this.form.appId = obj.appId;
        console.log(this.form)
        this.form.appIdDisabled = true;
      } else if ('add' == type) {
        this.form.appIdDisabled = false;
      }
      this.addBoxVisible = true;

    },
    confirmFrom() {
      if(!this.form.id) {
        console.log(this.form)
        this.$refs[this.form].validate((valid) => {
          if (valid) {
            addObj(this.form).then(res => {
              this.$message({
                showClose: true,
                message: '添加成功',
                type: 'success'
              })
              this.addBoxVisible = false;
              this.refreshChange()
            }).catch(() => {
            })
          }
        });
      } else {
        this.$refs[this.form].validate((valid) => {
          if (valid) {
            putObj(this.form).then(response => {
              this.$message({
                showClose: true,
                message: '修改成功',
                type: 'success'
              })
              this.addBoxVisible = false;
              this.refreshChange()
            }).catch(() => {
            })
          }
        });
      }
    },
    //查看详情
    goDetail(row) {
      console.log(row)
      //路由跳转
      this.$router.push({ name: '排序详情',params: {id: row.id}});
    },
    rowClick(row,){
      this.$emit("confirm",row)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
