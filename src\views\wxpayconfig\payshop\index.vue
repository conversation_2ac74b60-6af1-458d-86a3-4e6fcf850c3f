<template>
  <div class="execution">
    <basic-container>
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>微信第三方平台配置</span>
        </div>
        <avue-form ref="form" v-model="form" :option="tableOption" @submit="handleUpdate" :permission="permissionList">
        </avue-form>
      </el-card>
    </basic-container>
  </div>
</template>

<script>
import { getObj,putObj} from '@/api/wxpayconfig/wxpayshop'
import {tableOption} from '@/const/crud/wxpayconfig/payshop'
import {mapGetters} from 'vuex'

export default {
  name: 'payshop',
  components: {

  },
  data() {
    return {
      form: {},
      tableOption: tableOption
    }
  },
  created() {
    this.handleGet()
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        editBtn: this.permissions['weixin:wxpayshop:edit'] ? true : false,
      };
    }
  },
  methods: {
    handleGet: function () {
      getObj().then(res => {
        console.log(res)
        this.form = res.data.data ? res.data.data : {}
      })
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate(form, done) {
      putObj(this.form).then(res => {
        done()
        this.$message({
          showClose: true,
          message: '修改成功',
          type: 'success'
        })
      }).catch(() => {
        done()
      })
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
