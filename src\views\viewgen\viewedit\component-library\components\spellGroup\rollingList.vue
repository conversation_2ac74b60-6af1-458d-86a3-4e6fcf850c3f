<template>
  <div class="rollingListComponent">
    <span>
      <span v-show="setData.enterFlag">已有{{setData.enterNum}}人关注 </span>
      <span v-show="setData.payFlag">已有{{setData.payNum}}购买</span>
    </span>
    <div v-for="(item,index) in 10" :key="index" >
       <div class="content">
         <div class="cu-avatar round bg-img lg margin-xs  "
              style="background-image:url('http://thirdwx.qlogo.cn/mmopen/ajNVdqHZLLDTz1ymn3XLacynx7pnkjfho4VZMgQicTYicyvLbxXTpu6AqaFWU9qOhLheoSyBQKLRic43NMiaR7Xffw/132');height: 30px;width: 30px;">
         </div>
           <p>用户***01</p>
           <p style="padding-left: 10px">10分钟前参与下单</p>
       </div>
    </div>
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';

export default {
  data() {
    return {};
  },
  components: {},
  props: {
    theme: {type: Object | Array},
    setData: {type: Object | Array},
    cId: {type: Number},
    noEditor: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {
  },
  mounted() {
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
  },
  watch: {
    setData(newVal, oldVal) {
    },
    componentsList(newVal, oldVal) {          //添加的时候出发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    }
  },
  beforeDestroy() {
    // this.$root.Bus.$off('addHotSpot')
  }
};
</script>
<style lang='less' scoped>

@import '../../colorui/main.css';
@import '../../colorui/icon.css';

.rollingListComponent{
  height: 100px;
  overflow: hidden;
}
.content {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

</style>
