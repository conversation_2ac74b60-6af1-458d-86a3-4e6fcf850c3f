<template>
  <div class="execution">
      <avue-crud ref="crud"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 @on-load="getPage"
                 @refresh-change="refreshChange"
                 @row-update="handleUpdate"
                 @row-save="handleSave"
                 @row-del="handleDel"
                 @sort-change="sortChange"
                 @search-change="searchChange">
        <template slot="id" slot-scope="scope">
          <div style="display: flex;align-items: center;justify-items: start">
          <div v-if="scope.row.headimgUrl">
            <el-avatar size="small" :src="scope.row.headimgUrl"> </el-avatar>
          </div>
          <div v-else>
            <el-avatar icon="el-icon-user-solid"></el-avatar>
          </div>
          <p>{{scope.row.nickName}}</p>
          </div>
        </template>
      </avue-crud>
      <el-button v-if="tableData && tableData.length>0" type="primary" @click="openAllFriendBox" style="width: 100%;margin-top: 15px" size="mini">查看更多</el-button>
    <!-- 全部的列表 allFriendBoxVisible-->
    <el-dialog
      title="好友列表"
      :visible.sync="allFriendBoxVisible"
      width="80%"
      center>
      <avue-crud ref="allCrud"
                 :page="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption2"
                 v-model="form"
                 @on-load="getPage"
                 @refresh-change="refreshChange"
                 @row-update="handleUpdate"
                 @row-save="handleSave"
                 @row-del="handleDel"
                 @sort-change="sortChange"
                 @search-change="searchChange">
      </avue-crud>
    </el-dialog>
  </div>
</template>

<script>
import {getPage, getObj, addObj, putObj, delObj} from '@/api/wxmp/wxuserfriend'
import {tableOption, tableOption2} from '@/const/crud/wxmp/wxuserfriend'
import {mapGetters} from 'vuex'

export default {
  name: 'wxuserfriend',
  props: {
    appId: {
      type: String,
    },
    userId: {
      type: String,
    },
  },
  data() {
    return {
      appIdTemp:'',
      userIdTemp:'',
      allFriendBoxVisible:false,
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      tableOption2: tableOption2
    }
  },
  created() {
  },
  mounted: function () {
  },
  watch: {
    appId(val, oldVal) {
      if (val != oldVal) {
        this.appIdTemp = val;
      }
    },
    userId(val, oldVal) {
      if (val != oldVal) {
        this.userIdTemp = val;
        this.refreshChange()
      }
    },
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.permissions['weixin:wxuserfriend:add'] ? true : false,
        delBtn: this.permissions['weixin:wxuserfriend:del'] ? true : false,
        editBtn: this.permissions['weixin:wxuserfriend:edit'] ? true : false,
        viewBtn: this.permissions['weixin:wxuserfriend:get'] ? true : false
      };
    }
  },
  methods: {
    searchChange(params,done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    sortChange(val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage(page, params) {
      if( !this.userId ||  !this.appId){
        return;
      }
      this.tableLoading = true
      this.paramsSearch = {
        userId: this.userIdTemp,
        appId: this.appIdTemp
      };
      getPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
      }, params, this.paramsSearch)).then(res => {
        console.log("res",res)
        this.tableData = res.data.data.records
        this.page.total = res.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this
      this.$confirm('是否确认删除此数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        return delObj(row.id)
      }).then(data => {
        _this.$message({
          showClose: true,
          message: '删除成功',
          type: 'success'
        })
        this.getPage(this.page)
      }).catch(function (err) {
      })
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      putObj(row).then(res => {
        this.$message({
          showClose: true,
          message: '修改成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      addObj(row).then(res => {
        this.$message({
          showClose: true,
          message: '添加成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page)
    },
    openAllFriendBox() {
      this.allFriendBoxVisible = true;
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
