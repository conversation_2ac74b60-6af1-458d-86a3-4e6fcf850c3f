// base.scss : 放置公共样式
@import './mixin.scss';
.container{
  position: relative;
  width: 1226px;
  margin-right:auto;
  margin-left:auto;
}
input{
  outline: none;
  box-sizing: border-box;
  font-size: 14px;
}
.fl{
  float: left;
}
.fr{
  float: right;
}
.clearfix:before,.clearfix:after{
  content:' ';
  display:table;
}
.clearfix:after{
  clear: both;
}
// 公共头部导航的logo
.header-logo{
  display:inline-block;
  width:55px;
  height:55px;
  background-color:#FF6600;
  a{
    display:inline-block;
    width:110px;
    height:55px;
    &:hover:before{
      margin-left:-55px;
      transition:margin .2s;
    }
  }
}
