import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/wxpagedevise/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/weixin/wxpagedevise',
    method: 'post',
    data: obj
  })
}

export function copyPage(obj) {
  return request({
    url: '/weixin/wxpagedevise/copyPage',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/weixin/wxpagedevise/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/wxpagedevise/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/wxpagedevise',
    method: 'put',
    data: obj
  })
}
// 根据类别拿取页面
export function getListByType(type) {
  return request({
    url: '/weixin/wxpagedevise/type/' + type,
    method: 'get'
  })
}
//按条件查询list
export function getListByTypeList(query) {
  return request({
    url: '/weixin/wxpagedevise/get/typelist',
    method: 'get',
    params: query
  })
}
//查询页面 按类型分组
export function getGroupList(query) {
  return request({
    url: '/weixin/wxpagedevise/get/grouplist',
    method: 'get',
    params: query
  })
}
//查询已有页面类型
export function getTypeList() {
  return request({
    url: '/weixin/wxpagedevise/typelist',
    method: 'get'
  })
}
//查询已有页面
export function getPageList() {
  return request({
    url: '/weixin/wxpagedevise/list',
    method: 'get'
  })
}
