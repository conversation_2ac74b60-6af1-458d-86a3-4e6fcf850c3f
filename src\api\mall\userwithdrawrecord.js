import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/mall/userwithdrawrecord/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/mall/userwithdrawrecord',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/mall/userwithdrawrecord/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/mall/userwithdrawrecord/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/mall/userwithdrawrecord',
        method: 'put',
        data: obj
    })
}

export function putStatus(obj) {
  return request({
    url: '/mall/userwithdrawrecord/status',
    method: 'put',
    data: obj
  })
}
