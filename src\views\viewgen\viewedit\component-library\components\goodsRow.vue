<template>
    <div class="goodsComponent" :style="{marginBottom: `${setData.pageSpacing}px`}">
      <div class="wrapper-list"  >
        <div class="cu-bar">
          <div class="shop-selection text-df margin-left" :style="{color: `${setData.titleColor}`}">
            <span class=" text-bold" :class="setData.titleIcon"></span>
            <span class="margin-left-xs">{{setData.title}}</span>
          </div>
          <div class="shop-more text-sm margin-right">更多<text class="cuIcon-right"></text></div>
        </div>
        <div class=" shop-detail" style="overflow-x:scroll;padding: 8px 0;position: relative;display:flex;" v-if="setData.goodsList&&setData.goodsList.length > 0">
          <div v-for="(item, index) in setData.goodsList" :key="index" style="float: left;margin-right: 8px;">
            <div class="item shadow-warp radius goods-card">
              <div class="img-box">
                <img :src="item.picUrls[0] ? item.picUrls[0] : noPic" mode="aspectFill"  class="card-img">
              </div>
              <div class="text-black margin-top-xs  overflow-2" style="padding: 0 5px;">{{item.name}}</div>
              <div class="flex justify-between align-center" style="padding: 5px;">
                <div class="text-price text-bold text-red text-lg ">{{item.priceDown}}</div>
                <div class="cu-tag bg-red radius sm" v-if="item.freightTemplat&&item.freightTemplat.type == '2'">包邮</div>
                <div class="text-gray text-sm padding-lr-sm">已售{{item.saleNum}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>

<script>
import { mapState , mapGetters , mapMutations , mapActions } from 'vuex';
import placeholderImg from "../pages/page-components/placeholderImg";


export default {
    data() {
        return {
          noPic: require('../assets/images/icon/<EMAIL>')
        };
    },
    components: { placeholderImg },
    props: {
        theme : { type: Object | Array },
        setData : { type: Object | Array },
        cId     : { type: Number },
        noEditor: {
            type: Boolean,
            default: false,
        }
    },
    computed: {
        ...mapState({
            componentsList: state => state.divpage.componentsList,
        }),
    },
    created() {
    },
    mounted() {
    },
    methods: {
        ...mapMutations([
            'updateData'
        ]),
    },
    watch:{
        setData(newVal, oldVal){},
        componentsList(newVal, oldVal){          //添加的时候出发（解决没有移动的时候不emit数据）
            let that = this;
            that.updateData({componentsList: that.componentsList})
        }
    },
    beforeDestroy(){
        // this.$root.Bus.$off('addHotSpot')
    }
};
</script>
<style lang='less' scoped>

  @import '../colorui/main.css';
  @import '../colorui/icon.css';
  .goodsComponent {
    position: relative;
    display: block;
    width: 100%;
    background: #ffffff;

    .wrapper-list{
      padding: 0 15px;
    }
    .goods-item{
      margin: auto !important;
      margin-top: 10px !important;
    }

    .row-img {
      margin-top: 13px;
      margin-left: -10px;
      width: 120px !important;
      height: 120px !important;
      border-radius: 5px
    }
    .card-img {
      width: 100% !important;
      height: 100% !important;
      border-radius: 5px
    }

    .buy{
      padding: 3px 10px 5px 10px;
    }

    .goods-container {
      justify-content: space-between;
      flex-wrap: wrap;
      box-sizing: content-box;
      padding: 10px;
    }

    .goods-box {
      width: 175px;
      height: 265px;
      background-color: #fff;
      overflow: hidden;
      margin-bottom: 10px;
      border-radius: 5px;
      box-shadow:0px 10px 10px #e5e5e5;
    }

    .goods-box .img-box {
      width: 100%;
      height: 175px;
      overflow: hidden;
    }

    .goods-box .img-box image {
      width: 100%;
      height: 265px;
    }
  }

  .overflow-1 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
  .overflow-2 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
</style>
