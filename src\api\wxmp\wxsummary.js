import request from '@/router/axios'

export function getUserSummary(query) {
  return request({
    url: '/weixin/wxsummary/usersummary',
    method: 'get',
    params: query
  })
}

export function getUserCumulate(query) {
  return request({
    url: '/weixin/wxsummary/usercumulate',
    method: 'get',
    params: query
  })
}

export function getInterfaceSummary(query) {
  return request({
    url: '/weixin/wxsummary/interfacesummary',
    method: 'get',
    params: query
  })
}

