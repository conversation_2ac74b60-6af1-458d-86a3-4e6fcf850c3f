<template>
  <div class="execution">
    <basic-container>
      <el-row>
        <el-col :span="24">
          <avue-crud ref="crud"
                     :page="page"
                     :data="tableData"
                     :permission="permissionList"
                     :table-loading="tableLoading"
                     :option="tableOption"
                     v-model="form"
                     @on-load="getPage"
                     @refresh-change="refreshChange"
                     @row-update="handleUpdate"
                     @row-save="handleSave"
                     @row-del="handleDel"
                     @sort-change="sortChange"
                     @search-change="searchChange">
            <template slot-scope="scope" slot="menuLeft">
              <el-button type="primary"
                         icon="el-icon-plus"
                         size="small"
                         @click.stop="openFormBox('add')">新增
              </el-button>
            </template>
            <template slot-scope="scope" slot="menu">
<!--              <el-button icon="el-icon-full-screen"    size="small" type="text"  @click="qrCode">二维码</el-button>-->
              <el-button icon="el-icon-copy-document"   size="small"  type="text" @click="copyPageUrl(scope.row)">复制地址</el-button>
              <el-button icon="el-icon-edit" type="text"  size="small" @click="openFormBox('put',scope.row)">编辑</el-button>
              <el-button icon="el-icon-delete" type="text"  size="small" @click="handleDel(scope.row)">删除</el-button>
            </template>
          </avue-crud>
        </el-col>
      </el-row>
    </basic-container>
    <!--     提交表单-->
    <el-dialog
      title="新建绑定页面"
      :visible.sync="formBoxVisible"
      :close-on-click-modal="false"
      center
      @close="cancelSubmit"
      :append-to-body="true"
      lock-scroll
      width="80%">
      <div style="overflow: hidden">
        <el-form :ref="dataForm" :model="dataForm" :rules="addFormRules" label-width="100px" size="mini">
          <el-form-item label="页面名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="请输入页面标题"></el-input>
          </el-form-item>
          <el-divider content-position="center">微信分享设置</el-divider>
          <el-form-item label="分享标题">
            <el-input v-model="dataForm.pageBase.shareTitle" size="mini"></el-input>
          </el-form-item>
          <el-form-item label="分享描述">
            <el-input v-model="dataForm.pageBase.describe" size="mini"></el-input>
          </el-form-item>
          <el-form-item label="分享图标" prop="expireCoverImg">
            <MaterialLibrary :value="dataForm.pageBase.shareImgUrl?[dataForm.pageBase.shareImgUrl]:[]"
                          @sureSuccess="dataForm.pageBase.shareImgUrl = $event?$event[0]:''"
                          @deleteMaterial="dataForm.pageBase.shareImgUrl = ''"
                          type="image" :shopId="'-1'"
                          :num=1
                          :divStyle="'width:50%;height:95px;margin-bottom:8px;line-height: 100px;'"
                          default-library-type="wechat"></MaterialLibrary>
          </el-form-item>
          <el-divider content-position="center">内容设置</el-divider>
          <el-form-item label="页面标题" >
            <el-input v-model="dataForm.pageBase.pageTitle" placeholder="请输入页面标题"></el-input>
          </el-form-item>
          <el-form-item label="主题颜色" prop="theme">
            <bg-color-select :thememobile="{}" :bgValue="dataForm.theme" @onChange="dataForm.theme = $event"></bg-color-select>
          </el-form-item>
          <el-form-item label="客片标题" prop="title">
            <el-input v-model="dataForm.title" placeholder="分享客片的时候显示的标题，以及页面内的标题"></el-input>
          </el-form-item>
          <el-form-item label="页面头图" prop="imgUrl">
            <MaterialLibrary :value="dataForm.imgUrl?[dataForm.imgUrl]:[]"  
                          @sureSuccess="dataForm.imgUrl = $event?$event[0]:''" 
                          @deleteMaterial="dataForm.imgUrl = ''"
                          type="image" :shopId="'-1'" 
                          :divStyle="'width:100%;margin-bottom:0px;height:100px;line-height: 100px;'" 
                          :num=1
                          default-library-type="wechat"></MaterialLibrary>
          </el-form-item>
          <el-form-item label="填写提示" prop="remind">
            <el-input v-model="dataForm.remind" placeholder="请输入客片标题"></el-input>
          </el-form-item>
          <el-form-item label="短信配置" prop="remind">
            <el-alert
              title="使用提示"
              type="warning"
              :closable="false"
              description="发送短信，需要完善短信配置，请保证系统配置已经完成此步骤！">
            </el-alert>
            <el-input v-model="dataForm.signName" size="mini" style="margin-top: 5px" placeholder="请输入签名"></el-input>
            <el-input v-model="dataForm.templateCode" size="mini" style="margin-top: 5px" placeholder="请输入模版编号"></el-input>
          </el-form-item>
<!--          <el-form-item label="邮箱规则" prop="emailRules">-->
<!--            <el-radio-group v-model="dataForm.emailRules">-->
<!--              <el-radio :label="'0'">不需要登记邮箱</el-radio>-->
<!--              <el-radio :label="'1'">需要登记邮箱</el-radio>-->
<!--            </el-radio-group>-->
<!--          </el-form-item>-->
<!--          只有待签用户可以解决-->
<!--          <el-form-item label="用户规则" prop="userRules">-->
<!--            <el-radio-group v-model="dataForm.userRules">-->
<!--              <el-radio :label="'0'">仅待签用户进行绑定</el-radio>-->
<!--              <el-radio :label="'1'">所有用户均可绑定</el-radio>-->
<!--            </el-radio-group>-->
<!--          </el-form-item>-->
          <!--            <el-form-item label="通知钉钉"  prop="payNum">-->
          <!--            </el-form-item>-->
          <el-form-item label="协议文本" prop="content">
            <div style="overflow: hidden">
              <view-gen-editor v-model="dataForm.content"></view-gen-editor>
            </div>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm">提交</el-button>
            <el-button type="primary" @click="cancelSubmit">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {getObj as getWxApp} from "@/api/wxmp/wxapp";
import {getPage, getObj, addObj, putObj, delObj} from '@/api/viewgen/imgsharebindpage'
import {tableOption} from '@/const/crud/viewgen/imgsharebindpage'
import {mapGetters} from 'vuex'
import ViewGenEditor from '@/components/editor/ViewGenEditor.vue'
import store from "@/store";
import bgColorSelect from "@/views/viewgen/viewedit/component-library/pages/page-components/bgColorSelect";
import MaterialLibrary from '@/components/material/materialLibrary.vue'
import {pageUrls} from "@/components/wx-page-select/pageUrls";
import {h5HostMobile} from "@/config/env";


export default {
  name: 'imgsharebindpage',
  components: {
    ViewGenEditor,
    bgColorSelect,
    MaterialLibrary,
  },
  props: {
    obj: {type: Object},//页面ID
  },
  data() {
    return {
      pageId: this.obj.id,
      formBoxVisible: false,
      dataForm: {
        pageBase:{}
      },
      form: {},
      tableData: [],
      wxApp:{},//wxApp
      addFormRules: {
        name: [
          {required: true, message: '请填写页面名称', trigger: 'submit'}
        ],
      },
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      uploadImg: { //头图上传
        list: [],
        uploadIconVisible: true,
        processIconVisible: false,
      },
      serverUrl: "/upms/file/upload?fileType=image&dir=wx/imgShare/bindPage", // 这里写你要上传的图片服务器地址
      header: {
        Authorization: 'Bearer ' + store.getters.access_token
      } // 有的图片服务器要求请求头需要有token
    }
  },
  created() {
    this.getWxApp();
  },
  mounted () {
  },
  watch:{
    'obj.id'(newVal, oldVal){
      this.pageId = newVal;
      this.refreshChange();
    },
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.permissions['weixin:imgsharebindpage:add'] ? true : false,
        delBtn: this.permissions['weixin:imgsharebindpage:del'] ? true : false,
        editBtn: this.permissions['weixin:imgsharebindpage:edit'] ? true : false,
        viewBtn: this.permissions['weixin:imgsharebindpage:get'] ? true : false
      };
    }
  },
  methods: {
    searchChange(params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    sortChange(val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage(page, params) {
      this.tableLoading = true
      if(!params){
        params = {pageId:this.pageId};
      }else{
        params.pageId = this.pageId;
      }
      // console.log("paranm",params)
      getPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
      }, params, this.paramsSearch)).then(res => {
        this.tableData = res.data.data.records
        this.page.total = res.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this
      this.$confirm('是否确认删除此数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        return delObj(row.id)
      }).then(data => {
        _this.$message({
          showClose: true,
          message: '删除成功',
          type: 'success'
        })
        this.getPage(this.page)
      }).catch(function (err) {
      })
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      putObj(row).then(res => {
        this.$message({
          showClose: true,
          message: '修改成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      addObj(row).then(res => {
        this.$message({
          showClose: true,
          message: '添加成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    beforeUpload(file, item) {
      const isLt1M = file.size / 1024 / 1024 < 1;
      if (!isLt1M) {
        this.$message.error('上传图片大小不能超过 1MB!');
        item.uploadIconVisible = true;
        item.processIconVisible = false;
        item.uploadDisabled = false;
      }
      return isLt1M;
    },
    /**
     * 刷新回调
     */
    refreshChange() {
      this.getWxApp()
      this.getPage(this.page)
    },
    /**
     * 提交表单
     */
    submitForm() {
      console.log("请求的", this.dataForm)
      this.$refs[this.dataForm].validate((valid) => {
        if (valid) {
          if(!this.dataForm.id) {
            addObj(this.dataForm).then(res => {
              this.$message({
                showClose: true,
                message: '新增成功',
                type: 'success'
              })
              this.formBoxVisible = false;
              this.refreshChange();
            }).catch(() => {
            });
          }else {
            putObj(this.dataForm).then(res => {
              this.$message({
                showClose: true,
                message: '修改成功',
                type: 'success'
              })
              this.$refs[this.dataForm].resetFields();
              this.formBoxVisible = false;
              this.refreshChange();
            }).catch(() => {
            })
          }
        }
      });
    },
    cancelSubmit() {
      this.$refs[this.dataForm].resetFields();
      this.formBoxVisible = false;
      this.refreshChange();
    },
    /**
     * 二维码
     */
    qrCode(page) {

    },
    /**
     * 复制地址
     */
    copyPageUrl(page) {
      let url = "";
      let list =  pageUrls;//app 页面地址
      for (let i in list ) {
        if(list[i].type == "7"){
          url = h5HostMobile + list[i].url + "?bind_id=" + page.id + "&app_id=" + this.wxApp.id+"&tenant_id="+this.wxApp.tenantId;
        }
      }
      if('1' == this.wxApp.isComponent){
        url= url + '&component_appid='+this.wxApp.componentAppId;
      }
      console.log("urls",url);

      this.$copyText(url).then( e => {
        this.$message.success("复制成功")
        console.log(e)
      }, function (e) {
        this.$message.error("复制失败")
        console.log(e)
      })
    },
    openFormBox(type,obj) {
      if(type =='add') {
        this.dataForm = Object.assign({
          pageId:this.pageId,
          emailRules: '0',
          userRules: '0',
          imgUrl: '',
          content: '',
          theme:'#000000'
          }, {});
        // console.log("add", this.dataForm )
        this.formBoxVisible = true;
      }else if(type =='put') {
        this.dataForm =  Object.assign({}, obj);
        // console.log("put", this.dataForm )
        this.formBoxVisible = true;
      }
    },
    getWxApp(){
      getWxApp(this.obj.appId).then(res=>{
        console.log("公众号",res);
        this.wxApp = res.data.data;
      })
    }
  }
}
</script>

<style lang="scss" scoped>
///deep/ .avue-crud__menu {
//  display: none;
//}
.img_dialog {
  position: absolute;
  left: 80%;
  top: 0;
  width: 30px;
  opacity: 0;
}


.image_preview {
  position: relative;
  float: left;
  display: inline;
  margin: 0px 15px 10px 0px;

  .image_preview_image {
    border: 1px solid transparent;
    width: 150px;
    height: 150px;
  }

  &:hover .img_dialog {
    text-align: center;
    cursor: default;
    text-align: center;
    color: #fff;
    opacity: 1;
    font-size: 20px;
    background-color: rgba(0, 0, 0, .5);
    transition: opacity .3s;
  }
}


.waite_upload_img {
  border: 1px #8c939d dashed;
  border-radius: 6px;
  width: 150px;
  height: 150px
}
</style>
