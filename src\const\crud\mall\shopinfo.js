import {getCount} from '@/api/mall/shopinfo'

const validateName = (rule, value, callback) => {
  if (window.openType === 'edit'){
    callback()
  }else{
    getCount({
      name: value
    }).then(response => {
      let data = response.data.data
      if (data > 0) {
        callback(new Error('店铺名字已经存在'))
      } else {
        callback()
      }
    })
  }
}

export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  excelBtn: true,
  printBtn: true,
  viewBtn: true,
  labelWidth: 160,
  column: [
    {
      label: '店铺名',
      prop: 'name',
      editDisabled: true,
      rules: [
        {
          required: true,
          message: '请输入name',
          trigger: 'blur'
        },
        {
          max: 100,
          message: '长度在不能超过100个字符'
        },{
          validator: validateName,
          trigger: 'blur'
        }
      ]
    },
    {
      label: '电话号码',
      prop: 'phone',
      sortable: true,
      rules: [
        {
          required: true,
          message: '请输入phone',
          trigger: 'blur'
        },
        {
          max: 15,
          message: '长度在不能超过15个字符'
        },
      ]
    },
    {
      label: '图片',
      prop: 'imgUrl',
      width: 250,
      dataType: 'array',
      slot: true,
      formslot: true,
      rules: [{
        type: 'array',
        required: true,
        message: '图片不能为空',
        trigger: 'change'
      }]
    },
    {
      label: '地址',
      prop: 'address',
      formslot: true
    },
    {
      label: '是否启用',
      prop: 'enable',
      type: 'radio',
      search: true,
      sortable: true,
      span: 24,
      slot: true,
      rules: [{
        required: true,
        message: '请选择启用状态',
        trigger: 'blur'
      }],
      dicData: [{
        label: '关闭',
        value: '0'
      }, {
        label: '启用',
        value: '1'
      }]
    },
    {
      label: '详细介绍',
      prop: 'detail',
      sortable: true,
      hide: true,
      rules: [
        {
          max: 225,
          message: '长度在不能超过225个字符'
        },
      ]
    },
    {
      label: '微信特约商户号',
      prop: 'wxMchId',
      hide: true,
      rules: [
        {
          max: 32,
          message: '长度在不能超过32个字符'
        },
      ],
      tip: '微信支付使用必配，微信支付服务商下的特约商户号'
    },
    {
      label: '支付宝app_auth_token',
      prop: 'aliAuthToken',
      hide: true,
      rules: [
        {
          max: 64,
          message: '长度在不能超过32个字符'
        },
      ],
      tip: '支付使用必配，支付宝第三方应用商户授权的app_auth_token'
    }
  ]
}
