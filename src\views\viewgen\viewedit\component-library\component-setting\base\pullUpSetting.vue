<template>
  <div class="pull-up-setting">
    <el-form label-width="100px" size="small">
      <el-form-item label="标题">
        <el-input v-model="form.title" placeholder="请输入标题"></el-input>
      </el-form-item>
      
      <el-form-item label="最大高度">
        <el-input-number v-model="form.maxHeight" :min="10" :max="100" :step="5" controls-position="right"></el-input-number>
        <span class="unit">%</span>
      </el-form-item>
      
      <el-form-item label="圆角大小">
        <el-input-number v-model="form.borderRadius" :min="0" :max="50" :step="2" controls-position="right"></el-input-number>
        <span class="unit">px</span>
      </el-form-item>
      
      <el-form-item label="确认按钮">
        <el-input v-model="form.confirmText" placeholder="请输入确认按钮文本"></el-input>
      </el-form-item>
      
      <el-form-item label="显示选项">
        <el-radio-group v-model="form.displayType">
          <el-radio :label="'image'">图片</el-radio>
          <el-radio :label="'text'">文字</el-radio>
          <el-radio :label="'both'">图片+文字</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="商品选择">
        <el-button type="primary" size="small" @click="showGoodsDrawer">选择商品</el-button>
        <div v-if="form.selectedGoods" class="selected-goods">
          <span>已选商品: {{form.selectedGoods.name || '未命名商品'}}</span>
        </div>
      </el-form-item>
      
      <!-- <el-form-item>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </el-form-item> -->
    </el-form>
    
    <!-- 商品选择抽屉 -->
    <el-drawer
      :append-to-body="true"
      title="选择商品"
      :visible.sync="drawerVisible"
      direction="rtl"
      size="80%"
      :before-close="handleDrawerClose">
      <div class="goods-drawer-content">
        <goods-list v-if="drawerVisible" @select-goods="handleSelectGoods"></goods-list>
      </div>
    </el-drawer>
  </div>
</template>

<script>
export default {
  name: "pullUpSetting",
  components: {
    GoodsList: () => import('@/views/mall/goodsspu/goodsSelector.vue')
  },
  props: {
    showData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    config: {
      type: Object,
      default: () => {
        return {}
      }
    },
    appId: {
      type: String,
      default: ''
    },
    thememobile: {
      type: Object,
      default: () => {
        return {}
      }
    },
    clientType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      form: {
        title: '请选择',
        confirmText: '确定',
        maxHeight: 50,
        borderRadius: 20,
        displayType: 'both',
        selectedGoods: null
      },
      drawerVisible: false
    }
  },
  created() {
    // 初始化数据，如果有传入数据则使用传入数据
    if (this.showData && Object.keys(this.showData).length > 0) {
      this.form = JSON.parse(JSON.stringify(this.showData));
      // 确保数值类型正确
      this.form.maxHeight = Number(this.form.maxHeight || 50);
      this.form.borderRadius = Number(this.form.borderRadius || 20);
      // 确保selectedGoods字段存在
      if (!this.form.selectedGoods) {
        this.form.selectedGoods = null;
      }
      // 确保displayType字段存在
      if (!this.form.displayType) {
        this.form.displayType = 'both';
      }
    }
  },
  methods: {
    handleConfirm() {
      this.$emit('confirm', this.form);
    },
    handleCancel() {
      this.$emit('cancel');
    },
    handleUpdate() {
      this.$emit('update', this.form);
    },
    showGoodsDrawer() {
      this.drawerVisible = true;
    },
    handleDrawerClose(done) {
      done();
    },
    handleSelectGoods(goods) {
      this.form.selectedGoods = goods;
      this.drawerVisible = false;
    }
  },
  watch: {
    form: {
      handler() {
        this.handleUpdate();
      },
      deep: true
    }
  }
}
</script>

<style lang="less" scoped>
.pull-up-setting {
  padding: 20px;
  
  .unit {
    margin-left: 5px;
    color: #606266;
  }
  
  .selected-goods {
    margin-top: 10px;
    padding: 8px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .goods-drawer-content {
    height: 100%;
    overflow: auto;
    position: relative;
    z-index: 2100;
  }
}
</style> 