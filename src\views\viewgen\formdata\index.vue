<template>
  <div class="execution">
    <basic-container>
      <avue-crud ref="crud"
                 :page="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="showTableOption"
                 v-model="form"
                 @on-load="getPage"
                 @refresh-change="refreshChange"
                 @row-update="handleUpdate"
                 @row-save="handleSave"
                 @row-del="handleDel"
                 @sort-change="sortChange"
                 @search-change="searchChange">
      </avue-crud>
    </basic-container>
  </div>
</template>

<script>
import {getPage, getObj, addObj, putObj, delObj} from '@/api/viewgen/wxformdata'
import {getObj as getPageData} from '@/api/viewgen/pagedevise'
import {tableOption} from '@/const/crud/viewgen/wxformdata'
import {mapGetters} from 'vuex'

export default {
  name: 'wxformdata',
  data() {
    return {
      id: "",
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      pageData: "",
      paramsSearch: {},
      tableLoading: false,
      showTableOption: {}
    }
  },
  created() {
    this.initPage();
  },
  mounted() {
    window.localStorage.setItem('formDataId', this.id);
  },
  destroyed() {
    // 考虑用vuex管理    只存不删... 可能此页面后期独立出来 暂不处理
    // window.localStorage.removeItem('formDataId');
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.permissions['weixinapi:wxformdata:add'] ? true : false,
        delBtn: this.permissions['weixinapi:wxformdata:del'] ? true : false,
        editBtn: this.permissions['weixinapi:wxformdata:edit'] ? true : false,
        viewBtn: this.permissions['weixinapi:wxformdata:get'] ? true : false
      };
    }
  },
  methods: {
    initPage(){
      if (this.$route.params.id) {
        this.id = this.$route.params.id;
        window.localStorage.setItem('formDataId', this.id);
      } else {
        //只存不删... 可能此页面后期独立出来 暂不处理
        this.id = window.localStorage.getItem('formDataId');
      }
      this.showTableOption={};
      getPageData(this.id).then(res => {
        this.pageData = res.data.data;
        if(this.pageData){
          //傻瓜的对象深拷贝  使用 Object.assign()无效的结果
          let obj =  JSON.parse(JSON.stringify(tableOption));
          let list = this.pageData.pageComponent.componentsList;
          for (let i = 0; i < list.length; i++) {
            let columnObj ={
              label: '',
              prop: '',
              sortable: false,
            }
            if(list[i].config.type==4 && list[i].config.ableSubmit ){
              columnObj.label =  list[i].data.title;
              columnObj.prop =  list[i].id+"";
              obj.column.push(columnObj);
            }
          };
          this.showTableOption = obj;
          // console.log("列表值", this.showTableOption.column.length);
        }
      }).catch(() => {

      });
      // 监听页面刷新,保存当前ID
      window.addEventListener('beforeunload', e => {
        window.localStorage.setItem('formDataId', this.id);
      });
    },
    searchChange(params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    sortChange(val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage(page, params) {
      console.log("tableData12",this.tableData)
      this.tableLoading = true
      getPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
      }, params, this.paramsSearch)).then(res => {
        console.log("1撒大",res.data)
        this.tableData = res.data.data.records;
        for (let i = 0; i < this.tableData.length ; i++) {
          let objList= JSON.parse(this.tableData[i].data);
          for (let j = 0; j < objList.length; j++) {
            this.$set(this.tableData[i],objList[j].id+'',objList[j].value)
          }
        }
        console.log("tableData",this.tableData)
        this.page.total = res.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(err   => {
        console.log("dadasdas",err)
        this.tableLoading = false
      })
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this
      this.$confirm('是否确认删除此数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        return delObj(row.id)
      }).then(data => {
        _this.$message({
          showClose: true,
          message: '删除成功',
          type: 'success'
        })
        this.getPage(this.page)
      }).catch(function (err) {
      })
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page)
    },
    handleUpdate() {

    },
    handleSave() {

    }
  }
}
</script>

<style lang="scss" scoped>
</style>
