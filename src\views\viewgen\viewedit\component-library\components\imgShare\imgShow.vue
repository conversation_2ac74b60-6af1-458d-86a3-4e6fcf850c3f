<template>
  <div class="imgShowComponent"
       :style="{marginBottom: `${setData.pageMarginBottom}px`,marginTop: `${setData.pageMarginTop}px`,marginLeft: `${setData.pageMarginLeft}px`,marginRight: `${setData.pageMarginRight}px`}">
    <div class="block" >
      <el-image fit="fill"
                :style="{backgroundColor:setData.imgBackGround, paddingBottom: `${setData.imgPaddingBottom}px`,paddingTop: `${setData.imgPaddingTop}px`}"
                style="height: 200px;display: flex;align-items: center;justify-content: center;background-color: transparent">
        <div slot="error" class="image-slot"  >
          <i class="el-icon-picture-outline">图片展示</i>
        </div>
      </el-image>
    </div>
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';


export default {
  data() {
    return {};
  },
  components: {},
  props: {
    theme: {type: Object | Array},
    setData: {type: Object | Array},
    cId: {type: Number},
    noEditor: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {
  },
  mounted() {
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
  },
  watch: {
    setData(newVal, oldVal) {
    },
    componentsList(newVal, oldVal) {          //添加的时候出发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    }
  },
  beforeDestroy() {
    // this.$root.Bus.$off('addHotSpot')
  }
};
</script>
<style lang='less' scoped>

@import '../../colorui/main.css';
@import '../../colorui/icon.css';

.image-slot{
  border: 1px black solid;
}
</style>
