import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/mall/usershop/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/mall/usershop',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/mall/usershop/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/mall/usershop/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/mall/usershop',
        method: 'put',
        data: obj
    })
}

export function getCount(query) {
  return request({
    url: '/mall/usershop/count',
    method: 'get',
    params: query
  })
}

export function getStatistics(query) {
  return request({
    url: '/mall/usershop/statistics',
    method: 'get',
    params: query
  })
}
