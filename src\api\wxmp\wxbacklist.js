
import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/wxuserbacklist/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/weixin/wxuserbacklist',
    method: 'post',
    data: obj
  })
}
export function addByTag(obj) {
  return request({
    url: '/weixin/wxuserbacklist/add/tag',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/weixin/wxuserbacklist/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/wxuserbacklist/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/wxuserbacklist',
    method: 'put',
    data: obj
  })
}
