<template>
  <div class="pull-up-component">
    <!-- 直接显示面板，不需要触发器 -->
    <div class="pull-up-panel" 
         :style="{
           maxHeight: setData.maxHeight ? setData.maxHeight + '%' : '50%',
           borderRadius: setData.borderRadius ? setData.borderRadius + 'px' : '20px'
         }">
      <div class="panel-header">
        <div class="panel-title">{{ setData.title || '请选择' }}</div>
      </div>
      <div class="panel-content">
        <!-- 商品信息区域 -->
        <div class="goods-info" v-if="setData.selectedGoods">
          <div class="goods-image">
            <img :src="setData.selectedGoods.picUrls && setData.selectedGoods.picUrls.length > 0 ? setData.selectedGoods.picUrls[0] : ''" alt="商品图片">
          </div>
          <div class="goods-details">
            <div class="goods-name">{{ setData.selectedGoods.name }}</div>
            <div class="goods-price">¥{{ setData.selectedGoods.priceDown }}</div>
          </div>
        </div>
        
        <div class="panel-footer">
          <div class="confirm-btn">{{ setData.confirmText || '确定' }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "pullUpComponent",
  props: {
    setData: {
      type: Object,
      default: () => {
        return {
          title: '请选择',
          confirmText: '确定',
          maxHeight: '50',
          borderRadius: '20',
          displayType: 'both',
          selectedGoods: null
        }
      }
    },
    config: {
      type: Object,
      default: () => {
        return {}
      }
    },
    cId: {
      type: [String, Number],
      default: ''
    },
    thememobile: {
      type: Object,
      default: () => {
        return {}
      }
    }
  }
}
</script>

<style lang="less" scoped>
.pull-up-component {
  width: 100%;
  position: relative;
  
  .pull-up-panel {
    width: 100%;
    background-color: #fff;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    
    .panel-header {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 15px;
      border-bottom: 1px solid #eee;
      
      .panel-title {
        font-size: 16px;
        font-weight: 500;
      }
    }
    
    .panel-content {
      flex: 1;
      overflow-y: auto;
      
      .goods-info {
        display: flex;
        padding: 15px;
        border-bottom: 1px solid #eee;
        
        .goods-image {
          width: 80px;
          height: 80px;
          margin-right: 10px;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 4px;
          }
        }
        
        .goods-details {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          
          .goods-name {
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 5px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
          }
          
          .goods-price {
            font-size: 16px;
            color: #ff6b00;
            font-weight: bold;
          }
        }
      }
      
      .panel-footer {
        padding: 15px;
        
        .confirm-btn {
          background-color: #ff6b00;
          color: #fff;
          text-align: center;
          padding: 10px;
          border-radius: 4px;
          font-size: 15px;
        }
      }
    }
  }
}
</style> 