<template>
  <div class="execution">
    <basic-container>
      <avue-crud ref="crud"
                 :page.sync="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 v-model="form"
                 @on-load="getPage"
                 @refresh-change="refreshChange"
                 @row-update="handleUpdate"
                 @row-save="handleSave"
                 @row-del="handleDel"
                 @sort-change="sortChange"
                 @search-change="searchChange">
        <template slot="keyPathForm"
                  slot-scope="scope">
          <div v-if="scope.row.keyPath">
            <el-tag closable @close="keyPathRemove">
              {{scope.row.keyPath.slice(0,60)}}
            </el-tag>
          </div>
          <el-upload
            v-if="!scope.row.keyPath"
            :action="'/payapi/payconfig/cert/upload'"
            :headers="headers"
            :limit="1"
            :on-success="uploadKeySuccess">
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">请上传apiclient_cert.p12</div>
          </el-upload>
        </template>
        <template slot="privateKeyPathForm"
                  slot-scope="scope">
          <div v-if="scope.row.privateKeyPath">
            <el-tag closable @close="privateKeyPathRemove">
              {{scope.row.privateKeyPath.slice(0,60)}}
            </el-tag>
          </div>
          <el-upload
            v-if="!scope.row.privateKeyPath"
            :action="'/payapi/payconfig/cert/upload'"
            :headers="headers"
            :limit="1"
            :on-success="uploadPrivateKeySuccess">
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">请上传apiclient_key.pem</div>
          </el-upload>
        </template>
        <template slot="privateCertPathForm"
                  slot-scope="scope">
          <div v-if="scope.row.privateCertPath">
            <el-tag closable @close="privateCertPathRemove">
              {{scope.row.privateCertPath.slice(0,60)}}
            </el-tag>
          </div>
          <el-upload
            v-if="!scope.row.privateCertPath"
            :action="'/payapi/payconfig/cert/upload'"
            :headers="headers"
            :limit="1"
            :on-success="uploadPrivateCertSuccess">
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">请上传apiclient_cert.pem</div>
          </el-upload>
        </template>
        <template slot="type"
                  slot-scope="scope">
          <div v-if="scope.row.type == '1'"><img class="pay-img" src="/img/wx-pay.png"></div>
          <div v-if="scope.row.type == '2'"><img class="pay-img" src="/img/ali-pay.png"></div>
        </template>
        <template slot="typeForm"
                  slot-scope="scope">
          <el-radio-group v-model="scope.row.type" size="small">
            <el-radio border
                      v-for="item in dicDataType"
                      :key="item.id"
                      :label="item.id"
                      :disabled="item.disabled">
              {{item.name}}
            </el-radio>
          </el-radio-group>
        </template>
      </avue-crud>
    </basic-container>
  </div>
</template>

<script>
import {getPage, getObj, addObj, putObj, delObj} from '@/api/payapi/payconfig'
import {tableOption} from '@/const/crud/wxpayconfig/payapplyform'
import {mapGetters} from 'vuex'
import store from "@/store"

export default {
  name: 'applypayment',
  data() {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      headers: {
        Authorization: 'Bearer ' + store.getters.access_token
      },
      dicDataType: [{
        name: '微信支付服务商',
        id: '1'
      }
        // ,{
        //   name: '支付宝服务商',
        //   id: '2'
        // }
      ]
    }
  },
  created() {
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.permissions['payapi:payconfig:add'] ? true : false,
        delBtn: this.permissions['payapi:payconfig:del'] ? true : false,
        editBtn: this.permissions['payapi:payconfig:edit'] ? true : false,
        viewBtn: this.permissions['payapi:payconfig:get'] ? true : false
      };
    }
  },
  methods: {

  }
}
</script>

<style lang="scss" scoped>
.pay-img{
  width: 100px;
  height: 80px;
}
</style>
