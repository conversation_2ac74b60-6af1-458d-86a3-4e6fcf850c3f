import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/mall/distributionconfig/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/mall/distributionconfig',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/mall/distributionconfig/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/mall/distributionconfig/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/mall/distributionconfig',
        method: 'put',
        data: obj
    })
}

export function getObj2() {
  return request({
    url: '/mall/distributionconfig',
    method: 'get'
  })
}
