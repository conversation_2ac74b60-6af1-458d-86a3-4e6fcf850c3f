<template>
  <div class="execution">
    <!--    <basic-container>-->
    <el-row :gutter="10">
      <el-col :span="8">
        <el-form class="interior_from" :ref="interiorFrom" :model="interiorFrom"  :rules="dataFormRules" label-width="100px"
                 size="medium">
          <el-form-item label="操作时间" prop="handleTime">
            <el-date-picker
              v-model="interiorFrom.handleTime"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择日期时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="操作时长" prop="duration">
            <el-input-number v-model="interiorFrom.duration" controls-position="right" :min="1"
                             :max="100">
            </el-input-number>分钟
          </el-form-item>
          <el-form-item label="进入数量" prop="enterNum">
            <el-input-number v-model="interiorFrom.enterNum" controls-position="right" :min="1"
                             :max="99999"></el-input-number>
          </el-form-item>
          <el-form-item label="支付数量"  prop="payNum">
            <el-input-number v-model="interiorFrom.payNum" controls-position="right" :min="1"
                             :max="99999"></el-input-number>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm">提交</el-button>
            <el-button @click="resetForm">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="16">
        <avue-crud ref="crud"
                   :page="page"
                   :data="tableData"
                   :permission="permissionList"
                   :table-loading="tableLoading"
                   :option="tableOption"
                   v-model="form"
                   @on-load="getPage"
                   @refresh-change="refreshChange"
                   @row-update="handleUpdate"
                   @row-save="handleSave"
                   @row-del="handleDel"
                   @sort-change="sortChange"
                   @search-change="searchChange">
        </avue-crud>
      </el-col>
    </el-row>
    <!--    </basic-container>-->
  </div>
</template>

<script>
import {getPage, getObj, addObj, putObj, delObj} from '@/api/viewgen/internaldata'
import {tableOption} from '@/const/crud/viewgen/internaldata'
import {mapGetters} from 'vuex'

export default {
  name: 'internaldata',
  props:{
    pageId: [String],
  },
  data() {
    return {
      interiorFrom: {},//提交表单
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      dataFormRules: {
        handleTime: [
          {required: true, message: '请填写操作时间', trigger: 'blur'},
        ],
        duration: [
          {required: true, message: '请填写操作时长', trigger: 'blur'},
        ],
        enterNum: [
          {required: true, message: '请填写进入数量', trigger: 'blur'},
        ],
        payNum: [
          {required: true, message: '请填写支付数量', trigger: 'blur'},
        ],
      },
    }
  },
  created() {
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.permissions['weixin:internaldata:add'] ? true : false,
        delBtn: this.permissions['weixin:internaldata:del'] ? true : false,
        editBtn: this.permissions['weixin:internaldata:edit'] ? true : false,
        viewBtn: this.permissions['weixin:internaldata:get'] ? true : false
      };
    }
  },
  methods: {
    searchChange(params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    sortChange(val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage(page, params) {
      this.tableLoading = true
      getPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
      }, params, this.paramsSearch)).then(response => {
        this.tableData = response.data.data.records
        this.page.total = response.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this
      this.$confirm('是否确认删除此数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        return delObj(row.id)
      }).then(data => {
        _this.$message({
          showClose: true,
          message: '删除成功',
          type: 'success'
        })
        this.getPage(this.page)
      }).catch(function (err) {
      })
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      putObj(row).then(response => {
        this.$message({
          showClose: true,
          message: '修改成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      addObj(row).then(response => {
        this.$message({
          showClose: true,
          message: '添加成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * 刷新回调
     */
    refreshChange() {
      console.log("地方 是 ")
      this.getPage(this.page)
    },
    submitForm() {
      this.$refs[this.interiorFrom].validate((valid) => {
        if (valid) {
          this.interiorFrom.pageId = this.pageId;
          addObj(this.interiorFrom).then(response => {
            this.$message({
              showClose: true,
              message: '添加成功',
              type: 'success'
            })
            this.interiorFrom = {};
            this.getPage(this.page)
          }).catch(() => {
          })
        } else {
          return false;
        }
      });
    },
    resetForm() {
      this.interiorFrom = {};
      this.$refs[this.interiorFrom].resetFields();
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .avue-crud__menu {
  display: none;
}

.interior_from {
  padding-top: 15px;
  border: 1px solid #DCDFE6;
  box-shadow: 1px 1px 10px rgba(0, 0, 0, 0.1);
}
</style>
