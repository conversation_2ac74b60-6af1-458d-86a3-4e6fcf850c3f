import Layout from '@/page/index/'

export default [{
  path: '/wel',
  component: Layout,
  redirect: '/wel/index',
  children: [{
    path: 'index',
    name: '首页',
    component: () =>
      import ( /* webpackChunkName: "views" */ '@/page/wel')
  }]
},
  {
    path: '/info',
    component: Layout,
    redirect: '/info/index',
    children: [{
      path: 'index',
      name: '个人信息',
      component: () =>
        import ( /* webpackChunkName: "page" */ '@/views/upms/user/info'),
    },
    ]
  },
  {
    path: '/mall/config/decorate/home/<USER>',
    component: Layout,
    children: [{
      path: 'addPagePC',
      name: '商城PC页面装修',
      component: () => import ( /* webpackChunkName: "divpagePC" */ '@/views/mall/pagedevise/pc-component-library/pages/divpage.vue'),
      meta: {
        hideInMenu: true,//是否单独页面：不加载左侧菜单和顶部菜单
        keepAlive: false,
        isAuth: true,
        repeatAddTag: false,
        activeName: 'addDivPagePC'
      }
    },]
  },

  {
    path: '/mall/config/decorate/home/<USER>',
    component: Layout,
    children: [{
      path: 'addPage',
      name: '商城页面装修',
      component: () =>
        import ( /* webpackChunkName: "divpage" */ '@/views/mall/pagedevise/component-library/pages/divpage.vue'),
      meta: {
        keepAlive: false,
        isAuth: true,
        repeatAddTag: false,
        activeName: 'addDivPage'
      }
    },]
  },

  {
    path: '/mall/config/decorate/home/<USER>',
    component: Layout,
    children: [{
      path: 'addPageShopPC',
      name: '店铺PC页面装修',
      component: () =>  import ( /* webpackChunkName: "divpageShopPC" */ '@/views/mall/pagedevise/pc-component-library-shop/pages/divpage.vue'),
      meta: {
        hideInMenu: true,//是否单独页面：不加载左侧菜单和顶部菜单
        keepAlive: false,
        isAuth: true,
        repeatAddTag: false,
        activeName: 'addDivPageShopPC'
      }
    },]
  },
  {
    path: '/mall/config/decorate/home/<USER>',
    component: Layout,
    children: [{
      path: 'addPageShop',
      name: '店铺页面装修',
      component: () =>
        import ( /* webpackChunkName: "divpageShop" */ '@/views/mall/pagedevise/component-library-shop/pages/divpage.vue'),
      meta: {
        keepAlive: false,
        isAuth: true,
        repeatAddTag: false,
        activeName: 'addDivPageShop'
      }
    },]
  },
  {
    path: '/viewgen/manager/index',
    component: Layout,
    children: [{
      path: 'addView',
      name: '页面装修',
      component: () =>
        import ( /* webpackChunkName: "divpageShop" */ '@/views/viewgen/viewedit/component-library/pages/divpage.vue'),
      meta: {
        keepAlive: false,
        isAuth: true,
        repeatAddTag: false,
        activeName: 'addDivView'
      }
    },
    ]
  },
  {
    path: '/viewgen/formData/index',
    component: Layout,
    children: [{
      path: 'formData',
      name: '表单数据',
      component: () => import ( /* webpackChunkName: "divpageShop" */ '@/views/viewgen/formdata/index.vue'),
      meta: {
        keepAlive: false,
        isAuth: true,
        repeatAddTag: false,
        activeName: 'addDivView'
      }
    },
    ]
  },
  {
    path: '/weixin/viewgen/',
    component: Layout,
    children: [{
      path: 'groupondata',
      name: '拼团数据',
      component: () => import ( /* webpackChunkName: "divpageShop" */ '@/views/viewgen/groupondata/index.vue'),
      meta: {
        keepAlive: false,
        isAuth: true,
        repeatAddTag: false,
        activeName: 'addGrouponData'
      }
    },
    ]
  },
  {
    path: '/weixin/viewgen/',
    component: Layout,
    children: [{
      path: 'wxgoogdssortdetail',
      name: '排序详情',
      component: () => import ( /* webpackChunkName: "divpageShop" */ '@/views/viewgen/goodssortdetail/index.vue'),
      meta: {
        keepAlive: false,
        isAuth: true,
        repeatAddTag: false,
        activeName: 'addGrouponData'
      }
    },
    ]
  },

  // {
  //   path: '/mall/goods/goodsappraises',
  //   name: '商品评论',
  //   component: Layout,
  //   children: [{
  //     path: ':goodsAppraises',
  //     component: () =>
  //       import( /* webpackChunkName: "views" */ '@/views/mall/goodsappraises')
  //   }]
  // }

]
