export const tableOption = {
  dialogDrag: true,
  border: true,
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  excelBtn: false,
  printBtn: false,
  delBtn: false,
  addBtn: false,
  viewBtn: false,
  editBtn: false,
  dateBtn: true,
  menu: false,
  searchMenuSpan: 6,
  column: [
    {
      label: '评价时间',
      prop: 'createTime',
      sortable: true,
      width: 250,
      display: false
    },
    {
      label: '店铺',
      prop: 'shopId',
      type: 'select',
      search: true,
      filterable: true,
      display: false,
      cascaderItem: ['spuId'],
      props: {
        label: 'name',
        value: 'id'
      },
      dicUrl: '/mall/shopinfo/list'
    },
    {
      label: '商品',
      prop: 'spuId',
      type: 'select',
      search: true,
      display: false,
      hide: true,
      filterable: true,
      props: {
        label: 'name',
        value: 'id'
      },
      dicUrl: '/mall/goodsspu/list?shopId={{key}}'
    },
	  {
      label: '用户',
      prop: 'nickName',
      editDisplay: false,
      slot:true,
    },
	  {
      label: '商品信息',
      prop: 'specInfo',
      width: 200,
      slot: true,
      editDisplay: false,
      align: 'left'
    },
    {
      label: '订单信息',
      prop: 'orderItem',
      width: 240,
      slot: true,
      editDisplay: false,
      align: 'left'
    },
	  {
      label: '首评',
      prop: 'appraises',
      sortable:true,
      slot:true,
      editDisplay: false,
      align: 'left'
    },
	  {
      label: '追评',
      prop: 'appraises2',
      slot:true,
      formslot: true,
      align: 'left'
    }
  ]
}
