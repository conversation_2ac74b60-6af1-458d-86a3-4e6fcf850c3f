<!--商品分类标签-->
<template>
  <div class="cuttingLineSetting">
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">音乐控件</p>
      <div slot="mainContent">
        <el-form ref="form" label-width="100px" :model="formData">
          <el-divider>基础属性</el-divider>
          <el-alert
            title="提示:"
            type="warning"
            description="请填写实际能播放的音乐地址。"
            center
            :closable="false">
          </el-alert>
          <el-form-item label="音乐地址" >
            <el-input  v-model="formData.url" size="mini" type="text" placeholder="请填写音乐地址">
            </el-input>
          </el-form-item>
        </el-form>
      </div>
    </settingSlot>
  </div>

</template>
<script>

import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';
import settingSlot from '../settingSlot';

export default {
  components: {settingSlot},
  data() {
    return {
      formDataCopy: {
        url:'',
      },
      formData: {}
    };
  },
  props: {
    showData: {
      type: Object,
      default: () => {
      }
    },
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
      clickComIndex: state => state.divpage.clickComIndex,
    })
  },
  mounted() {
    let that = this;
    if (that.IsEmptyObj(that.showData)) {
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
    } else {
      that.formData = that.showData
    }
    that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
    // that.updateData({
    //   componentsList: that.componentsList
    // })
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    // 删除按钮
    delBtn(index) {
      let that = this;
      that.$confirm('是否删除该按钮?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
        that.updateData({componentsList: that.componentsList});
      }).catch(() => {
      })
    },
    cancel() {
      this.$emit('cancel')
    },
    reset() {
      let that = this;
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      that.componentsList[that.clickComIndex]['data'] = this.formData;
    },
    confirm() {
      this.$emit('confirm', this.formData)
    },
    delete() {
      this.$emit('delete')
    },
  },
  watch: {
    showData: {
      handler(newVal, oldVal) {
        this.formData = newVal ? newVal : this.formData;
      },
      deep: true
    },
    thememobile() {
    },
  }
};
</script>
<style lang='less' scoped>

.el-form-item {
  margin-bottom: 0;
}

</style>
