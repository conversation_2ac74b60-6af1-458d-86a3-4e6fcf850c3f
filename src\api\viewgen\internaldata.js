import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/internaldata/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/weixin/internaldata',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/weixin/internaldata/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/internaldata/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/internaldata',
    method: 'put',
    data: obj
  })
}
