<!--商品分类标签-->
<template>
  <div class="categorySetting">
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">作品分类</p>
      <div slot="mainContent">
        <el-form ref="form" label-width="100px" :model="formData">
          <el-divider>基础属性</el-divider>
          <el-form-item label="导航样式">
            <el-radio-group v-model="formData.navStyle">
              <el-radio :label="0">横向导航</el-radio>
              <el-radio :label="1">竖向导航</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="初始背景颜色">
            <bg-color-select :thememobile="thememobile" :bgValue="formData.background"
                             @onChange="formData.background = $event"></bg-color-select>
          </el-form-item>
          <el-form-item label="初始字体颜色">
            <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
              <el-input v-model="formData.fontColor" size="small">
                <template slot="append">
                  <el-color-picker size="mini" v-model="formData.fontColor"></el-color-picker>
                </template>
              </el-input>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="初始字体大小">
            <el-input-number v-model="formData.fontSize" size="small" controls-position="right" :min="12"
                             :max="30"></el-input-number>
            px
          </el-form-item>
          <el-form-item label="选中字体颜色">
            <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
              <el-input v-model="formData.selectedFontColor" size="small">
                <template slot="append">
                  <el-color-picker size="mini" v-model="formData.selectedFontColor"></el-color-picker>
                </template>
              </el-input>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="选中字体大小">
            <el-input-number v-model="formData.selectedFontSize" size="small" controls-position="right" :min="12"
                             :max="30"></el-input-number>
            px
          </el-form-item>
          <el-form-item label="标题图标">
            <icon-select style="float:left;margin-right: 10px;"  @onChangeIcon="formData.titleIcon = $event"></icon-select>
            <div :style="{color: `${formData.titleColor}`}" :class="formData.titleIcon"></div>
          </el-form-item>

          <el-divider>导航规则</el-divider>
          <el-form-item label="全部排序" >
            <el-popover
              placement="top-start"
              width="450"
              trigger="click">
              <div >
                <h3>提示：</h3>
                <div>全部排序为所有内容指定排序规则，若需要更细粒度的排序规则，请在类别排序中设置。 优先级：默认排序<全部排序<类别排序</div>
                <div>默认排序为作品上架时间</div>
              </div>
              <el-button class="warning_msg_btn" slot="reference" type="warning"  size="mini" icon="el-icon-question" circle></el-button>
            </el-popover>
            <el-tag @click="openSortBox(2,null)" v-if="formData.sortRule && formData.sortRule.id"> {{formData.sortRule.name}}</el-tag>
            <el-button  @click="deleteSort(2,null)"  v-if="formData.sortRule && formData.sortRule.id" icon="el-icon-delete"  type="danger" size="mini"></el-button>
            <el-button @click="openSortBox(2,null)" v-if="!formData.sortRule || !formData.sortRule.id" icon="el-icon-sort" size="mini"></el-button>
          </el-form-item>
          <el-form-item label="显示方式" >
            <el-popover
              placement="top-start"
              width="450"
              trigger="click">
              <div >
                <h3>提示：</h3>
                <div>自动读取：自动加载系统标签分类，请先设置好系统的标签分类</div>
              </div>
              <el-button class="warning_msg_btn"  slot="reference" type="warning"  size="mini" icon="el-icon-question" circle></el-button>
            </el-popover>
            <el-radio-group v-model="formData.showType" @change="showTypeChange">
              <el-radio :label="0">自动读取</el-radio>
              <el-radio :label="1">自定义设置</el-radio>
            </el-radio-group>
          </el-form-item>
          <draggable v-show="formData.showType==0" v-model="formData.initCategoryList" @start="datadragStart"
                     @update="datadragUpdate" @end="datadragEnd"
                     :move="datadragMove" :options="{filter:'.notDraggable', preventOnFilter: false, animation:500}">
            <transition-group>

              <div  v-for="(item,index) in formData.initCategoryList" :key="index" class="drag-item">
                <el-form-item label="导航名称">
                  <p>{{ item.name }}</p>
                </el-form-item>
                <el-form-item label="类别排序" >
                  <el-tag @click="openSortBox(0,index)" v-if="item.sortRule.id"> {{item.sortRule.name}}</el-tag>
                  <el-button  @click="deleteSort(0 ,index)"  v-if="item.sortRule.id" icon="el-icon-delete"  type="danger" size="mini"></el-button>
                  <el-button @click="openSortBox(0,index)" v-if="!item.sortRule.id " icon="el-icon-sort" size="mini"></el-button>
                </el-form-item>
                <el-form-item label="背景图片">
                  <MaterialList :value="item.imgUrl?[item.imgUrl]:[]"
                              @sureSuccess="item.imgUrl = $event?$event[0]:''"
                              @deleteMaterial="item.imgUrl = ''"
                              type="image"
                              :num=1
                              :divStyle="'width:100%;height:95px;margin-bottom:8px;line-height: 100px;'"></MaterialList>
                </el-form-item>
                <el-form-item label="图片地址">
                  <el-input v-model="item.imgUrl" size="mini" type="text" style="margin-top: 5px">
                  </el-input>
                </el-form-item>
                <el-form-item label="选中后背景">
                  <MaterialList :value="item.selectedImgUrl?[item.selectedImgUrl]:[]"
                              @sureSuccess="item.selectedImgUrl = $event?$event[0]:''"
                              @deleteMaterial="item.selectedImgUrl = ''"
                              type="image"
                              :num=1
                              :divStyle="'width:100%;height:95px;margin-bottom:8px;line-height: 100px;'"></MaterialList>
                </el-form-item>
                <el-form-item label="选中图片地址">
                  <el-input v-model="item.selectedImgUrl" size="mini" type="text" style="margin-top: 5px">
                  </el-input>
                </el-form-item>
                <el-form-item label="背景尺寸">
                  <el-select v-model="item.backgroundSize" placeholder="请选择背景尺寸" size="mini">
                    <el-option label="原始大小" value="auto"></el-option>
                    <el-option label="包含" value="contain"></el-option>
                    <el-option label="覆盖" value="cover"></el-option>
                    <el-option label="100%" value="100% 100%"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="背景位置">
                  <el-select v-model="item.backgroundPosition" placeholder="请选择背景位置" size="mini">
                    <el-option label="居中" value="center"></el-option>
                    <el-option label="左上" value="left top"></el-option>
                    <el-option label="左中" value="left center"></el-option>
                    <el-option label="左下" value="left bottom"></el-option>
                    <el-option label="右上" value="right top"></el-option>
                    <el-option label="右中" value="right center"></el-option>
                    <el-option label="右下" value="right bottom"></el-option>
                    <el-option label="顶部居中" value="center top"></el-option>
                    <el-option label="底部居中" value="center bottom"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="背景重复">
                  <el-select v-model="item.backgroundRepeat" placeholder="请选择背景重复方式" size="mini">
                    <el-option label="不重复" value="no-repeat"></el-option>
                    <el-option label="水平重复" value="repeat-x"></el-option>
                    <el-option label="垂直重复" value="repeat-y"></el-option>
                    <el-option label="重复" value="repeat"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="跳转设置">
                  <wx-page-select
                    class="notDraggable"
                    :isSystemUrl="item.isSystemUrl"
                    @switchChange="item.isSystemUrl=$event"
                    :page="item.pageUrl"
                    @change="item.pageUrl=$event"></wx-page-select>
                </el-form-item>
              </div>
            </transition-group>
          </draggable>

          <draggable v-show="formData.showType==1" v-model="formData.categoryList" @start="datadragStart"
                     @update="datadragUpdate" @end="datadragEnd"
                     :move="datadragMove" :options="{filter:'.notDraggable', preventOnFilter: false, animation:500}">
            <transition-group>
              <div v-for="(item,index) in formData.categoryList" :key="'dragItem'+index" class="drag-item ">
                <div style="height: 30px">
                  <el-button class="notDraggable" style="float: right" @click="delMenu(index)"  icon="el-icon-delete" size="mini" type="danger"></el-button>
                </div>
                <el-form-item label="导航名称">
                  <el-input class="menu_list_title notDraggable" :draggable="false" v-model="item.name" size="mini"></el-input>
                </el-form-item>
                <el-form-item label="类别排序">
                  <el-tag @click="openSortBox(1,index)" v-if="item.sortRule.id"> {{item.sortRule.name}}</el-tag>
                  <el-button  @click="deleteSort(1 ,index)"  v-if="item.sortRule.id" icon="el-icon-delete"  type="danger" size="mini"></el-button>
                  <el-button @click="openSortBox(1,index)" v-if="!item.sortRule.id " icon="el-icon-sort" size="mini"></el-button>
                </el-form-item>
                <el-form-item label="背景图片">
                  <MaterialList :value="item.imgUrl?[item.imgUrl]:[]"
                              @sureSuccess="item.imgUrl = $event?$event[0]:''"
                              @deleteMaterial="item.imgUrl = ''"
                              type="image"
                              :num=1
                              :divStyle="'width:100%;height:95px;margin-bottom:8px;line-height: 100px;'"></MaterialList>
                </el-form-item>
                <el-form-item label="图片地址">
                  <el-input v-model="item.imgUrl" size="mini" type="text" style="margin-top: 5px">
                  </el-input>
                </el-form-item>
                <el-form-item label="选中后背景">
                  <MaterialList :value="item.selectedImgUrl?[item.selectedImgUrl]:[]"
                              @sureSuccess="item.selectedImgUrl = $event?$event[0]:''"
                              @deleteMaterial="item.selectedImgUrl = ''"
                              type="image"
                              :num=1
                              :divStyle="'width:100%;height:95px;margin-bottom:8px;line-height: 100px;'"></MaterialList>
                </el-form-item>
                <el-form-item label="选中图片地址">
                  <el-input v-model="item.selectedImgUrl" size="mini" type="text" style="margin-top: 5px">
                  </el-input>
                </el-form-item>
                <el-form-item label="背景尺寸">
                  <el-select v-model="item.backgroundSize" placeholder="请选择背景尺寸" size="mini">
                    <el-option label="原始大小" value="auto"></el-option>
                    <el-option label="包含" value="contain"></el-option>
                    <el-option label="覆盖" value="cover"></el-option>
                    <el-option label="100%" value="100% 100%"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="背景位置">
                  <el-select v-model="item.backgroundPosition" placeholder="请选择背景位置" size="mini">
                    <el-option label="居中" value="center"></el-option>
                    <el-option label="左上" value="left top"></el-option>
                    <el-option label="左中" value="left center"></el-option>
                    <el-option label="左下" value="left bottom"></el-option>
                    <el-option label="右上" value="right top"></el-option>
                    <el-option label="右中" value="right center"></el-option>
                    <el-option label="右下" value="right bottom"></el-option>
                    <el-option label="顶部居中" value="center top"></el-option>
                    <el-option label="底部居中" value="center bottom"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="背景重复">
                  <el-select v-model="item.backgroundRepeat" placeholder="请选择背景重复方式" size="mini">
                    <el-option label="不重复" value="no-repeat"></el-option>
                    <el-option label="水平重复" value="repeat-x"></el-option>
                    <el-option label="垂直重复" value="repeat-y"></el-option>
                    <el-option label="重复" value="repeat"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="所选标签">
                  <div>
                    <el-tag class="goods_form_tag" v-for="(tag,index) in item.tagList" :key="index" size="medium"
                            :color="tag.backColor"
                            :style="getFontColor(tag.fontColor)">{{ tag.name }}
                    </el-tag>
                    <el-button @click="openTagBox(item.tagList)" icon="el-icon-plus" size="mini"></el-button>
                  </div>
                </el-form-item>
                <el-form-item label="跳转标签">
                  <wx-page-select
                    class="notDraggable"
                    :isSystemUrl="item.isSystemUrl"
                    @switchChange="item.isSystemUrl=$event"
                    :page="item.pageUrl"
                    @change="item.pageUrl=$event"></wx-page-select>
                </el-form-item>
              </div>
            </transition-group>
          </draggable>
          <el-button v-show="formData.showType==1" style="margin-left: 20px" @click="addTab" size="medium">添加
          </el-button>

          <el-divider>内容显示</el-divider>
          <el-form-item label="加载方式" >
            <el-radio-group v-model="formData.loadingType">
              <el-radio :label="1">分页加载</el-radio>
              <el-radio :label="2">下滑加载</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="显示比例">
            <el-radio-group v-model="formData.imgShowSize">
              <el-radio :label="1">比例一</el-radio>
              <el-radio :label="2">比例二</el-radio>
              <el-radio :label="3">比例三</el-radio>
              <el-radio :label="4">比例四</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="显示动画">
            <el-radio-group v-model="formData.animation">
              <el-radio :label="0">无</el-radio>
              <el-radio :label="1">遮照</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="显示列数">
            <el-radio-group v-model="formData.rowRules">
              <el-radio :label="0">单列</el-radio>
              <el-radio :label="1">双列</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否显示名称">
            <el-switch
              v-model="formData.nameFlag"
              active-text="显示"
              inactive-text="不显示">
            </el-switch>
          </el-form-item>
          <el-form-item label="内容上边距">
            <el-input-number v-model="formData.topSpacing" size="small" controls-position="right" :min="0"
                             :max="30"></el-input-number>
            px
          </el-form-item>
          <el-form-item label="内容下边距">
            <el-input-number v-model="formData.bottomSpacing" size="small" controls-position="right" :min="0"
                             :max="30"></el-input-number>
            px
          </el-form-item>
          <el-form-item label="内容左边距">
            <el-input-number v-model="formData.leftSpacing" size="small" controls-position="right" :min="0"
                             :max="30"></el-input-number>px
          </el-form-item>
          <el-form-item label="内容右边距">
            <el-input-number v-model="formData.rightSpacing" size="small" controls-position="right" :min="0"
                             :max="30"></el-input-number>px
          </el-form-item>
          <el-form-item label="内容间距" v-show="formData.rowRules==1">
            <el-input-number v-model="formData.interval" size="small" controls-position="right" :min="0"
                             :max="30"></el-input-number>px
          </el-form-item>

        </el-form>
      </div>
    </settingSlot>
    <!-- 商品标签栏 goodsTagBoxVisible-->
    <el-dialog
      :append-to-body="true"
      title="作品标签"
      :visible.sync="goodsTagBoxVisible"
      width="80%"
      center>
      <goods-tag-select ref="goodsTagSelect" :appId="appId" :selectTagList="selectTagList"></goods-tag-select>
    </el-dialog>
    <!-- 商品排序栏 goodsSortBoxVisible-->
    <el-dialog
      :append-to-body="true"
      title="排序规则"
      :visible.sync="goodsSortBoxVisible"
      width="80%"
      center>
      <goods-sort-select ref="goodsSortSelect" :appId="appId" :selectSortId="selectTagList" @confirm="sortConfirm"></goods-sort-select>
    </el-dialog>
  </div>

</template>
<script>

import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';
import goodsTagSelect from "@/views/viewgen/goodslist/goodsTagSelect";
import settingSlot from '../settingSlot';
import bgColorSelect from "../../pages/page-components/bgColorSelect";
import draggable from "vuedraggable";
import WxPageSelect from '@/components/wx-page-select/Index.vue'
import goodsSortSelect from "@/views/viewgen/goodssort/selectSort";
import iconSelect from '@/views/viewgen/viewedit/component-library/pages/page-components/iconSelect.vue'
import {getList} from "@/api/viewgen/wxgoodstagtype";
import MaterialList from '@/components/material/wxlist.vue'

export default {
  components: {settingSlot, bgColorSelect, draggable, goodsTagSelect, WxPageSelect,goodsSortSelect,iconSelect, MaterialList},
  data() {
    return {
      categoryData: [],//分类数据
      selectTagList: [],
      goodsTagBoxVisible: false,
      goodsSortBoxVisible: false,
      sortType: '', //排序选中类型 0系统类别 1自定义类别 2全部
      sortIndex: '',//排序选中索引 0系统类别 1自定义类别 2全部
      formDataCopy: {
        loadingType: 2,
        background: "#000000",
        fontColor: "#FFFFFF",
        fontSize: 13,
        selectedFontColor: "#FFFFFF",
        selectedFontSize: 15,
        titleIcon: 'cuIcon-unfold',
        navStyle: 1, // 0: 横向导航, 1: 竖向导航
        containerHeight: 500, // 容器高度
        imgShowSize: 2,
        animation: 0,
        showType: 1,
        topSpacing: 1,
        bottomSpacing: 1,
        leftSpacing: 2,
        rightSpacing: 2,
        interval: 1,
        nameFlag: false,
        initCategoryList: [],//自动读取
        categoryList: [
          {
            name: '全部',
            sortRule: {
              id: "",
              name: ""
            },
            tagList: [],
            isSystemUrl: true,
            pageUrl: '',
            imgUrl: '',
            selectedImgUrl: '',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat'
          }
        ],//自定义的
        rowRules: 1,
        sortRule: {
          id: '',
          name: ''
        },//排序规则
        isSystemUrl: true,
        pageUrl: ''
      },
      formData: {}
    };
  },
  props: {
    thememobile: {type: Object | Array},
    showData: {
      type: Object,
      default: () => {
      }
    },
    appId: {type: Object | Array},
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
      clickComIndex: state => state.divpage.clickComIndex,
    })
  },
  mounted() {
    let that = this;

    // 改进初始化逻辑：不再简单判断空对象，而是合并默认值与传入值
    if (that.IsEmptyObj(that.showData)) {
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy));
      this.getTagList();
    } else {
      // 先用默认值初始化，确保所有属性都存在
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy));
      
      // 然后用传入的showData覆盖对应的属性（保留showData中的值）
      // 只合并顶层属性
      Object.keys(that.showData).forEach(key => {
        // 特殊处理数组类型，防止覆盖默认结构
        if (key === 'categoryList' || key === 'initCategoryList') {
          if (that.showData[key] && that.showData[key].length > 0) {
            that.$set(that.formData, key, that.showData[key]);
          }
        } else {
          that.$set(that.formData, key, that.showData[key]);
        }
      });
      
      // 打印检查分类列表数据
      if (that.formData.categoryList) {
        // 记录imgUrl值，用于调试
        that.formData.categoryList.forEach((item, index) => {
        });
      }
      if (that.formData.initCategoryList) {
      }
      
      // 兼容旧数据，确保有navStyle属性
      if (that.formData.navStyle === undefined) {
        that.$set(that.formData, 'navStyle', 0);
      }
      // 兼容旧数据，确保有containerHeight属性
      if (that.formData.containerHeight === undefined) {
        that.$set(that.formData, 'containerHeight', 300);
      }
      // 兼容旧数据，确保sortRule有完整结构
      if (!that.formData.sortRule) {
        that.$set(that.formData, 'sortRule', { id: '', name: '' });
      } else if (!that.formData.sortRule.name) {
        that.$set(that.formData.sortRule, 'name', '');
      }

      // 确保categoryList每项都有完整的数据结构
      if (!that.formData.categoryList) {
        that.$set(that.formData, 'categoryList', []);
      }

      if (that.formData.categoryList && that.formData.categoryList.length > 0) {
        that.formData.categoryList.forEach((item, index) => {
          if (!item.sortRule) {
            that.$set(that.formData.categoryList[index], 'sortRule', { id: '', name: '' });
          } else if (!item.sortRule.name) {
            that.$set(item.sortRule, 'name', '');
          }
          if (!item.tagList) {
            that.$set(that.formData.categoryList[index], 'tagList', []);
          }
          if (!item.hasOwnProperty('isSystemUrl')) {
            that.$set(that.formData.categoryList[index], 'isSystemUrl', true);
          }
          if (!item.hasOwnProperty('pageUrl')) {
            that.$set(that.formData.categoryList[index], 'pageUrl', '');
          }
          // 确保背景图片相关属性存在，但不要覆盖现有值
          if (!item.hasOwnProperty('imgUrl')) {
            that.$set(that.formData.categoryList[index], 'imgUrl', '');
          }
          if (!item.hasOwnProperty('selectedImgUrl')) {
            that.$set(that.formData.categoryList[index], 'selectedImgUrl', '');
          }
          if (!item.hasOwnProperty('backgroundSize')) {
            that.$set(that.formData.categoryList[index], 'backgroundSize', 'cover');
          }
          if (!item.hasOwnProperty('backgroundPosition')) {
            that.$set(that.formData.categoryList[index], 'backgroundPosition', 'center');
          }
          if (!item.hasOwnProperty('backgroundRepeat')) {
            that.$set(that.formData.categoryList[index], 'backgroundRepeat', 'no-repeat');
          }
        });
      }

      // 同样确保initCategoryList每项都有完整的数据结构
      if (!that.formData.initCategoryList) {
        that.$set(that.formData, 'initCategoryList', []);
      }

      if (that.formData.initCategoryList && that.formData.initCategoryList.length > 0) {
        that.formData.initCategoryList.forEach((item, index) => {
          if (!item.sortRule) {
            that.$set(that.formData.initCategoryList[index], 'sortRule', { id: '', name: '' });
          } else if (!item.sortRule.name) {
            that.$set(item.sortRule, 'name', '');
          }
          if (!item.hasOwnProperty('isSystemUrl')) {
            that.$set(that.formData.initCategoryList[index], 'isSystemUrl', true);
          }
          if (!item.hasOwnProperty('pageUrl')) {
            that.$set(that.formData.initCategoryList[index], 'pageUrl', '');
          }
          // 确保背景图片相关属性存在，但不要覆盖现有值
          if (!item.hasOwnProperty('imgUrl')) {
            that.$set(that.formData.initCategoryList[index], 'imgUrl', '');
          }
          if (!item.hasOwnProperty('selectedImgUrl')) {
            that.$set(that.formData.initCategoryList[index], 'selectedImgUrl', '');
          }
          if (!item.hasOwnProperty('backgroundSize')) {
            that.$set(that.formData.initCategoryList[index], 'backgroundSize', 'cover');
          }
          if (!item.hasOwnProperty('backgroundPosition')) {
            that.$set(that.formData.initCategoryList[index], 'backgroundPosition', 'center');
          }
          if (!item.hasOwnProperty('backgroundRepeat')) {
            that.$set(that.formData.initCategoryList[index], 'backgroundRepeat', 'no-repeat');
          }
        });
      }
    }

    // 再次确保数据完整性 - 如果categoryList为空，使用默认值
    if ((!that.formData.categoryList || that.formData.categoryList.length === 0) && that.formData.showType === 1) {
      that.$set(that.formData, 'categoryList', JSON.parse(JSON.stringify(that.formDataCopy.categoryList)));
    }

    // 如果是自动读取模式且initCategoryList为空，获取标签列表
    if (that.formData.showType === 0 && (!that.formData.initCategoryList || that.formData.initCategoryList.length === 0)) {
      this.getTagList();
    }

    // 最后一定要更新组件数据
    that.$set(that.componentsList[that.clickComIndex], 'data', that.formData);
    // 强制更新视图
    that.$forceUpdate();
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    // 判断对象是否为空
    IsEmptyObj(obj) {
      // 检查undefined和null
      if (obj === undefined || obj === null) {
        return true;
      }

      // 检查空对象 {}
      if (typeof obj === 'object' && Object.keys(obj).length === 0) {
        return true;
      }

      // 对于字符串形式的对象，检查是否为'{}'
      if (JSON.stringify(obj) === '{}' || JSON.stringify(obj) === '{"data":{}}' || JSON.stringify(obj) === '{"data":null}') {
        return true;
      }
      
      // 检查是否只包含部分基础属性而缺少重要数据
      // 如只有navStyle和containerHeight，没有其他必要数据的情况，也视为"空"
      if (Object.keys(obj).length <= 2) {
        // 如果只有很少的几个属性，检查是否缺少重要数据
        const hasImportantFields = 
          obj.loadingType !== undefined || 
          obj.background !== undefined || 
          obj.fontColor !== undefined || 
          obj.categoryList !== undefined ||
          obj.showType !== undefined;
          
        if (!hasImportantFields) {
          return true;
        }
      }

      return false;
    },
    // 删除按钮
    delBtn(index) {
      let that = this;
      that.$confirm('是否删除该按钮?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
        that.updateData({componentsList: that.componentsList});
      }).catch(() => {
      })
    },
    cancel() {
      this.$emit('cancel')
    },
    reset() {
      let that = this;
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy));
      that.$set(that.componentsList[that.clickComIndex], 'data', this.formData);
      // 确保数据刷新
      this.$forceUpdate();
    },
    confirm() {

      // 确保数据完整性 - 如果有任何必要属性缺失，从formDataCopy补充
      Object.keys(this.formDataCopy).forEach(key => {
        if (this.formData[key] === undefined) {
          this.$set(this.formData, key, JSON.parse(JSON.stringify(this.formDataCopy[key])));
        }
      });

      // 确保数据完整性
      if (!this.formData.categoryList || this.formData.categoryList.length === 0) {
        if (this.formData.showType === 1) {
          // 自定义模式下，确保有默认分类
          this.$set(this.formData, 'categoryList', JSON.parse(JSON.stringify(this.formDataCopy.categoryList)));
        }
      }

      // 最后验证每个分类项的背景图片数据是否完整
      if (this.formData.categoryList && this.formData.categoryList.length > 0) {
        this.formData.categoryList.forEach((item, index) => {
          // 确保背景图片相关属性存在
          if (!item.hasOwnProperty('selectedImgUrl')) {
            this.$set(this.formData.categoryList[index], 'selectedImgUrl', '');
          }
          if (!item.hasOwnProperty('backgroundSize')) {
            this.$set(this.formData.categoryList[index], 'backgroundSize', 'cover');
          }
          if (!item.hasOwnProperty('backgroundPosition')) {
            this.$set(this.formData.categoryList[index], 'backgroundPosition', 'center');
          }
          if (!item.hasOwnProperty('backgroundRepeat')) {
            this.$set(this.formData.categoryList[index], 'backgroundRepeat', 'no-repeat');
          }
        });
      }

      if (this.formData.initCategoryList && this.formData.initCategoryList.length > 0) {
        this.formData.initCategoryList.forEach((item, index) => {
          // 确保背景图片相关属性存在
          if (!item.hasOwnProperty('selectedImgUrl')) {
            this.$set(this.formData.initCategoryList[index], 'selectedImgUrl', '');
          }
          if (!item.hasOwnProperty('backgroundSize')) {
            this.$set(this.formData.initCategoryList[index], 'backgroundSize', 'cover');
          }
          if (!item.hasOwnProperty('backgroundPosition')) {
            this.$set(this.formData.initCategoryList[index], 'backgroundPosition', 'center');
          }
          if (!item.hasOwnProperty('backgroundRepeat')) {
            this.$set(this.formData.initCategoryList[index], 'backgroundRepeat', 'no-repeat');
          }
        });
      }

      // 更新组件数据
      this.$set(this.componentsList[this.clickComIndex], 'data', this.formData);

      // 发送数据
      this.$emit('confirm', this.formData);
    },
    delete() {
      this.$emit('delete')
    },
    // 拖动的事件等等=======================================================>
    datadragStart(e) {
      let that = this;
      that.updateData({clickComIndex: null})
      that.showSetBlock = false;
    },
    datadragUpdate(e) {
      e.preventDefault();
    },
    datadragEnd(e) {
      // console.log(e, "拖动结束");
    },
    datadragMove(e, originalEve) {
      return (e.draggedContext.element.text !== 'Gold（不可拖动元素）');
    },
    // 拖动的事件等等=======================================================>
    openTagBox(list) {
      this.selectTagList = list;
      this.goodsTagBoxVisible = true;
    },
    getFontColor(val) {
      if (!val) {
        return;
      }
      return "color:" + val;
    },
    addTab() {
      this.formData.categoryList.push({
        name: '',
        sortRule: {
          id: "",
          name: ""
        },
        tagList: [],
        isSystemUrl: true,
        pageUrl: '',
        imgUrl: '',
        selectedImgUrl: '',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      });
      // 强制更新视图
      this.$forceUpdate();
      // 更新组件数据
      this.$set(this.componentsList[this.clickComIndex], 'data', this.formData);
    },
    delMenu(index) {
      this.formData.categoryList.splice(index,1);
      // 强制更新视图
      this.$forceUpdate();
      // 更新组件数据
      this.$set(this.componentsList[this.clickComIndex], 'data', this.formData);
    },
    getTagList() {
      getList({appId: this.appId}).then(res => {
        let list = [{
          id: "-1",
          name: "全部",
          isSystemUrl: true,
          pageUrl: '',
          sortRule: {
            id: '',
            name: ''
          },
          imgUrl: '',
          selectedImgUrl: '',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }];

        for (let i = 0; i < res.data.data.length; i++) {
          list.push({
            id: res.data.data[i].id,
            name: res.data.data[i].name,
            isSystemUrl: true,
            pageUrl: '',
            sortRule: {
              id: '',
              name: ''
            },
            imgUrl: '',
            selectedImgUrl: '',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat'
          });
        }
        this.formData.initCategoryList = list;
        // 确保数据变更后视图更新
        this.$forceUpdate();
      }).catch((error) => {
        console.error("获取标签列表失败", error);
        // 确保即使请求失败，也有基本的默认数据
        if (!this.formData.initCategoryList || this.formData.initCategoryList.length === 0) {
          this.formData.initCategoryList = [{
            id: "-1",
            name: "全部",
            isSystemUrl: true,
            pageUrl: '',
            sortRule: {
              id: '',
              name: ''
            },
            imgUrl: '',
            selectedImgUrl: '',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat'
          }];
        }
      });
    },
    showTypeChange(val) {

      if (val == 0) {
        // 自动读取模式
        if (!this.formData.initCategoryList || this.formData.initCategoryList.length === 0) {
          this.getTagList();
        }
      } else if (val == 1) {
        // 自定义设置模式
        // 确保自定义设置模式下至少有一个分类项
        if (!this.formData.categoryList || this.formData.categoryList.length === 0) {
          // 使用Vue的响应式方法设置数组
          this.$set(this.formData, 'categoryList', JSON.parse(JSON.stringify(this.formDataCopy.categoryList)));
        } else {
          // 确保每个项都有正确的响应式结构
          this.formData.categoryList.forEach((item, index) => {
            if (!item.sortRule) {
              this.$set(this.formData.categoryList[index], 'sortRule', { id: '', name: '' });
            }
            if (!item.tagList) {
              this.$set(this.formData.categoryList[index], 'tagList', []);
            }
            if (!item.hasOwnProperty('isSystemUrl')) {
              this.$set(this.formData.categoryList[index], 'isSystemUrl', true);
            }
            if (!item.hasOwnProperty('pageUrl')) {
              this.$set(this.formData.categoryList[index], 'pageUrl', '');
            }
            // 确保背景图片相关属性存在，但不要覆盖现有值
            if (!item.hasOwnProperty('imgUrl')) {
              this.$set(this.formData.categoryList[index], 'imgUrl', '');
            }
            if (!item.hasOwnProperty('selectedImgUrl')) {
              this.$set(this.formData.categoryList[index], 'selectedImgUrl', '');
            }
            if (!item.hasOwnProperty('backgroundSize')) {
              this.$set(this.formData.categoryList[index], 'backgroundSize', 'cover');
            }
            if (!item.hasOwnProperty('backgroundPosition')) {
              this.$set(this.formData.categoryList[index], 'backgroundPosition', 'center');
            }
            if (!item.hasOwnProperty('backgroundRepeat')) {
              this.$set(this.formData.categoryList[index], 'backgroundRepeat', 'no-repeat');
            }
          });
        }
      }

      // 更新组件数据
      this.$set(this.componentsList[this.clickComIndex], 'data', this.formData);
      // 确保数据刷新
      this.$forceUpdate();
    },
    //打开排序规则 type :0 系统类别排序 1自定义类别排序 2 全部排序
    openSortBox(type,index) {
      this.sortType = type;
      this.sortIndex = index;
      this.goodsSortBoxVisible = true;
    },
    //删除排序规则 type :0 系统类别排序 1自定义类别排序 2 全部排序
    deleteSort(type,index) {
      if(0 == type){
        this.formData.initCategoryList[index].sortRule = {id:""}
      }else if(1 == type){
        this.formData.categoryList[index].sortRule = {id:""}
      }else if(2 == type){
        this.formData.sortRule = {id:""}
      }
      // 更新组件数据
      this.$set(this.componentsList[this.clickComIndex], 'data', this.formData);
      // 确保数据刷新
      this.$forceUpdate();
    },
    sortConfirm(obj) {

      if(this.sortType == 0){
        this.formData.initCategoryList[this.sortIndex].sortRule = {id: obj.id, name: obj.name};
      }else if(this.sortType == 1){
        this.formData.categoryList[this.sortIndex].sortRule = {id: obj.id, name: obj.name};
      }else if(this.sortType == 2){
        this.formData.sortRule = {id: obj.id, name: obj.name};
      }
      this.goodsSortBoxVisible = false;
      // 更新组件数据
      this.$set(this.componentsList[this.clickComIndex], 'data', this.formData);
      // 确保数据变更后视图更新
      this.$forceUpdate();
    },
  },
  watch: {
    showData: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.formData = JSON.parse(JSON.stringify(newVal));
          // 确保必要的属性存在
          if (this.formData.showType === 1 && (!this.formData.categoryList || this.formData.categoryList.length === 0)) {
            this.$set(this.formData, 'categoryList', JSON.parse(JSON.stringify(this.formDataCopy.categoryList)));
          }
          // 更新组件数据
          this.$set(this.componentsList[this.clickComIndex], 'data', this.formData);
          // 确保视图更新
          this.$forceUpdate();
        }
      },
      deep: true
    },
    thememobile() {
    },
  }
};
</script>
<style lang='less' scoped>

@import '../../colorui/main.css';
@import '../../colorui/icon.css';

.el-form-item {
  margin-bottom: 0;
}

.list_del_tag {
  display: none;
  float: right;
}

.drag-item {
  border: 1px solid #409EFF;
  margin-bottom: 5px;

  &:hover {
    cursor: move;
    border: 1px dashed #1fc421;

    .list_del_tag {
      display: inline;
    }
  }
}
.warning_msg_btn{
  margin-right: 10px;
}
</style>
