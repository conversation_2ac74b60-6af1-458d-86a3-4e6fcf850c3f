<!--商品分类标签-->
<template>
  <div class="cuttingLineSetting">
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">热点图片</p>
      <div slot="mainContent">
        <el-form ref="form" label-width="100px" :model="formData">
          <el-divider>基础属性</el-divider>
          <el-form-item label="组件上边距">
            <el-input v-model="formData.paddingTop" size="mini" type="number" style="margin-top: 5px"
                      placeholder="页面下边距">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="组件下边距">
            <el-input v-model="formData.paddingBottom" size="mini" type="number" style="margin-top: 5px"
                      placeholder="页面下边距">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="背景颜色">
            <el-input v-model="formData.background" size="small" style="margin-top: 5px">
              <template slot="append">
                <el-color-picker size="mini" v-model="formData.background"></el-color-picker>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="显示宽度">
            <el-input v-model="formData.width" size="mini" type="number" placeholder="宽度" :min="0" :max="100"
                      style="margin-top: 5px">
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
          <el-form-item label="图片选择" >
            <MaterialList :value="formData.imageUrl?[formData.imageUrl]:[]"  @sureSuccess="formData.imageUrl = $event?$event[0]:''" @deleteMaterial="formData.imageUrl = ''"
                          type="image"  :divStyle="'width:100%;margin-bottom:0px;height:90px;line-height: 90px;'" :num=1 ></MaterialList>
          </el-form-item>
          <el-form-item >
            <el-button type="primary" size="small" @click="openHotAreaBox">选取区域</el-button>
          </el-form-item>
        </el-form>
      </div>
    </settingSlot>

    <!--    热点区域-->
    <el-dialog
      title="热区选择"
      :visible.sync="hotAreaBoxVisible"
      :close-on-click-modal="false"
      append-to-body>
      <el-row justify="start" :gutter="5" >
        <el-col :span="12" >
          <div >
            <hot-zone
              :image="formData.imageUrl"
              :zonesInit="formData.zones"
              @add="handleAdd"
              @remove="handleRemove"
              @change="handleChange"
            >
            </hot-zone>
          </div>
        </el-col>
        <el-col :span="12">
          <div style="height: 600px; overflow: hidden;overflow-y: scroll;">
            <el-card class="box-card" v-for="(item,index) in formData.zones" :key="index">
              <h3>{{"区域"+(index+1)}}</h3>
              <wx-page-select
                :isSystemUrl="item.isSystemUrl"
                @switchChange="item.isSystemUrl =$event"
                :page="item.pageUrl"
                @change="item.pageUrl=$event"></wx-page-select>
            </el-card>

          </div>
        </el-col>
      </el-row>
    </el-dialog>
  </div>

</template>
<script>

import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';
import {getLocation} from '@/api/third-party/Tmap/TMap';
import region from '/public/json/region.json';
import settingSlot from '../settingSlot';
import bgColorSelect from "../../pages/page-components/bgColorSelect";
import hotZone from './hotZone'
import ErrLogs from "@/page/logs";
import wxPageSelect from '@/components/wx-page-select/Index.vue'
import MaterialList from '@/components/material/wxlist.vue'
export default {
  components: {ErrLogs, settingSlot, bgColorSelect,hotZone,wxPageSelect,MaterialList},
  data() {
    return {
      hotAreaBoxVisible: false,
      formDataCopy: {
        paddingTop: 0,
        paddingBottom: 0,
        background: "#ffffff",
        imageUrl: '',
        width: 100,
        zones: [
          {
            heightPer: 0.4374,
            leftPer: 0.1153,
            topPer: 0.238,
            widthPer: 0.2827,
            isSystemUrl: false,
            url: ''
          }
        ],
      },
      formData: {}

    };
  },
  props: {
    thememobile: {type: Object | Array},
    showData: {
      type: Object,
      default: () => {
      }
    },
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
      clickComIndex: state => state.divpage.clickComIndex,
    })
  },
  mounted() {
    let that = this;
    if (that.IsEmptyObj(that.showData)) {
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
    } else {
      that.formData = that.showData
    }
    that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
    // that.updateData({
    //   componentsList: that.componentsList
    // })
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    // 删除按钮
    delBtn(index) {
      let that = this;
      that.$confirm('是否删除该按钮?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
        that.updateData({componentsList: that.componentsList});
      }).catch(() => {
      })
    },
    cancel() {
      this.$emit('cancel')
    },
    reset() {
      let that = this;
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      that.componentsList[that.clickComIndex]['data'] = this.formData;
    },
    confirm() {
      this.$emit('confirm', this.formData)
    },
    delete() {
      this.$emit('delete')
    },
    openHotAreaBox() {
      this.hotAreaBoxVisible = true;
    },
    handleAdd (zone) {
      zone.pageUrl = "";
      zone.isSystemUrl = false;
      this.formData.zones.push(zone)
      console.log(zone);
    },
    handleRemove (index) {
      this.formData.zones.splice(index, 1)
    },
    handleChange () {
    },
    getZoneStyle (val) {
      return `${(val || 0) * 100}%`
    },
    handleZoneClick (url) {
      url && window.open(url)
    }
  },
  watch: {
    showData: {
      handler(newVal, oldVal) {
        this.formData = newVal ? newVal : this.formData;
      },
      deep: true
    },
    thememobile() {
    },
  }
};
</script>
<style lang='less' scoped>

.el-form-item {
  margin-bottom: 0;
}
.box-card{
  margin-bottom: 10px;
}

</style>
