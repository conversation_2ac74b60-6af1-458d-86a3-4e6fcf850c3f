<template>
  <div class="execution">
    <basic-container>
      <el-tabs type="border-card" v-model="tabValue" @tab-click="tabClick">
        <el-tab-pane name="1" label="未签用户">
          <un-sign-user ref="unSignUser"></un-sign-user>
        </el-tab-pane>
        <el-tab-pane name="2" label="已签用户">
          <sign-user :wxAppList="wxAppList" ref="signUser"></sign-user>
        </el-tab-pane>
      </el-tabs>
    </basic-container>
  </div>
</template>

<script>
import signUser from '@/views/viewgen/imgshareuser/signuser'
import unSignUser from '@/views/viewgen/imgshareuser/unsignuser'
import {getList as getWxAppList, getList} from "@/api/wxmp/wxapp";
import {tableOption} from "@/const/crud/viewgen/viewmanger";

export default {
  name: 'imgshareuser',
  components: {
    signUser,
    unSignUser,
  },
  data() {
    return {
      tabValue: '1',
      wxAppList: [],
    }
  },
  created() {
    this.getWxAppList();
  },
  mounted() {
    console.log("getWxAppList",this.wxAppList)
  },
  computed: {},
  methods: {
    //加载公众号列表
    getWxAppList() {
      getWxAppList({
        appType: '2'
      }).then(res => {
        console.log("加载公众号列表",res)
        this.wxAppList = res.data;

      }).catch((err) => {
        console.log(err)
      })
    },
    tabClick(tab) {
      if (tab.name == "1") {
        this.$refs.signUser.refreshChange();
      } else if (tab.name == "2") {
        this.$refs.unSignUser.refreshChange();
      }
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
