<template>
  <div>
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">下拉选择框</p>
      <div slot="hint"></div>
      <div slot="mainContent">
        <el-divider>基础属性</el-divider>
        <el-form ref="form" label-width="100px" :model="formData">
          <el-form-item label="标题内容">
            <el-input v-model="formData.title" size="mini" style="margin-top: 5px" placeholder="标题文字">
            </el-input>
          </el-form-item>
          <el-form-item label="标题颜色">
            <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
              <el-input v-model="formData.titleColor" size="small" style="margin-top: 5px">
                <template slot="append">
                  <el-color-picker size="mini" v-model="formData.titleColor"></el-color-picker>
                </template>
              </el-input>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="标题大小">
            <el-input v-model="formData.titleSize" size="mini" style="margin-top: 5px" placeholder="文字大小">
            </el-input>
          </el-form-item>

          <el-form-item label="标题加粗">
            <el-switch
              v-model="formData.titleWeight"
              active-text="加粗"
              inactive-text="普通">
            </el-switch>
          </el-form-item>
          <el-divider></el-divider>
          <el-form-item label="描述内容">
            <el-input v-model="formData.describe" size="mini" style="margin-top: 5px" placeholder="标题文字">
            </el-input>
          </el-form-item>
          <el-form-item label="描述颜色">
            <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
              <el-input v-model="formData.describeColor" size="small" style="margin-top: 5px">
                <template slot="append">
                  <el-color-picker size="mini" v-model="formData.describeColor"></el-color-picker>
                </template>
              </el-input>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="描述大小">
            <el-input v-model="formData.describeSize" size="mini" style="margin-top: 5px" placeholder="文字大小">
            </el-input>
          </el-form-item>
          <el-form-item label="描述加粗">
            <el-switch
              v-model="formData.describeWeight"
              active-text="加粗"
              inactive-text="普通">
            </el-switch>
          </el-form-item>
          <el-divider></el-divider>
          <el-form-item label="是否必填">
            <el-switch
              v-model="formData.required"
              active-text="必填"
              inactive-text="非必填">
            </el-switch>
          </el-form-item>
          <el-divider></el-divider>
          <el-alert
            title="使用提示"
            type="success"
            :closable="false"
            description="下方输入框内填写下拉选项，每行一个，建议单个选项长度不超过20个汉字。">
          </el-alert>
          <el-form-item label="参数输入">
            <el-input
              type="textarea"
              :autosize="{ minRows: 5, maxRows: 10}"
              placeholder="请输入参数"
              @blur="blurOption"
              v-model="textValue">
            </el-input>
          </el-form-item>
        </el-form>
      </div>
    </settingSlot>
  </div>
</template>

<script>

import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';

import settingSlot from './../settingSlot'
import MaterialList from '@/components/material/wxlist.vue'
import iconSelect from '../../pages/page-components/iconSelect.vue'
export default {
  components: {settingSlot, MaterialList,iconSelect},
  data() {
    return {
      formDataCopy: {
        title: '消费预算',
        titleColor: '#000000',
        titleSize: 18,
        titleWeight: false,//标题是否加粗
        describe: '填写消费预算以便为您提供更精准的优惠介绍',//描述
        describeColor: '#666666',//描述字体色
        describeSize: 12,
        describeWeight: false,
        required: true,//是否必填
        optionList: [],
      },
      textValue:'',
      formData: {}
    };
  },
  props: {
    clientType: [String],
    showData: {
      type: Object,
      default: () => {
      }
    },
    config: {
      type: Object,
      default: () => {
      }
    }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
      clickComIndex: state => state.divpage.clickComIndex,
    })
  },
  watch: {
    showData: {
      handler(newVal, oldVal) {
        this.formData = newVal ? newVal : this.formData;
      },
      deep: true
    },
  },
  mounted() {
    let that = this;
    if (that.IsEmptyObj(that.showData)) {
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
    } else {
      that.formData = that.showData
    }
    that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
    // that.updateData({
    //   componentsList: that.componentsList
    // })
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),

    // 添加项目
    addItem() {
      let that = this;
      that.pushItem()
    },
    pushItem() {
      let that = this;
      if (that.formData.noticeList.length >= 10) {
        that.$message.error("项目不能超过10条")
        return false;
      }
      that.formData.noticeList.push({
        id: Math.random(),
        imageUrl: '',
        imgWidth: 0,
        imgHeight: 0,
        pageUrl: '',
        content: '',
        tag: ''
      })
    },
    // 删除项目
    delItem(index) {
      let that = this;
      if (that.formData.swiperList.length <= 1) {
        that.$message.error("请至少保留一条项目")
        return false;
      }
      that.$confirm('是否删除该项目?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$delete(that.formData.noticeList, index)
      }).catch(() => {
      })
    },
    // 删除按钮
    delBtn(index) {
      let that = this;
      that.$confirm('是否删除该按钮?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
        that.updateData({componentsList: that.componentsList});
      }).catch(() => {
      })
    },
    cancel() {
      this.$emit('cancel')
    },
    reset() {
      let that = this;
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      that.componentsList[that.clickComIndex]['data'] = this.formData;
    },
    confirm() {
      this.$emit('confirm', this.formData)
    },
    delete() {
      this.$emit('delete')
    },
    // 拖动的事件等等=======================================================>
    datadragStart(e) {
      let that = this;
      that.updateData({clickComIndex: null})
      that.showSetBlock = false;
    },
    datadragUpdate(e) {
      e.preventDefault();
    },
    datadragEnd(e) {
      // console.log(e, "拖动结束");
    },
    datadragMove(e, originalEve) {
      return (e.draggedContext.element.text !== 'Gold（不可拖动元素）');
    },
    // ===================================================================>
    blurOption() {
      console.log("触发了")
      this.formData.optionList = this.textValue.split(/[(\r\n)\r\n]+/);
      console.log(this.formData.optionList)
    },
  },

};
</script>
<style lang='less' scoped>

@import '../../colorui/main.css';

.el-form-item {
  margin-bottom: 0;
}

.menu_list_title_name {
  display: inline;
}

.menu_list_title {
  display: block;
  width: 80%;
}

.menu_list_tag {
  display: none;
  float: right;
}

.drag-item {
  padding: 0px 0 5px 20px;
  margin-bottom: 15px;
  margin-top: 20px;
  border: 1px solid transparent;

  &:hover {
    cursor: move;
    border: 1px dashed #1fc421;

    .menu_list_tag {
      display: inline;
    }
  }
}
</style>
