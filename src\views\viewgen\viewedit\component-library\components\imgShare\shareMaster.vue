<template>
  <div class="shareMasterComponent"
       :style="{marginBottom: `${setData.pageMarginBottom}px`,marginTop: `${setData.pageMarginTop}px`,marginLeft: `${setData.pageMarginLeft}px`,marginRight: `${setData.pageMarginRight}px`}">
    <div v-show="setData.showType == '1'">
      <div class="show">
        <div class="round cu-avatar bg-img lg margin-xs text-yellow"
             :style="{width:`${setData.avatarSize}px`,height:`${setData.avatarSize}px`}"
             style="background-image:url('/img/spell-group/yiwen.png');border: 5px white solid">
        </div>
      </div>
    </div>
    <div :style="{backgroundColor: setData.backColor,
          marginBottom: `${setData.insideMarginBottom}px`,
          marginTop: `${setData.insideMarginTop}px`,
          paddingBottom: `${setData.insidePaddingBottom}px`,
          paddingTop: `${setData.insidePaddingTop}px`,
          borderTopLeftRadius:`${setData.topBorderRadius}px`,
          borderTopRightRadius:`${setData.topBorderRadius}px`,
          borderBottomLeftRadius:`${setData.bottomBorderRadius}px`,
          borderBottomRightRadius:`${setData.bottomBorderRadius}px`}">
      <div v-show="setData.showType == '0'">
        <div class="show">
          <div class="cu-avatar bg-img lg margin-xs text-yellow"
               style="background-image:url('/img/spell-group/yiwen.png');height: 80px;width: 80px;">
          </div>
          <div :style="{color: setData.fontColor}">
            <div class="content_title">{{ setData.title }}</div>
            <div class="content_describe">{{ setData.describe }}</div>
            <div  class="content_remark">{{ setData.remark }}</div>
            <div class=" content_share text-cut">来自【微信昵称】的分享</div>
          </div>
        </div>
        <div class="bottomFont">
          <h5 class="text-cut" style=" font-size: 8px; margin-top: 3px; color:#999;">{{ setData.copyright }}</h5>
        </div>
      </div>
      <div v-show="setData.showType == '1'">
        <div :style="{color: setData.fontColor}">
          <div class="show content_title text-cut"><p>{{ setData.title }}</p></div>
          <div class="show content_describe text-cut"><p>{{ setData.describe }}</p></div>
          <div class="show content_remark text-cut"><p>{{ setData.remark }}</p></div>
          <div class="show content_share text-cut"><p>来自【微信昵称】的分享</p></div>
        </div>
        <div class="bottomFont">
          <h5 class="show text-cut" style=" font-size:9px; margin-top: 3px; color:#999;">{{ setData.copyright }}</h5>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';


export default {
  data() {
    return {};
  },
  components: {},
  props: {
    theme: {type: Object | Array},
    setData: {type: Object | Array},
    cId: {type: Number},
    noEditor: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {
  },
  mounted() {
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
  },
  watch: {
    setData(newVal, oldVal) {
    },
    componentsList(newVal, oldVal) {          //添加的时候出发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    }
  },
  beforeDestroy() {
    // this.$root.Bus.$off('addHotSpot')
  }
};
</script>
<style lang='less' scoped>

@import '../../colorui/main.css';
@import '../../colorui/icon.css';

.show {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.bottomFont {
  padding-top: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}
.content_title{
  font-weight: bolder;
  font-size: 24px;
  padding-bottom: 10px;
}
.content_describe{
  font-size: 12px;
  padding-bottom: 5px;
}
.content_remark{
  font-size: 12px;
  padding-bottom: 5px;
}
.content_share{
  font-size: 12px;
}
</style>
