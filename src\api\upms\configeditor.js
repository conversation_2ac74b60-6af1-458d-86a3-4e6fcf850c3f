import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/upms/configeditor/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/upms/configeditor',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/upms/configeditor/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/upms/configeditor/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/upms/configeditor',
        method: 'put',
        data: obj
    })
}

export function getObj2() {
    return request({
        url: '/upms/configeditor',
        method: 'get'
    })
}
