import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/mall/userinfo/page',
        method: 'get',
        params: query
    })
}

export function getCount(query) {
    return request({
        url: '/mall/userinfo/count',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/mall/userinfo',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/mall/userinfo/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/mall/userinfo/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/mall/userinfo',
        method: 'put',
        data: obj
    })
}

export function editParentId(obj) {
  return request({
    url: '/mall/userinfo/parentId',
    method: 'put',
    data: obj
  })
}

export function getStatistics(query) {
  return request({
    url: '/mall/userinfo/statistics',
    method: 'get',
    params: query
  })
}

export function getStatisticsByColumn(column, query) {
  return request({
    url: '/mall/userinfo/statistics/'+column,
    method: 'get',
    params: query
  })
}
