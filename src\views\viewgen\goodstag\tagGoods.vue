<!--抽屉作品列表-->
<template>
  <div class="execution">
    <basic-container>
      <el-row :span="24" :gutter="10">
        <el-col :xs="24"
                :sm="24"
                :md="24">
          <avue-crud ref="crud"
                     :page.sync="page"
                     :data="tableData"
                     :table-loading="tableLoading"
                     :option="tableOption"
                     :permission="permissionList"
                     @on-load="getPageByTag"
                     @refresh-change="refreshChange"
                     @row-update="handleUpdate"
                     @row-save="handleSave"
                     @row-del="handleDel"
                     @sort-change="sortChange"
                     @search-change="searchChange"
                     @selection-change="selectionChange">
            <template slot="subscribe" slot-scope="scope" >
              <el-tag size="mini" effect="dark" :type="scope.row.subscribe == '1' ? 'success' : scope.row.subscribe == '0' ? 'danger' : 'warning'">{{scope.row.$subscribe}}</el-tag>
            </template>
            <template slot="sex" slot-scope="scope" >
              <el-tag size="mini" effect="light" :type="scope.row.sex == '1' ? '' : scope.row.sex == '2' ? 'danger' : 'warning'">{{scope.row.$sex}}</el-tag>
            </template>
            <template slot="latitude" slot-scope="scope">
              <el-link v-if="scope.row.longitude" type="primary" target="_blank" :href="'https://map.qq.com/?type=marker&isopeninfowin=1&markertype=1&pointx='+scope.row.longitude+'&pointy='+scope.row.latitude+'&name='+scope.row.nickName+'&ref=gocreateone'">
                <i class="el-icon-map-location"></i>
              </el-link>
            </template>
            <template slot="menuLeft">
              <el-button type="primary"
                         @click="batchDel"
                         size="small"
                         icon="el-icon-document"
                         >批量删除</el-button>
              <el-button type="danger"
                         @click="delAllTagLink"
                         size="small"
                         icon="el-icon-delete-solid"
                         >清除全部</el-button>
<!--              <el-button-->
<!--                         size="small"-->
<!--                         @click="addByIdBoxVisible =true"-->
<!--                         icon="el-icon-add"-->
<!--                        >新增用户</el-button>-->
            </template>
            <template slot="menu" slot-scope="scope">
              <el-button type="text"
                         icon="el-icon-price-tag"
                         size="small"
                         plain
                         @click="delTagLink(scope.row,scope.index)">删除标签</el-button>
            </template>
            <template slot="tagidListSearch" slot-scope="scope">
              <el-select v-model="scope.row.tagidList" placeholder="请选择">
                <el-option
                        v-for="item in userTagsData"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id">
                </el-option>
              </el-select>
            </template>
          </avue-crud>
        </el-col>
      </el-row>
    </basic-container>
    <!--    添加id框-->
    <el-dialog
      title="添加byId"
      :visible.sync="addByIdBoxVisible"
      width="50%"
      append-to-body
      :close-on-click-modal="false"
      center>
      <el-form ref="form"  label-width="80px">
        <el-form-item label="openId">
          <el-input v-model="addById.openId"  :autosize="{ minRows: 10}" type="textarea"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="addByOpenId">确认</el-button>
          <el-button @click="addByIdBoxVisible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
  import { getPageByTag, addObj, putObj, delObj, updateRemark } from '@/api/viewgen/wxgoods'
  import {addById, getList as listUserTags} from '@/api/wxmp/wxusertags'
  import { delTagLink,delAllTagLink} from '@/api/viewgen/wxgoodstaglink'
  import { addByTag} from '@/api/wxmp/wxbacklist'
  import { tableOption3 } from '@/const/crud/viewgen/wxgoods'
  import { mapGetters } from 'vuex'
  export default {
    name: 'tagGoods',
    components: {},
    props: {
      selectedTagId: {type:String},//选中的标签
      appId: {type:String},
    },
    watch: {
      selectedTagId(val, oldVal){
        console.log("id改了")
        this.getPageByTag(this.page)
      },

    },
    data() {
      return {
        addByIdBoxVisible:false,//用户标签弹出框
        addById:{},
        tableData: [],
        page: {
          total: 0, // 总页数
          currentPage: 1, // 当前页数
          pageSize: 20, // 每页显示多少条
          ascs: [],
          descs: ''
        },
        paramsSearch:{},
        tableLoading: false,
        tableOption: tableOption3,
        selectionData: [],//批量选取
        dialogTagging: false,
        checkedTags: [],
        userTagsData: [],
        taggingType: '',
        wxUserId:'',
      }
    },
    created() {
    },
    mounted: function() { },
    computed: {
      ...mapGetters(['permissions']),
      permissionList() {
        return {
          addBtn: this.permissions['wxmp:wxuser:add'] ? true : false,
          delBtn: this.permissions['wxmp:wxuser:del'] ? true : false,
          editBtn: this.permissions['wxmp:wxuser:edit'] ? true : false,
          viewBtn: this.permissions['wxmp:wxuser:get'] ? true : false,
        }
      }
    },
    methods: {
      listUserTags() {
        this.tableLoading = true
        listUserTags({
          appId: this.appId
        }).then(res => {
          if(res.data.code == '0'){
            let userTagsData = res.data.data
            this.userTagsData = userTagsData
            this.$refs.crud.DIC.tagidList = userTagsData
          }else{
            this.$message.error('获取用户标签出错：' + res.data.msg)
          }
          this.tableLoading = false
          this.getPageByTag(this.page)
        }).catch(() => {
          this.tableLoading = false
          this.getPageByTag(this.page)
        })
      },
      selectionChange(list){
        this.selectionData = list
      },
      searchChange(params,done){
        params = this.filterForm(params)
        this.paramsSearch = params
        this.page.currentPage = 1
        this.getPageByTag(this.page,params)
        done()
      },
      sortChange(val){
        let prop = val.prop ? val.prop.replace(/([A-Z])/g,"_$1").toLowerCase() : '';
        if(val.order=='ascending'){
          this.page.descs = []
          this.page.ascs = prop
        }else if(val.order=='descending'){
          this.page.ascs = []
          this.page.descs = prop
        }else{
          this.page.ascs = []
          this.page.descs = []
        }
        this.getPageByTag(this.page)
      },
      getPageByTag(page, params) {
        if(this.appId){
          this.tableData =[];
          this.tableLoading = true
          this.paramsSearch.tagId = this.selectedTagId;
          console.log("请求参数",Object.assign({
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
            appType: '2',
            id:this.selectedTagId,
          }, params, this.paramsSearch))
          getPageByTag(Object.assign({
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
            appType: '2',
            id:this.selectedTagId,
          }, params, this.paramsSearch)).then(res => {
            this.tableData = res.data.data.records
            this.page.total = res.data.data.total
            this.page.currentPage = page.currentPage
            this.page.pageSize = page.pageSize
            this.tableLoading = false
          }).catch(() => {
            this.tableLoading = false
          })
        }
      },
      updateRemark(row, index){
        this.$prompt('请输入备注', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /\S/,
          inputErrorMessage: '输入不能为空'
        }).then(({ value }) => {
          this.tableLoading = true
          row.remark = value
          updateRemark(row).then(res => {
            this.tableLoading = false
            if(res.data.code == '0'){
              this.tableData.splice(index, 1, Object.assign({}, row))
              this.$message({
                showClose: true,
                message: '修改成功',
                type: 'success'
              })
              this.getPageByTag(this.page)
            }else{
              this.$message.error(res.data.msg)
            }
          }).catch(() => {
            this.tableLoading = false
          })
        })
      },
      handleDel: function(row, index) {
        var _this = this
        this.$confirm('是否确认删除', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function() {
            return delObj(row.id)
          }).then(data => {
          _this.$message({
            showClose: true,
            message: '删除成功',
            type: 'success'
          })
          this.getPageByTag(this.page)
        }).catch(function(err) { })
      },
      /**
       * @title 数据更新
       * @param row 为当前的数据
       * @param index 为当前更新数据的行数
       * @param done 为表单关闭函数
       *
       **/
      handleUpdate: function(row, index, done, loading) {
        putObj(row).then(data => {
          this.$message({
            showClose: true,
            message: '修改成功',
            type: 'success'
          })
          done()
          this.getPageByTag(this.page)
        }).catch(() => {
          loading()
        })
      },
      /**
       * @title 数据添加
       * @param row 为当前的数据
       * @param done 为表单关闭函数
       *
       **/
      handleSave: function(row, done, loading) {
        addObj(row).then(data => {
          this.$message({
            showClose: true,
            message: '添加成功',
            type: 'success'
          })
          done()
          this.getPageByTag(this.page)
        }).catch(() => {
          loading()
        })
      },
      /**
       * 刷新回调
       */
      refreshChange(page) {
        this.getPageByTag(this.page)
      },
      //全部拉黑
      addBackList() {
        console.log("请求参数",{
          tagId:this.selectedTagId,
          appId:this.appId
        })
        this.$confirm('确定要全部拉黑吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          addByTag( {
            id:this.selectedTagId,
            appId:this.appId
          }).then(res => {
            this.$message({
              showClose: true,
              message: '拉黑成功',
              type: 'success'
            })
          }).catch(() => {
          })
        }).catch(() => {
        });
      },
      //删除标签标签
      delTagLink(obj){
        let goodsIdList =[];
        goodsIdList.push(obj.id)
        this.$confirm('确定要删除标签吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          delTagLink({
            tagId:this.selectedTagId,
            goodsIdList:goodsIdList,
          }).then(res => {
            this.$message({
              showClose: true,
              message: '删除成功',
              type: 'success'
            })
            this.getPageByTag(this.page)
          }).catch(() => {
          })
        }).catch(() => {
        });

      },
      //批量删除标签标签
      batchDel(){
        console.log("213123",this.selectionData)
        let goodsIdList =[];
        for (let i = 0; i < this.selectionData.length; i++) {
          goodsIdList.push(this.selectionData[i].id)
        }

        this.$confirm('确定要删除标签吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          delTagLink({
            tagId:this.selectedTagId,
            goodsIdList:goodsIdList,
          }).then(res => {
            this.$message({
              showClose: true,
              message: '删除成功',
              type: 'success'
            })
            this.getPageByTag(this.page)
          }).catch(() => {
          })
        }).catch(() => {
        });
      },
      //删除标签全部内容
      delAllTagLink(){
        this.$confirm('确定要清空该标签吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          delAllTagLink( {
            tagId:this.selectedTagId,
          }).then(res => {
            this.$message({
              showClose: true,
              message: '清空成功',
              type: 'success'
            })
            this.getPageByTag(this.page)
          }).catch(() => {
          })
        }).catch(() => {
        });
      },

      addByOpenId(){
        this.addById.openIdList =  this.addById.openId.split(/[(\r\n)\r\n]+/);
        this.addById.appId= this.appId;
        this.addById.tagId= this.selectedTagId;
        addById(this.addById).then(res => {
          console.log("res",res);
          this.refreshChange();
          this.addByIdBoxVisible = false;
        }).catch(() => {
        })
      },
    }
  }
</script>

<style lang="scss" scoped>
</style>
