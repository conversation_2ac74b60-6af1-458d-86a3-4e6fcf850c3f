<template>
  <div class="countDownSetting">
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">投票倒计时</p>
      <div slot="mainContent">
        <el-form ref="form" label-width="100px" :model="formData">
          <el-divider>基础属性</el-divider>
          <el-form-item label="上边界">
            <el-input v-model="formData.pageMarginTop" size="mini" type="number" style="margin-top: 5px" placeholder="组件上边距">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="下边界">
            <el-input v-model="formData.pageMarginBottom" size="mini" type="number" style="margin-top: 5px" placeholder="组件下边距">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="左边界">
            <el-input v-model="formData.pageMarginLeft" size="mini" :mini="0" type="number" style="margin-top: 5px"
                      placeholder="组件内部与左边界的间隔">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="右边界">
            <el-input v-model="formData.pageMarginRight" size="mini" :mini="0" type="number" style="margin-top: 5px"
                      placeholder="组件内部与右边界的间隔">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-divider>内容设置</el-divider>
          <el-form-item  label="内容上距">
            <el-input v-model="formData.contentPaddingTop" size="mini"  :mini="0" type="number" style="margin-top: 5px" placeholder="">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item  label="内容下距">
            <el-input v-model="formData.contentPaddingBottom" size="mini"  :mini="0" type="number" style="margin-top: 5px" placeholder="">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item  label="内容左距">
            <el-input v-model="formData.contentPaddingLeft" size="mini"  :mini="0" type="number" style="margin-top: 5px" placeholder="">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item  label="内容右距">
            <el-input v-model="formData.contentPaddingRight" size="mini"  :mini="0" type="number" style="margin-top: 5px" placeholder="">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="主体颜色">
            <el-input v-model="formData.color" size="small" style="margin-top: 5px">
              <template slot="append">
                <el-color-picker size="mini" v-model="formData.color"></el-color-picker>
              </template>
            </el-input>
          </el-form-item>
           <el-form-item label="字体颜色">
            <el-input v-model="formData.fontColor" size="small" style="margin-top: 5px">
              <template slot="append">
                <el-color-picker size="mini" v-model="formData.fontColor"></el-color-picker>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="背景颜色">
            <el-input v-model="formData.backColor" size="small" style="margin-top: 5px">
              <template slot="append">
                <el-color-picker size="mini" v-model="formData.backColor"></el-color-picker>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="提示信息">
            <el-input v-model="formData.title" size="small" style="margin-top: 5px"></el-input>
          </el-form-item>
          <el-form-item label="上圆角设置">
            <el-slider v-model="formData.topBorderRadius" :min="0" :max="40"></el-slider>
          </el-form-item>
          <el-form-item label="下圆角设置">
            <el-slider v-model="formData.bottomBorderRadius" :min="0" :max="40"></el-slider>
          </el-form-item>
        </el-form>
      </div>
    </settingSlot>
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';
import draggable from "vuedraggable";
import settingSlot from '../settingSlot'
import store from "@/store";

export default {
  components: {settingSlot,draggable},
  data() {
    return {
      formDataCopy: {
        pageMarginTop:0,
        pageMarginBottom:0,
        pageMarginLeft:0,
        pageMarginRight:0,
        contentPaddingTop:0,
        contentPaddingBottom:0,
        contentPaddingLeft:0,
        contentPaddingRight:0,
        title:"距离集福时间到期",
        color:'#f36d6d',
        fontColor:'#000000',
        backColor:'#FFFFFF',
        topBorderRadius:0,
        bottomBorderRadius:0,
      },
      formData: {},
    };
  },
  props: {
    thememobile: {type: Object | Array},
    showData: {
      type: Object,
      default: () => {
      }
    },
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
      clickComIndex: state => state.divpage.clickComIndex,
    })
  },
  mounted() {
    let that = this;
    if (that.IsEmptyObj(that.showData)) {

      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
    } else {
      that.formData = that.showData
    }
    that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
    // that.updateData({
    //   componentsList: that.componentsList
    // })
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    // 删除按钮
    cancel() {
      this.$emit('cancel')
    },
    reset() {
      let that = this;
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      that.componentsList[that.clickComIndex]['data'] = this.formData;
    },
    confirm() {
      this.$emit('confirm', this.formData)
    },
    delete() {
      this.$emit('delete')
    },
    formatTooltip(val) {
      return val / 100;
    },
  },

  watch: {
    showData: {
      handler(newVal, oldVal) {
        this.formData = newVal ? newVal : this.formData;
      },
      deep: true
    },
  }
};
</script>
<style lang='less' scoped>

.el-form-item{
  margin-bottom: 0;
}

</style>
