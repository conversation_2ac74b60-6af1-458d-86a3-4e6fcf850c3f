import request from '@/router/axios'

export function getList(query) {
  return request({
    url: '/weixin/wxusertags/wxlist',
    method: 'get',
    params: query
  })
}

export function getSysList(query) {
  return request({
    url: '/weixin/wxusertags/list',
    method: 'get',
    params: query
  })
}

export function getUserTagAndType(query) {
  return request({
    url: '/weixin/wxusertags/getTagAndType',
    method: 'get',
    params: query
  })
}


export function addObj(obj) {
  return request({
    url: '/weixin/wxusertags/add4Sys',
    method: 'post',
    data: obj
  })
}

export function addUserTags(obj) {
  return request({
    url: '/weixin/wxuser/batch-set-tags',
    method: 'post',
    data: obj
  })
}

export function deleteUserTags(obj) {
  return request({
    url: '/weixin/wxuser/batch-delete-tags',
    method: 'post',
    data: obj
  })
}

export function addAttention(obj) {
  return request({
    url: '/weixin/wxusertags/add4Sys/attention',
    method: 'post',
    data: obj
  })
}
//临时使用
export function addById(obj) {
  return request({
    url: '/weixin/wxusertags/addById',
    method: 'post',
    data: obj
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/wxusertags/put4Sys',
    method: 'put',
    data: obj
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/wxusertags/'+ id,
    method: 'delete',
  })
}

//标签高级管理操作
export function tagManger(obj) {
  return request({
    url: '/weixin/wxusertags/manager',
    method: 'post',
    data: obj
  })
}

