<template>
  <div class="execution">
    <basic-container>
      <el-form label-width="100px"  class="search_form" :model="form">
        <el-row type="flex" justify="start">
          <el-form-item label="所选公众号:">
            <el-select size="small" v-model="appIdList" @change="appChange" placeholder="请选择" multiple collapse-tags>
              <el-option
                v-for="item in wxAppList"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="页面选择:">
            <el-select size="small" style="width: 150px" v-model="currentPageTypeList" @change="pageTypeChange"
                       placeholder="请选择类型" multiple collapse-tags>
              <el-option
                v-for="item in pageTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
            <el-select size="small" v-model="pageId" placeholder="可选择具体页面">
              <el-option
                v-for="item in pageList"
                :key="item.id"
                :label="item.pageName"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row type="flex" justify="start">
          <el-form-item label="订单类型:">
            <el-select size="small" v-model="currentOrderTypeList" placeholder="请选择" multiple collapse-tags>
              <el-option
                v-for="item in orderTypeList"
                :key="item.id"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否支付:">
            <el-select size="small" v-model="currentPayType" placeholder="请选择">
              <el-option
                v-for="item in payTypeList"
                :key="item.id"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="手机号码:">
            <el-input size="small" v-model="phone" placeholder="请输入">
            </el-input>
          </el-form-item>
          <el-form-item label="支付金额:">
            <el-input size="small" v-model="paymentPrice" placeholder="请输入">
            </el-input>
          </el-form-item>
        </el-row>
        <el-row type="flex" justify="start">
          <el-form-item label="">
            <el-button size="small" type="primary" @click="search">确认搜索</el-button>
            <el-button size="small" @click="reset">重置</el-button>
          </el-form-item>

        </el-row>
      </el-form>
      <avue-crud ref="crud"
                 :page.sync="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 v-model="form"
                 @on-load="getPage"
                 @refresh-change="refreshChange"
                 @row-update="handleUpdate"
                 @row-save="handleSave"
                 @row-del="handleDel"
                 @sort-change="sortChange"
                 @size-change="sizeChange"
                 @search-change="searchChange">
        <!-- 自定义订单状态列显示 -->
        <template slot="statusDesc" slot-scope="scope">
          <el-tag
            :type="getStatusTagType(scope.row.statusDesc)"
            size="small">
            {{ scope.row.statusDesc }}
          </el-tag>
        </template>

        <!-- 自定义退款状态列显示 -->
        <template slot="refundStatus" slot-scope="scope">
          <el-tag
            v-if="getLatestRefundStatus(scope.row)"
            :type="getRefundStatusTagType(getLatestRefundStatus(scope.row))"
            size="small">
            {{ getLatestRefundStatus(scope.row) }}
          </el-tag>
          <span v-else>-</span>
        </template>

        <!-- 自定义购买商品列显示 -->
        <template slot="purchaseProduct" slot-scope="scope">
          <span v-if="scope.row.listOrderItem && scope.row.listOrderItem.length > 0">
            {{ scope.row.listOrderItem[0].spuName }}|{{ scope.row.listOrderItem[0].specInfo }}
          </span>
          <span v-else>-</span>
        </template>
      </avue-crud>
    </basic-container>
  </div>
</template>

<script>
import {getPage, getObj, addObj, putObj, delObj} from '@/api/viewgen/orderlist'
import {tableOption} from '@/const/crud/viewgen/orderlist'
import {mapGetters} from 'vuex'
import {getList as getWxAppList} from "@/api/wxmp/wxapp";
import {getListByType, getListByTypeList} from "@/api/viewgen/pagedevise";

export default {
  name: 'orderList',
  data() {
    return {
      phone:'',
      paymentPrice:'',
      appIdList: [],//所选公众号
      wxAppList: [],
      currentPageTypeList: [],//当前页面类型
      pageTypeList: [{
        value: '1',
        label: '首页'
      }, {
        value: '2',
        label: '拼团活动'
      }, {
        value: '3',
        label: '作品详情'
      }, {
        value: '4',
        label: '图文表单'
      }, {
        value: '5',
        label: '客照分享'
      }],
      currentOrderTypeList: [],//当前订单类型
      pageId: [],//所选页面
      pageList: [],
      orderTypeList: [
        //   {
        //   label: '普通订单',
        //   value: '0'
        // }, {
        //   label: '砍价订单',
        //   value: '1'
        // },
        // {
        //   label: '秒杀订单',
        //   value: '3'
        // },
        {
          label: '拼团订单',
          value: '2'
        }, {
          label: '按钮订单',
          value: '4'
        }],
      currentPayType: "",//当前订单类型
      payTypeList: [
        {
          label: '是',
          value: '1'
        }, {
          label: '否',
          value: '0'
        }],
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption
    }
  },
  created() {
    this.getWxApp();
  },
  mounted() {
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.permissions['weixinapi:orderinfo:add'] ? true : false,
        delBtn: this.permissions['weixinapi:orderinfo:del'] ? true : false,
        editBtn: this.permissions['weixinapi:orderinfo:edit'] ? true : false,
        viewBtn: this.permissions['weixinapi:orderinfo:get'] ? true : false
      };
    }
  },
  methods: {
    getWxApp() {
      getWxAppList({
      }).then(res => {
        let data = res.data
        this.wxAppList = data;
        for (let i = 0; i < data.length; i++) {
          tableOption.column[7].dicData.push({label: data[i].name, value: data[i].id});
        }
      }).catch(() => {
      })
    },
    // 根据订单状态返回对应的标签类型
    getStatusTagType(statusDesc) {
      if (statusDesc === '已完成') {
        return 'success'
      } else if (statusDesc === '已取消') {
        return 'danger' // Element UI的el-tag使用danger表示错误状态
      } else {
        return '' // 默认样式
      }
    },
    // 获取最新的退款状态
    getLatestRefundStatus(row) {
      if (row.listOrderRefunds && row.listOrderRefunds.length > 0) {
        const latestRefund = row.listOrderRefunds[row.listOrderRefunds.length - 1]
        return latestRefund.statusDesc ||latestRefund.status ||  '-'
      }
      return null
    },
    // 根据退款状态返回对应的标签类型
    getRefundStatusTagType(statusDesc) {
      if (statusDesc === '退款申请中') {
        return 'warning' // 黄色
      } else if (statusDesc === '同意退款') {
        return 'danger' // 红色
      } else if (statusDesc === '拒绝') {
        return '' // primary蓝色（默认）
      } else {
        return 'info' // 其他状态用info灰色
      }
    },
    searchChange(params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    sortChange(val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    sizeChange(val) {
      this.page.pageSize = val;
    },
    getPage(page, params) {
      if(this.paymentPrice && this.paymentPrice !=0){
        if(!/^[0-9]+.?[0-9]*/.test(this.paymentPrice) ){
          this.$message.warning("请填写正确的支付金额")
          return ;
        }
      }
      params = {
        pageId:this.pageId,
        appIdList: this.appIdList,
        pageTypeList: this.currentPageTypeList,
        orderTypeList: this.currentOrderTypeList,
        isPay: this.currentPayType,
        phone: this.phone,
        paymentPrice: this.paymentPrice,
      }
      this.tableLoading = true
      getPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
      }, params, this.paramsSearch)).then(res => {
        this.tableData = res.data.data.records
        this.page.total = res.data.data.total
        this.page.currentPage = res.data.data.current
        this.page.pageSize = res.data.data.size
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })

    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this
      this.$confirm('是否确认删除此数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        return delObj(row.id)
      }).then(data => {
        _this.$message({
          showClose: true,
          message: '删除成功',
          type: 'success'
        })
        this.getPage(this.page)
      }).catch(function (err) {
      })
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      putObj(row).then(res => {
        this.$message({
          showClose: true,
          message: '修改成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      addObj(row).then(res => {
        this.$message({
          showClose: true,
          message: '添加成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page)
    },
    //公众号类型更改
    appChange() {
      this.getPageByType()
    },
    //页面类型更改
    pageTypeChange() {
      this.getPageByType()
    },
    //拿取具体页面
    getPageByType() {
      if (!this.currentPageTypeList || this.currentPageTypeList.length == 0) {
        this.pageList = [];
        this.pageId = '';
        return;
      }
      let params = {
        appIdList: this.appIdList,
        pageTypeList: this.currentPageTypeList,
      }
      getListByTypeList(Object.assign(params)).then(res => {
        this.pageList = res.data.data;
      }).catch()
    },
    //
    search() {
      this.getPage(this.page)
    },
    //重置页面
    reset() {
      this.appIdList = [];
      this.currentPageTypeList=[];
      this.currentOrderTypeList =[];
      this.currentPayType ="";
      this.pageList = []
      this.pageId ='';
      this.phone ='';
      this.paymentPrice ='';
      this.getPage(this.page)
    },
  }
}
</script>

<style lang="scss" scoped>
//.search_form{
//   height: 40px;
//}
</style>
