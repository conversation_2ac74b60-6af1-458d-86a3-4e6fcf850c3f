<template>
    <div class="goodsContentComponent" :style="{background: `${setData.backColor}`, marginBottom: `${setData.pageSpacing}px`}">
        <div   :style="{paddingTop: `${setData.topAndBottomSpacing}px`, paddingBottom: `${setData.topAndBottomSpacing}px`, paddingLeft: `${setData.leftAndRightSpacing}px`, paddingRight: `${setData.leftAndRightSpacing}px`, }">
          <el-image class="imgBlock"  src="https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg" >
          </el-image>
        </div>
        <div   :style="{paddingTop: `${setData.topAndBottomSpacing}px`, paddingBottom: `${setData.topAndBottomSpacing}px`, paddingLeft: `${setData.leftAndRightSpacing}px`, paddingRight: `${setData.leftAndRightSpacing}px`, }">
          <el-image   class="imgBlock" src="https://fuss10.elemecdn.com/3/28/bbf893f792f03a54408b3b7a7ebf0jpeg.jpeg" >
          </el-image>
        </div>
    </div>
</template>

<script>
import { mapState  , mapMutations  } from 'vuex';
import placeholderImg from "../pages/page-components/placeholderImg";

export default {
    data() {
        return {};
    },
    components: { placeholderImg },
    props: {
        theme : { type: Object | Array },
        setData : { type: Object | Array },
        cId     : { type: Number },
        noEditor: {
            type: Boolean,
            default: false,
        }
    },
    computed: {
        ...mapState({
            componentsList: state => state.divpage.componentsList,
        }),
    },
    created() {
    },
    mounted() {
    },
    methods: {
        ...mapMutations([
            'updateData'
        ]),
    },
    watch:{
        setData(newVal, oldVal){},
        componentsList(newVal, oldVal){          //添加的时候出发（解决没有移动的时候不emit数据）
            let that = this;
            that.updateData({componentsList: that.componentsList})
        }
    },
    beforeDestroy(){
        // this.$root.Bus.$off('addHotSpot')
    },
};
</script>
<style lang='less' scoped>
  .imgBlock{
    display: block;

  }
  .goodsCoverComponent {
    position: relative;
    width: 100%;
    background: #ffffff;
  }
</style>
