export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  excelBtn: true,
  printBtn: true,
  viewBtn: true,
  searchMenuSpan: 6,
  column: [
    {
      label: 'PK',
      prop: 'id',
      sortable: true,
      rules: [
        {
          required: true,
          message: '请输入PK',
          trigger: 'blur'
        },
        {
          max: 32,
          message: '长度在不能超过32个字符'
        },
      ]
    },
    {
      label: '所属租户',
      prop: 'tenantId',
      sortable: true,
      rules: [
        {
          required: true,
          message: '请输入所属租户',
          trigger: 'blur'
        },
        {
          max: 32,
          message: '长度在不能超过32个字符'
        },
      ]
    },
    {
      label: '逻辑删除标记（0：显示；1：隐藏）',
      prop: 'delFlag',
      sortable: true,
      rules: [
        {
          required: true,
          message: '请输入逻辑删除标记（0：显示；1：隐藏）',
          trigger: 'blur'
        },
        {
          max: 2,
          message: '长度在不能超过2个字符'
        },
      ]
    },
    {
      label: '创建时间',
      prop: 'createTime',
      sortable: true,
      rules: [
        {
          required: true,
          message: '请输入创建时间',
          trigger: 'blur'
        },
      ]
    },
    {
      label: '最后更新时间',
      prop: 'updateTime',
      sortable: true,
      rules: [
        {
          required: true,
          message: '请输入最后更新时间',
          trigger: 'blur'
        },
      ]
    },
    {
      label: '分销级别（1：一级分销；2：二级分销）',
      prop: 'distributionLevel',
      sortable: true,
      rules: [
        {
          required: true,
          message: '请输入分销级别（1：一级分销；2：二级分销）',
          trigger: 'blur'
        },
        {
          max: 2,
          message: '长度在不能超过2个字符'
        },
      ]
    },
    {
      label: '分销员ID',
      prop: 'distributionUserId',
      sortable: true,
      rules: [
        {
          required: true,
          message: '请输入分销员ID',
          trigger: 'blur'
        },
        {
          max: 31,
          message: '长度在不能超过31个字符'
        },
      ]
    },
    {
      label: '订单id',
      prop: 'orderId',
      sortable: true,
      rules: [
        {
          required: true,
          message: '请输入订单id',
          trigger: 'blur'
        },
        {
          max: 32,
          message: '长度在不能超过32个字符'
        },
      ]
    },
    {
      label: '店铺id',
      prop: 'shopId',
      sortable: true,
      rules: [
        {
          max: 31,
          message: '长度在不能超过31个字符'
        },
      ]
    },
    {
      label: '用户id',
      prop: 'userId',
      sortable: true,
      rules: [
        {
          max: 31,
          message: '长度在不能超过31个字符'
        },
      ]
    },
    {
      label: '返佣金额',
      prop: 'commission',
      sortable: true,
      rules: [
        {
          required: true,
          message: '请输入返佣金额',
          trigger: 'blur'
        },
      ]
    },
    {
      label: '佣金状态（1：冻结；2：解冻）',
      prop: 'commissionStatus',
      sortable: true,
      rules: [
        {
          required: true,
          message: '请输入佣金状态（1：冻结；2：解冻）',
          trigger: 'blur'
        },
        {
          max: 2,
          message: '长度在不能超过2个字符'
        },
      ]
    },
  ]
}

export const tableOption2 = {
  dialogDrag: true,
  border: true,
  index: false,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  refreshBtn: false,
  columnBtn: false,
  addBtn: false,
  delBtn: false,
  searchMenuSpan: 6,
  dateBtn: true,
  column: [
    {
      label: '创建时间',
      prop: 'createTime',
      rules: [
        {
          required: true,
          message: '请输入创建时间',
          trigger: 'blur'
        },
      ],
      display: false
    },
    {
      label: '订单编号',
      prop: 'orderId',
      slot: true,
      rules: [
        {
          required: true,
          message: '请输入订单id',
          trigger: 'blur'
        },
        {
          max: 32,
          message: '长度在不能超过32个字符'
        },
      ],
      display: false
    },
    {
      label: '用户信息',
      prop: 'userId',
      slot: true,
      width: 220,
      rules: [
        {
          max: 31,
          message: '长度在不能超过31个字符'
        },
      ],
      display: false
    },
    {
      label: '返佣金额',
      prop: 'commission',
      rules: [
        {
          required: true,
          message: '请输入返佣金额',
          trigger: 'blur'
        },
      ]
    },
    {
      label: '佣金状态',
      prop: 'commissionStatus',
      type: 'radio',
      search: true,
      rules: [
        {
          required: true,
          message: '请输入佣金状态（1：冻结；2：解冻）',
          trigger: 'blur'
        },
        {
          max: 2,
          message: '长度在不能超过2个字符'
        },
      ],
      dicData: [{
        label: '冻结',
        value: '1'
      }, {
        label: '解冻',
        value: '2'
      }]
    },
  ]
}
