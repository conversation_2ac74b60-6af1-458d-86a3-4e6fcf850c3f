export const tableOption = {
  dialogType: 'drawer',
  border: true,
  stripe: true,
  columnBtn: false,//列的显隐按钮
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  excelBtn: true,
  printBtn: true,
  dialogWidth: '88%',
  selection: true,
  searchMenuSpan: 6,
  column: [
    {
      label: '商品名称',
      prop: 'name',
      search: true,
      display: false
    },
    // {
    //   label: '卖点',
    //   prop: 'sellPoint',
    //   sortable: true,
    //   display: false
    // },
    {
      label: '价格',
      prop: 'priceDown',
      slot: true,
      display: false
    },
    // {
    //   label: '商品编码',
    //   prop: 'spuCode',
    //   search: true,
    //   sortable: true,
    //   display: false
    // },
    // {
    //   label: '虚拟销量',
    //   prop: 'saleNum',
    //   sortable: true,
    //   display: false
    // },
    {
      label: '创建时间',
      prop: 'createTime',
      sortable: true,
      display: false
    },
    {
      label: '更新时间',
      prop: 'updateTime',
      sortable: true,
      display: false
    },
    {
      label: '是否上架',
      prop: 'shelf',
      type: 'radio',
      search: true,
      sortable: true,
      slot: true,
      display: false,
      dicData: [{
        label: '下架',
        value: '0'
      }, {
        label: '上架',
        value: '1'
      }]
    },
  ],
  group: [
    {
      icon: 'el-icon-goods',
      label: '基本信息',
      prop: 'group1',
      column: [
        {
          label: '商品名称',
          prop: 'name',
          span: 24,
          rules: [{
            required: true,
            message: '商品名称不能为空',
            trigger: 'blur'
          }, {
            max: 100,
            message: '长度在不能超过100个字符'
          }]
        },
        {
          label: '是否上架',
          prop: 'shelf',
          type: 'radio',
          rules: [{
            required: true,
            message: '请选择是否上架',
            trigger: 'blur'
          }],
          dicData: [{
            label: '下架',
            value: '0'
          }, {
            label: '上架',
            value: '1'
          }]
        },
        // {
        //   label: '商品编码',
        //   prop: 'spuCode',
        //   rules: [{
        //     max: 32,
        //     message: '长度在不能超过32个字符'
        //   }]
        // },
        // {
        //   label: '虚拟销量',
        //   prop: 'saleNum',
        //   type: 'number',
        //   tip: '可以按自己需求设置，系统会按销售情况自动累加'
        // },
        // {
        //   label: '卖点',
        //   prop: 'sellPoint',
        //   span: 24,
        //   rules: [{
        //     max: 500,
        //     message: '长度在不能超过500个字符'
        //   }]
        // }
        ]
    },
    {
      icon: 'el-icon-s-order',
      label: '规格信息',
      prop: 'group2',
      column: [
      //   {
      //     label: '规格类型',
      //     prop: 'specType',
      //     type: 'radio',
      //     rules: [{
      //       required: true,
      //       message: '请选择规格类型',
      //       trigger: 'blur'
      //     }],
      //     dicData: [{
      //       label: '统一规格',
      //       value: '0'
      //     }, {
      //       label: '多规格',
      //       value: '1'
      //     }]
      //   },
        {
          prop: 'skus',
          formslot: true,
          span: 24
        },
    ]
    },
    //   {
    //   icon: 'el-icon-grape',
    //   label: '辅助信息',
    //   prop: 'group5',
    //   column: [
    //     {
    //       label: '描述',
    //       prop: 'description',
    //       formslot: true,
    //       span: 12
    //     }]
    // }
    ]
}

export const tableOption2 = {
  dialogType: 'drawer',
  border: true,
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  searchShow: false,
  dialogWidth: '88%',
  editBtn: false,
  delBtn: false,
  addBtn: false,
  selection: true,
  menu: false,
  maxHeight: 450,
  column: [
    // {
    //   label: '店铺',
    //   prop: 'shopId',
    //   type: 'select',
    //   filterable: true,
    //   props: {
    //     label: 'name',
    //     value: 'id'
    //   },
    //   dicUrl: '/mall/shopinfo/list'
    // },
    {
      label: '商品名称',
      prop: 'name',
      search: true
    },
    // {
    //   label: '商品图片',
    //   prop: 'picUrls',
    //   width: 120,
    //   slot: true
    // },
    // {
    //   label: '商城商品类目',
    //   prop: 'categoryId',
    //   type: 'cascader',
    //   search: true,
    //   props: {
    //     label: 'name',
    //     value: 'id'
    //   },
    //   dicUrl: '/mall/goodscategory/tree'
    // },
    // {
    //   label: '店铺商品类目',
    //   prop: 'categoryShopId',
    //   type: 'cascader',
    //   search: true,
    //   props: {
    //     label: 'name',
    //     value: 'id'
    //   },
    //   dicUrl: '/mall/goodscategoryshop/tree'
    // },
    // {
    //   label: '卖点',
    //   prop: 'sellPoint',
    //   sortable: true
    // },
    {
      label: '价位',
      prop: 'price',
      slot: true
    },
    // {
    //   label: '商品编码',
    //   prop: 'spuCode',
    //   search: true,
    //   sortable: true
    // },
    // {
    //   label: '规格类型',
    //   prop: 'specType',
    //   search: true,
    //   type: 'radio',
    //   dicData: [{
    //     label: '统一规格',
    //     value: '0'
    //   }, {
    //     label: '多规格',
    //     value: '1'
    //   }]
    // },
    // {
    //   label: '虚拟销量',
    //   prop: 'saleNum',
    //   sortable: true
    // },
    {
      label: '创建时间',
      prop: 'createTime',
      sortable: true
    },
    {
      label: '更新时间',
      prop: 'updateTime',
      sortable: true
    },
    {
      label: '是否上架',
      prop: 'shelf',
      type: 'radio',
      search: true,
      sortable: true,
      slot: true,
      dicData: [{
        label: '下架',
        value: '0'
      }, {
        label: '上架',
        value: '1'
      }]
    },
    // {
    //   label: '积分赠送',
    //   prop: 'pointsGiveSwitch',
    //   type: 'radio',
    //   search: true,
    //   sortable: true,
    //   dicData: [{
    //     label: '开启',
    //     value: '1'
    //   }, {
    //     label: '关闭',
    //     value: '0'
    //   }]
    // },
    // {
    //   label: '积分抵扣',
    //   prop: 'pointsDeductSwitch',
    //   type: 'radio',
    //   search: true,
    //   sortable: true,
    //   dicData: [{
    //     label: '开启',
    //     value: '1'
    //   }, {
    //     label: '关闭',
    //     value: '0'
    //   }]
    // },
    // {
    //   label: '审核状态',
    //   prop: 'verifyStatus',
    //   type: 'select',
    //   search: true,
    //   filterable: true,
    //   sortable: true,
    //   dicData: [{
    //     label: '审核中',
    //     value: '0'
    //   }, {
    //     label: '审核通过',
    //     value: '1'
    //   }, {
    //     label: '审核不通过',
    //     value: '2'
    //   }]
    // }
  ]
}
