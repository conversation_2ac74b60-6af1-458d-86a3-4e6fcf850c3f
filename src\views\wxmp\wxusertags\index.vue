<template>
  <div class="execution">
    <basic-container>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              placeholder="请输入要搜索的标签"
              size="small"
              @keyup.enter.native="search"
              v-model="searchValue">
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" size="small" @click="search">确认搜索</el-button>
            <el-button type="primary" size="small" @click="resetSearch">重置</el-button>
          </el-col>
          <el-col :span="14">
            <el-button type="primary" size="small" @click="openTagBox('add')"><i class="el-icon-circle-plus"></i>新建标签</el-button>
            <el-button type="primary" size="small"  @click="openManagerBox()"><i class="el-icon-s-data"></i>高级管理</el-button>
<!--            日后再开发-->
<!--            <el-button type="primary" size="small"><i></i>模版群发</el-button>-->
<!--            <el-button type="primary" size="small"><i></i>微信标签群发</el-button>-->
<!--            <el-button type="primary" size="small"><i></i>模版群发记录</el-button>-->
          </el-col>
        </el-row>
        <el-row >
          <el-col :span="3">
            <el-card shadow="never">
              <div slot="header">
                <span>公众号名称</span>
              </div>
              <el-tree
                style="margin-top: 5px"
                :data="treeWxAppData"
                :props="treeWxAppProps"
                :filter-node-method="filterNode"
                node-key="id"
                default-expand-all
                ref="tree"
                @node-click="nodeClick">
              </el-tree>
            </el-card>
          </el-col>
          <el-col :span="3">
            <div class="aside_tag_type">
              <div>
                <el-button type="success" style="margin: 20px 0px 0px 0px " @click="openTagTypeManagerBox" size="small"><i></i>分类管理</el-button>
              </div>
              <ul v-for="(item,index) in tagTypeList" :key="index">
                <li class="tag_type_li" @click="ensureLocation(item.id)" >
                  <h4 class="tag_type_name">{{item.name}}</h4>
                </li>
              </ul>
            </div>
          </el-col>
          <el-col :span="18">
            <div class="main_tag_show">
              <div v-for="(item,index) in tagAndTypeList"  :key="index">
                <div :id="'tagTypeId'+item.typeId">
                  <el-divider content-position="left">{{ item.tagTypeName }}</el-divider>
                  <div class="main_tag" v-for="(tag,index) in item.tagList" :key="index">
                    <el-popover
                      :ref="tag.id"
                      placement="bottom"
                      width="150px"
                      trigger="hover">
                      <div style=" text-align: right; margin: 0;padding: 0">
                        <el-button class="main_popover_button" icon="el-icon-user-solid" type="mini" size="mini" @click="openDrawerVisible(tag)">管理用户</el-button>
                        <el-button class="main_popover_button" icon="el-icon-edit" type="mini" size="mini" @click="openTagBox('put',tag)">修改名称</el-button>
                        <el-button class="main_popover_button" icon="el-icon-delete-solid" type="mini" size="mini" @click="openDelTag(tag.id)">删除标签</el-button>
<!--                        有需求再做-->
<!--                        <el-button class="main_popover_button" icon="el-icon-chat-dot-round" type="mini" size="mini">上传公众号</el-button>-->
                      </div>
                    </el-popover>
                    <el-tag   v-popover="tag.id"  size="small" :color="tag.backColor" :style="getFontColor(tag.fontColor)">{{tag.name +" ("+tag.number+")"}}</el-tag>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
    </basic-container>

<!--    添加标签框-->
    <el-dialog
      :title="tagBox.title"
      :visible.sync="tagBox.visible"
      width="50%"
      :close-on-click-modal="false"
      center>
      <el-form ref="form" :model="tag" label-width="80px">
        <el-form-item label="创建方式" v-if="tagBox.type=='add'">
          <el-radio-group v-model="tag.operationType" size="mini">
            <el-radio border :label="0">普通创建</el-radio>
            <el-radio border :label="1">条件创建</el-radio>
          </el-radio-group>
        </el-form-item>
        <div v-if="tag.operationType == 1">
          <el-form-item label="关注状态">
            <el-radio-group v-model="tag.subscribeRule" size="mini">
              <el-radio  :label="0">不判断</el-radio>
              <el-radio  :label="1">已关注</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="电话状态">
            <el-radio-group v-model="tag.phoneRule" size="mini">
              <el-radio  :label="0">不判断</el-radio>
              <el-radio  :label="1">有电话</el-radio>
              <el-radio  :label="2">无电话</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="黑名单">
            <el-radio-group v-model="tag.backListRule" size="mini">
              <el-radio  :label="0">不判断</el-radio>
              <el-radio  :label="1">黑名单用户</el-radio>
              <el-radio  :label="2">正常用户</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="节点选择">
            <el-select v-model="tag.node" placeholder="请选择">
              <el-option
                v-for="item in nodeList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="日期范围">
            <el-date-picker
              v-model="tagDateRange"
              type="datetimerange"
              :picker-options="pickerOptions"
              value-format="yyyy-MM-dd HH:mm:ss"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              align="right">
            </el-date-picker>
          </el-form-item>
        </div>
        <el-form-item label="标签名称">
          <el-input v-model="tag.name"  :maxlength="25"  show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="标签分类">
          <el-select v-model="tag.typeId" clearable placeholder="请选择">
            <el-option
              v-for="item in tagTypeList"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
          <el-button type="primary" size="mini" @click="openTagTypeBox">添加分类</el-button>
        </el-form-item>
        <el-row  >
          <el-col :span="12">
            <el-form-item label="背景色">
              <el-color-picker v-model="tag.backColor"></el-color-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="字体色">
              <el-color-picker v-model="tag.fontColor"></el-color-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="是否置顶">
          <el-switch v-model="tag.topFlag" ></el-switch>
        </el-form-item>
        <el-form-item label="效果展示" v-show="tag.name">
          <el-tag   size="small" :color="tag.backColor" :style="getFontColor(tag.fontColor)">{{tag.name}}</el-tag>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="confirmTag">确认</el-button>
          <el-button @click="tagBox.visible = false">取消</el-button>
        </el-form-item>
      </el-form>

<!--     嵌套添加分类框-->
      <el-dialog
        width="30%"
        title="添加分类"
        :visible.sync="tagTypeBoxVisible"
        :close-on-click-modal="false"
        append-to-body>
        <el-form ref="form" :model="tagType" label-width="80px">
          <el-form-item label="分类名称">
            <el-input v-model="tagType.name"  :maxlength="25"  show-word-limit></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="addTagType">立即创建</el-button>
            <el-button @click="tagTypeBoxVisible = false">取消</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </el-dialog>
<!--    高级管理框-->
    <el-dialog
      title="高级管理"
      :visible.sync="manageBoxVisible"
      width="60%"
      @close="manageBoxClose"
      :close-on-click-modal="false"
      center>
      <el-form ref="tagManageForm" :model="tagManageForm" :rules="tagManageFormRules" label-width="80px">
        <el-form-item label="操作方式">
          <el-radio-group v-model="tagManageForm.operationBehavior" size="mini" @change="operationBehaviorChange">
            <el-radio border :label="'0'">合并标签</el-radio>
            <el-radio border :label="'1'">剔除标签</el-radio>
          </el-radio-group>
        </el-form-item>
        <div v-show="tagManageForm.operationBehavior=='0'">
          <el-form-item label="目标标签" prop="aimTagList">
            <div @click="openManagerTagBox('aim')">
              <el-tag  v-for="(item,index) in tagManageForm.aimTagList" :key="index"  size="medium" :color="item.backColor?item.backColor:''"
                       :style="item.fontColor?getFontColor(item.fontColor):''">{{ item.name?item.name:''}} </el-tag>
              <el-button  icon="el-icon-plus" size="mini"></el-button>
            </div>
          </el-form-item>
          <el-form-item label="结果标签" prop="resultTag">
            <span @click="openManagerTagBox('result')">
              <el-button v-if="!tagManageForm.resultTag"  icon="el-icon-plus" size="mini"></el-button>
                <el-tag  v-if="tagManageForm.resultTag" size="medium" :color="tagManageForm.resultTag?tagManageForm.resultTag.backColor:''"
                         :style="tagManageForm.resultTag?getFontColor(tagManageForm.resultTag.fontColor):''">{{ tagManageForm.resultTag?tagManageForm.resultTag.name:''}} </el-tag>
            </span>
            <el-button v-if="tagManageForm.resultTag"  @click="deleteResultTag" icon="el-icon-delete" size="mini" type="danger"></el-button>
          </el-form-item>
          <el-form-item label="合并方式" prop="mergeType">
            <el-radio-group v-model="tagManageForm.mergeType"  size="mini">
              <el-radio :label="'0'">将所有目标标签内的用户，合并到结果标签</el-radio>
              <el-radio :label="'1'">将同时被目标标签，标记的用户，合并到结果标签</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="清空规则">
              <div>
                操作完成后，清空目标标签
                <el-switch
                  v-model="tagManageForm.emptyAim"
                  active-text="是"
                  inactive-text="否"
                  active-color="#13ce66"
                  inactive-color="#ff4949">
                </el-switch>
              </div>
              <div>
                操作完成前，清空结果标签
                <el-switch
                  v-model="tagManageForm.emptyResult"
                  active-text="是"
                  inactive-text="否"
                  active-color="#13ce66"
                  inactive-color="#ff4949">
                </el-switch>
              </div>
          </el-form-item>
        </div>
        <div v-show="tagManageForm.operationBehavior=='1'">
          <el-form-item label="目标标签" prop="aimTagList">
            <div @click="openManagerTagBox('aim')">
              <el-tag  v-for="(item,index) in tagManageForm.aimTagList" :key="index"  size="medium" :color="item.backColor?item.backColor:''"
                       :style="item.fontColor?getFontColor(item.fontColor):''">{{ item.name?item.name:''}} </el-tag>
              <el-button  icon="el-icon-plus" size="mini"></el-button>
            </div>
          </el-form-item>
          <el-form-item label="剔除标签" prop="outTagList">
            <div @click="openManagerTagBox('out')">
              <el-tag  v-for="(item,index) in tagManageForm.outTagList" :key="index"  size="medium" :color="item.backColor?item.backColor:''"
                       :style="item.fontColor?getFontColor(item.fontColor):''">{{ item.name?item.name:''}}</el-tag>
              <el-button  icon="el-icon-plus" size="mini"></el-button>
            </div>
          </el-form-item>

          <el-form-item label="结果标签">
            <span @click="openManagerTagBox('result')">
              <el-button v-if="!tagManageForm.resultTag"  icon="el-icon-plus" size="mini"></el-button>
              <el-tag  v-if="tagManageForm.resultTag" size="medium" :color="tagManageForm.resultTag?tagManageForm.resultTag.backColor:''"
                       :style="tagManageForm.resultTag?getFontColor(tagManageForm.resultTag.fontColor):''">{{ tagManageForm.resultTag?tagManageForm.resultTag.name:''}} </el-tag>
            </span>
            <el-button v-if="tagManageForm.resultTag"  @click="deleteResultTag" icon="el-icon-delete" size="mini" type="danger"></el-button>
          </el-form-item>
          <el-form-item label="注意事项">
            <el-alert
              type="error"
              :closable="false"
              description="如果C标签留空，将直接在所有A标签中汰除B标签内的用户！！">
            </el-alert>
            <el-alert
              type="success"
              :closable="false"
              description="将所有[A]标签内用户汰除[B]标签用户后，剩余所有[A]标签用户存储到[C]标签">
            </el-alert>
          </el-form-item>
          <el-form-item label="清空规则">
            <div>
              操作完成后，清空目标标签
              <el-switch
                v-model="tagManageForm.emptyAim"
                active-text="是"
                inactive-text="否"
                active-color="#13ce66"
                inactive-color="#ff4949">
              </el-switch>
            </div>
            <div>
              操作完成后，清空剔除标签
              <el-switch
                v-model="tagManageForm.emptyOut"
                active-text="是"
                inactive-text="否"
                active-color="#13ce66"
                inactive-color="#ff4949">
              </el-switch>
            </div>
            <div>
              操作完成前，清空结果标签
              <el-switch
                v-model="tagManageForm.emptyResult"
                active-text="是"
                inactive-text="否"
                active-color="#13ce66"
                inactive-color="#ff4949">
              </el-switch>
            </div>
          </el-form-item>
        </div>
        <el-form-item>
          <el-button type="primary" @click="confirmManager">确认操作</el-button>
          <el-button @click="manageBoxVisible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
<!--    分类管理-->
    <el-dialog title="分类管理" :visible.sync="openTagTypeManagerBoxVisible">
      <template>
        <div class="execution">
          <basic-container>
            <avue-crud ref="crud"
                       :page="page"
                       :data="tableData"
                       :permission="permissionList"
                       :table-loading="tableLoading"
                       :option="tagTypeOption"
                       @row-save="handleSave"
                       @on-load="getTagTypePage"
                       @refresh-change="refreshChange"
                       @row-update="handleUpdate"
                       @row-del="handleDel">
            </avue-crud>
          </basic-container>
        </div>
      </template>
    </el-dialog>
<!--    用户管理-->
    <el-drawer
      title="相关用户"
      :visible.sync="drawerVisible"
      :direction="'rtl'"
      :size="'80%'"
      :before-close="drawerClose">
      <user-list ref="userListRef" :selectedTagId="drawerSelectedTagId" :appId="currentApp.id" @reFresh="search"></user-list>
    </el-drawer>
<!--    用户标签弹出框-->
    <el-dialog title="用户标签" :visible.sync="managerTagVisible">
      <div style="overflow: hidden">
        <wx-user-tag  :selectedType="tagManageForm.tagBoxSelectedType"  :selectedTagId="manageSelectedTagId"  :appId="currentApp.id" v-on:ensureTag="ensureTag" @backFun="ensureTag"></wx-user-tag>
      </div>
    </el-dialog>



  </div>
</template>

<script>
  import { getSysList as getTagList,addObj as addTag,putObj as putTag, delObj as delTag,addAttention,tagManger} from '@/api/wxmp/wxusertags'
  import { getPage as getTagTypePage,getList as getTagTypeList,addObj as addTagType, putObj as putTagType, delObj as delTagType} from '@/api/wxmp/wxtagtype'
  import { tagTypeOption } from '@/const/crud/wxmp/wxtagtype'
  import { mapGetters } from 'vuex'
  import {getList as getWxAppList} from "@/api/wxmp/wxapp";
  import userList from '@/views/wxmp/wxusertags/taguser.vue'
  import wxUserTag from "@/views/wxmp/wxusertags/userTagSelect";
  export default {
    name: 'wxusertags',
    components: {
      userList,
      wxUserTag
    },
    data() {
      return {
        managerTagVisible:false,//用户标签弹出框
        nodeList: [{
          value: '选项1',
          label: '黄金糕'
        }, {
          value: '选项2',
          label: '双皮奶'
        }, {
          value: '选项3',
          label: '蚵仔煎'
        }, {
          value: '选项4',
          label: '龙须面'
        }, {
          value: '选项5',
          label: '北京烤鸭'
        }],
        tagDateRange:[],//标签条件
        pickerOptions: {
          shortcuts: [{
            text: '最近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }]
        },
        manageBoxVisible: false,//高级管理框
        tagManageForm: {},//标签高级管理
        tagManageFormRules: {
          aimTagList: [{ required: true, trigger: 'blur',message: '目标标签不能为空'}],
          outTagList: [{ required: true, trigger: 'blur',message: '剔除标签不能为空'}],
          resultTag: [{ required: true, trigger: 'blur',message: '结果标签不能为空'}],
          mergeType: [{ required: true, trigger: 'blur',message: '请选择合并方式'}],
        },
        treeWxAppProps: {
          label: 'name',
          value: 'id'
        },
        treeWxAppData: [],
        drawerSelectedTagId:'',//打开抽屉选中的标签id
        manageSelectedTagId:'',//高级管理选中的标签id
        drawerVisible: false,
        tableLoading: false,
        tagTypeOption: tagTypeOption,
        tableData: [],
        page: {
          total: 0, // 总页数
          currentPage: 1, // 当前页数
          pageSize: 20, // 每页显示多少条
          ascs: [],//升序字段
          descs: []//降序字段
        },
        tagBox: {
          title: '',
          visible: false,
          type: ''
        },
        tagTypeBoxVisible: false,//内部分类框
        openTagTypeManagerBoxVisible: false,//分类管理
        currentApp: "",//当前公众号
        searchValue: "",//标签搜索值
        tagTypeList: [],//标签类别数组
        tagList: [],//标签数组
        tagAndTypeList:[],
        tag:{},
        tagType:{},
      };
    },
    watch: {

    },
    created() {
      this.getWxAppList();

    },
    mounted: function() { },
    computed: {
      ...mapGetters(['permissions']),
      permissionList() {
        return {
          addBtn: this.permissions['wxmp:wxusertags:add'] ? true : false,
          delBtn: this.permissions['wxmp:wxusertags:del'] ? true : false,
          editBtn: this.permissions['wxmp:wxusertags:edit'] ? true : false,
          viewBtn: this.permissions['wxmp:wxusertags:get'] ? true : false
        }
      }
    },
    methods: {
      //点击查询
      nodeClick(data) {
        console.log("data",data)
        this.$nextTick(() => {
          this.$refs.tree.setCurrentKey(data.id)
        })
        this.currentApp = data;
        this.tableData = []
        this.page.total = 0
        this.page.currentPage = 1
        this.tagTypeList= [];//标签类别数组
        this.tagList = [];
        this.getTagTypeList();
        this.getTagList();
      },
      filterNode(value, data) {
        if (!value) return true
        return data.name.indexOf(value) !== -1
      },
      //加载公众号列表
      getWxAppList(){
        getWxAppList({
        }).then(res => {
          let data = res.data
          this.treeWxAppData = data
          if(data && data.length > 0){
            //默认加载第一个公众号的素材

            this.nodeClick({
              id: data[0].id
            })
            this.currentApp = data[0];
          }
        }).catch(() => {
        })
      },
      //加载标签类别
      getTagTypeList() {
        getTagTypeList({
          appId:this.currentApp.id
        }).then(res=>{
          this.tagTypeList = res.data.data;
        }).catch(err=>{
          console.log(err)
        })
      },
      //加载标签
      getTagList() {
        getTagList({
          appId:this.currentApp.id
        }).then(res=>{
          console.log("加载标签", res);
          this.tagAndTypeList = res.data.data;
        }).catch(err=>{
          console.log(err)
        })
      },
      //搜索标签
      search() {
        console.log("search")
        if(!this.currentApp ){
          return
        }
        getTagList({
          appId :this.currentApp.id,
          name: this.searchValue
        }).then(res=>{
          this.tagAndTypeList = res.data.data;
        }).catch(err=>{
          console.log(err)
        })
      },
      //重置搜索
      resetSearch() {
        this.searchValue ="";
        getTagList({
          appId :this.currentApp.id,
        }).then(res=>{
          this.tagAndTypeList = res.data.data;
        }).catch(err=>{
          console.log(err)
        })
      },
      openTagBox(type,obj) {
        if(type == 'add') {
          this.tag = {
            name: "",
            operationType: 0,
            subscribeRule: 0,
            phoneRule: 0,
            backListRule: 0,
            backColor: '#409eff',
            fontColor: '#fff',
          }
          this.tagBox.type = "add";
          this.tagBox.title = "添加标签";
        }
        if(type == 'put'){
          this.tagBox.type = "put";
          this.tagBox.title = "修改标签";
          this.tag = {
            id: obj.id,
            name: obj.name,
            backColor: obj.backColor,
            fontColor: obj.fontColor,
            typeId: obj.typeId,
            topFlag: obj.topFlag
          }
        }
        this.tagBox.visible = true;
      },
      openTagTypeBox() {
        this.tagType = {};
        this.tagTypeBoxVisible = true;
      },
      //转化得到字体颜色
      getFontColor(val){
        if(!val){
          return;
        }
        return "color:" + val;
      },
      // 添加标签
      confirmTag(){
        if (!this.tag.name) {
          this.$message.warning("请填写标签名称");
          return;
        }
        if (!this.tag.typeId) {
          this.$message.warning("请选择标签类型");
          return;
        }
        if(this.tagBox.attention && this.tagBox.type =='add') {
          console.log("保存参数", this.tag)
          addAttention({
            appId: this.currentApp.id,
            name: this.tag.name,
            backColor: this.tag.backColor,
            fontColor: this.tag.fontColor,
            typeId: this.tag.typeId,
          }).then(res => {
            if (res.data.code == 0) {
              this.$message.success("添加成功")
              this.getTagList();
              this.tagBox.visible = false;
            }
          }).catch()
        }
        if(this.tagBox.type =='add') {
          console.log("保存参数", this.tag)
          addTag({
            appId: this.currentApp.id,
            name: this.tag.name,
            backColor: this.tag.backColor,
            fontColor: this.tag.fontColor,
            typeId: this.tag.typeId,
            operationType:this.tag.operationType,
            subscribeRule:this.tag.subscribeRule,
            backListRule:this.tag.backListRule,
            phoneRule:this.tag.phoneRule,
            subscribeTimeBegin:this.tagDateRange[0],
            subscribeTimeEnd:this.tagDateRange[1],
          }).then(res => {
            if (res.data.code == 0) {
              this.$message.success("添加成功")
              this.getTagList();
              this.tagBox.visible = false;
            }
          }).catch()
        }
        if(this.tagBox.type =='put') {
          console.log("保存参数", this.tag)
          putTag({
            id: this.tag.id,
            appId: this.currentApp.id,
            name: this.tag.name,
            backColor: this.tag.backColor,
            fontColor: this.tag.fontColor,
            typeId: this.tag.typeId,
          }).then(res => {
            if (res.data.code == 0) {
              this.$message.success("修改成功")
              this.getTagList();
              this.tagBox.visible = false;
            }
          }).catch();
        }
      },
      // 添加分类
      addTagType(){
        if (!this.tagType.name) {
          this.$message.warning("请填写分类名称");
          return;
        }
        addTagType({
          appId:this.currentApp.id,
          name:this.tagType.name,
        }).then(res=>{
          if(res.data.code ==0){
            this.$message.success("添加成功")
            this.getTagTypeList();
            this.tagTypeBoxVisible = false;
          }
        }).catch()
      },
      // 点击确定定位
      ensureLocation(id){
        // todo 吸顶问题的解决
        // console.log(id)
        let name = ("tagTypeId" + id);
        document.getElementById(name).scrollIntoView({behavior: "smooth",block: "center", inline: "nearest"});
        // document.getElementById(name).scroll({
        //   top: height, //向上移动的距离，如果有fixede布局， 直接减去对应距离即可
        //   behavior: 'smooth', // 平滑移动
        // });
      },
      openDelTag(id) {
        this.$confirm('此操作将永久删除该标签, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          delTag(id).then(res => {
            if (res.data.code == 0) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              });
              this.refreshChange();
            }
          }).catch()
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },
      openTagTypeManagerBox() {
        this.openTagTypeManagerBoxVisible = true;
        this.getTagTypePage(this.page);
        console.log(this.tagTypeList);
      },
      /**
       * @title 数据删除
       * @param row 为当前的数据
       * @param index 为当前删除数据的行数
       *
       **/
      handleDel (row, index) {
        let _this = this
        this.$confirm('是否确认删除此数据', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then( ()=>{
          delTagType(row.id).then(()=>{
            this.refreshChange();
          })
        }).then(data => {
          _this.$message({
            showClose: true,
            message: '删除成功',
            type: 'success'
          })
          this.getTagTypePage(this.page)
        }).catch(function (err) {
        })
      },
      /**
       * @title 数据更新
       * @param row 为当前的数据
       * @param index 为当前更新数据的行数
       * @param done 为表单关闭函数
       *
       **/
      handleUpdate (row, index, done, loading) {
        putTagType(row).then(res => {
          this.$message({
            showClose: true,
            message: '修改成功',
            type: 'success'
          })
          done()
          this.getTagTypePage(this.page);
          this.getTagTypeList();
        }).catch(() => {
          loading()
        })
      },
      /**
       * 刷新回调
       */
      refreshChange() {
        this.getTagTypePage(this.page)
        this.getTagTypeList();
        this.getTagList();
      },
      getTagTypePage(page, params) {
        this.tableLoading = true
        getTagTypePage(Object.assign({
          current: page.currentPage,
          size: page.pageSize,
          descs: this.page.descs,
          ascs: this.page.ascs,
          appId:this.currentApp.id
        }, params, this.paramsSearch)).then(res => {
          this.tableData = res.data.data.records
          this.page.total = res.data.data.total
          this.page.currentPage = page.currentPage
          this.page.pageSize = page.pageSize
          this.tableLoading = false
        }).catch(() => {
          this.tableLoading = false
        })
      },
      //不用的
      handleSave(row, done, loading) {
        addTagType({
          appId:this.currentApp.id,
          name:row.name,
        }).then(res => {
          this.$message({
            showClose: true,
            message: '添加成功',
            type: 'success'
          })
          done()
          this.refreshChange()
        }).catch(() => {
          loading()
        })
      },
      //抽屉打开
      openDrawerVisible(tag){
        this.drawerSelectedTagId = tag.id;
        console.log('点击表气啊',tag.id)
        this.$nextTick(()=>{
          this.$refs.userListRef.initLoad()
        })
        this.drawerVisible = true;
      },
      //抽屉关闭
      drawerClose(done) {
        if (this.$refs.userListRef) {
          this.$refs.userListRef.resetSearch();
        }
        done();
      },
      //打开高级管理框
      openManagerBox(){
        this.tagManageForm = {
          operationBehavior: '0',
          emptyAim:false,
          emptyResult:false,
        };
        this.manageBoxVisible = true;
      },
      //打开高级管理框内的标签框
      openManagerTagBox(type){
        this.tagManageForm.openTagBoxType = type;
        if('aim'== type){
          this.tagManageForm.tagBoxSelectedType = 1;
          this.manageSelectedTagId=[];
          if(this.tagManageForm.aimTagList) {
            for (let i = 0; i < this.tagManageForm.aimTagList.length; i++) {
              this.manageSelectedTagId.push(this.tagManageForm.aimTagList[i].id)
            }
          }
        }else  if('result'== type){
          this.tagManageForm.tagBoxSelectedType = 0;
          this.manageSelectedTagId =  this.tagManageForm.resultTag;
        }else  if('out'== type){
          this.tagManageForm.tagBoxSelectedType = 1;
          this.manageSelectedTagId=[];
          if(this.tagManageForm.outTagList){
            for (let i = 0; i <this.tagManageForm.outTagList.length ; i++) {
              this.manageSelectedTagId.push(this.tagManageForm.outTagList[i].id)
            }
          }
        }
        this.managerTagVisible = true;
      },
      //高级管理框星行为类型改变
      operationBehaviorChange(val){
        this.tagManageForm={
          operationBehavior: val,
        }
      },
      manageBoxClose(){
        this.$refs['tagManageForm'].resetFields();
      },
      deleteResultTag() {
        this.$set(this.tagManageForm, 'resultTag', '');
        this.$forceUpdate();
      },
      //弹出标签框的确认
      ensureTag(obj) {
        // console.log("确认标签122", obj)
        if( 'aim'== this.tagManageForm.openTagBoxType){
          this.tagManageForm.aimTagList = obj;
        }else if( 'result'== this.tagManageForm.openTagBoxType){
          this.tagManageForm.resultTag = obj;
        }else if( 'out'== this.tagManageForm.openTagBoxType){
          this.tagManageForm.outTagList = obj;
        }
      },
      //高级管理框的确认
      confirmManager(){
        this.$refs.tagManageForm.validate(valid => {
          if (valid) {
            // console.log("tagManageForm", this.tagManageForm);
            let obj = {
              operationBehavior: this.tagManageForm.operationBehavior,
              aimTagList: [],
              outTagList: [],
              resultTagList: [],
              mergeType: this.tagManageForm.mergeType,
              emptyAim: this.tagManageForm.emptyAim,
              emptyResult: this.tagManageForm.emptyResult,
              emptyOut: this.tagManageForm.emptyOut,
            };
            for (let i = 0; i < this.tagManageForm.aimTagList.length; i++) {
              obj.aimTagList.push(this.tagManageForm.aimTagList[i].id)
            }
            if (this.tagManageForm.operationBehavior == '1') {
              for (let i = 0; i < this.tagManageForm.outTagList.length; i++) {
                obj.outTagList.push(this.tagManageForm.outTagList[i].id)
              }
            }
            if (this.tagManageForm.resultTag && this.tagManageForm.resultTag.id) {
              obj.resultTagList.push(this.tagManageForm.resultTag.id)
            }
            // console.log("obj", obj);
            tagManger(obj).then(res => {
              this.refreshChange()
              this.manageBoxVisible = false;
            }).catch(() => {
            })
          }
        });
      },
    }
  }
</script>

<style lang="scss" scoped>
.main_popover_button {
  display: block;
  margin: 0px 0px 5px 5px;
  display: block;
  width: 140px
}

.aside_tag_type{
  //position: fixed;
  text-align: center;
}
.main_tag_show{
  padding-left: 10px;
}

.tag_type_li{
  padding-top: 10px;
  border:1px solid white;
}
.tag_type_li:hover{
  color: #409eff;;
  border-bottom:1px solid #409eff;;
  cursor: pointer;
}

.tag_type_name{
  overflow: hidden;
  text-overflow: ellipsis;
  padding-bottom: 15px;
}
.main_tag{
  display: inline-block;
  padding-right: 20px;
  padding-bottom: 30px;
}
</style>
