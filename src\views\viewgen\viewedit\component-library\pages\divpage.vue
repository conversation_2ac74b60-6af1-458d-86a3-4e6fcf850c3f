<!-- 注：如需开发新的组件请参考 搜索组件（searchSetting.vue和search.vue)两个文件或其他已有的组件。 -->
<!-- 组件开发需要有显示组件 components 目录下和组件设置 components-setting 目录下-->
<template>
  <div class="execution">
    <basic-container class="base">
      <div class="div-page-index">
        <el-row>
          <div>
            <el-row>
              <el-button @click="copyPageUrl">复制地址</el-button>
            </el-row>
          </div>
        </el-row>
        <el-row  type="flex" justify="start" class="content" >
          <!-- 左侧组件 -->
          <el-col :span="5" class="left-class">
            <div style="text-align: center; padding: 5px; margin-top: 10px;">
              <span>组件库</span>
            </div>
            <el-collapse v-model="activeNames" style="overflow: scroll;height: 90%">
              <el-collapse-item title="基础组件" name="imageModule">
                <el-row :gutter="20" >
                  <el-col :span="12" v-for="item in addCompBtns.baseModule" :key="item.name" >
                    <div class="base_module_button"
                         @click="addComponents(item.eventPayload.name, item.eventPayload.payLoad)">
                      <!--                      <img :src="item.img" width="28px">-->
                      <p>{{ item.name }}</p>
                    </div>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <!--                  待开发 -->
              <el-collapse-item v-if="initPageData.pageType=='2'" title="拼团模块" name="spellGroupModule">
                <el-row :gutter="20">
                  <el-col :span="12" v-for="item in addCompBtns.spellGroupModule" :key="item.name">
                    <div class="base_module_button"
                         @click="addComponents(item.eventPayload.name, item.eventPayload.payLoad)">
                      <!--                      <img :src="item.img" width="28px">-->
                      <p>{{ item.name }}</p>
                    </div>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <el-collapse-item v-if="initPageData.pageType=='3'" title="作品模块" name="goodsModule">
                <el-row :gutter="20">
                  <el-col :span="12" v-for="item in addCompBtns.goodsModule" :key="item.name">
                    <div v-show="getGoodsModuleShow(item)" class="base_module_button"
                         @click="addComponents(item.eventPayload.name, item.eventPayload.payLoad)">
                      <p>{{ item.name }}</p>
                    </div>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <el-collapse-item v-if="initPageData.pageType=='4'" title="表单模块" name="fromModule">
                <el-row :gutter="20">
                  <el-col :span="12" v-for="item in addCompBtns.fromModule" :key="item.name">
                    <div v-show="getGoodsModuleShow(item)" class="base_module_button"
                         @click="addComponents(item.eventPayload.name, item.eventPayload.payLoad)">
                      <p>{{ item.name }}</p>
                    </div>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <el-collapse-item v-if="initPageData.pageType=='5'" title="客照分享" name="imgShareModule">
                <el-row :gutter="20">
                  <el-col :span="12" v-for="item in addCompBtns.imgShareModule" :key="item.name">
                    <div v-show="getGoodsModuleShow(item)" class="base_module_button"
                         @click="addComponents(item.eventPayload.name, item.eventPayload.payLoad)">
                      <p>{{ item.name }}</p>
                    </div>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <el-collapse-item v-if="initPageData.pageType=='6'" title="档期模块" name="appointModule">
                <el-row :gutter="20">
                  <el-col :span="12" v-for="item in addCompBtns.appointModule" :key="item.name">
                    <div v-show="getGoodsModuleShow(item)" class="base_module_button"
                         @click="addComponents(item.eventPayload.name, item.eventPayload.payLoad)">
                      <p>{{ item.name }}</p>
                    </div>
                  </el-col>
                </el-row>
              </el-collapse-item>
            </el-collapse>
            <el-button type="primary" style="width: 100%;" size="small" @click.stop="handleSave()">保存</el-button>
          </el-col>
          <!-- 中间页面 -->
          <el-col :span="5" class="showContent">
            <div class="pageContent" :style="{}">
              <div class="componentsList" :style="{overflowY: showModal?'hidden':'auto',
              backgroundColor:initPageData.pageBase.backgroundColor,
              backgroundImage: `url(`+initPageData.pageBase.background+`)`}"
                   ref="componentsDiv">
                <draggable v-model="componentsList" @start="datadragStart" @update="datadragUpdate" @end="datadragEnd"
                           :disabled="!enabled" :move="datadragMove" :options="{animation:500}">
                  <transition-group>
                    <!-- <div v-for="(item,index) in componentsList" :key="item.id" class="drag-item" @mouseenter="mouseEnter($event, index, item.componentName)"> -->
                    <div v-for="(item,index) in componentsList" :key="item.id" class="drag-item "
                         :class="index==clickComIndex?'focus-class':''" :ref="'drag-item-'+item.id"
                         @click="showSetting(index, item.id, item.componentName)">
                      <component :is="item.componentName" :setData="item.data" :config="item.config" :cId="item.id"
                                 :thememobile="thememobile"></component>
                    </div>
                  </transition-group>
                </draggable>
<!--                <div class="cu-load bg-gray over"></div>-->
              </div>
            </div>
            <div v-if="showSetBtn && clickComIndex!=null" class="funBlock"
                 :style="{top: settingPosit.top-45+'px', left: settingPosit.left+10+'px'}">
              <i class="icon el-icon-arrow-up" @click="componentSort('up')"></i>
              <i class="icon el-icon-arrow-down" @click="componentSort('dowm')"></i>
              <i class="icon el-icon-delete" @click="deleteComponent"></i>
            </div>
          </el-col>
          <!-- 右侧组件设置 -->
          <el-col :span="14" >
            <div class="settingBlock" ref="settingBlock">
              <el-tabs type="border-card" v-model="activeTabName" >
                <el-tab-pane label="页面设置" name="0">
                  <page-setting :initPageData="initPageData"></page-setting>
                </el-tab-pane>
                <el-tab-pane v-if="initPageData.pageType =='2'" label="拼团设置" name="2">
                  <groupon-info-setting ref="grouponInfoSetting"  :pageId="this.id" :appId="this.initPageData.appId"></groupon-info-setting>
                </el-tab-pane>
                <el-tab-pane v-if="initPageData.pageType =='5'" label="分享规则设置" name="3">
                  <share-rules-setting ref="shareRulesSetting"  :pageId="this.id" :appId="this.initPageData.appId"></share-rules-setting>
                </el-tab-pane>
                <el-tab-pane v-if="initPageData.pageType =='6'" label="选档规则" name="2">
                  <appoint-rules-setting ref="appointRulesSetting"  :pageId="this.id" :appId="this.initPageData.appId"></appoint-rules-setting>
                </el-tab-pane>
                <el-tab-pane v-if="showSetBlock" label="组件设置" name="1">
                  <component :is="currentComType+'Setting'" :appId="initPageData.appId" :showData="showData" :config="comConfig"
                             @cancel="settingCancel" @confirm="settingConfirm" @update="handleComponentUpdate" :thememobile="thememobile"
                             @setPosition="modifySetPosition(settingPosit.top)" :clientType="initPageData.clientType">
                  </component>
                </el-tab-pane>

              </el-tabs>
            </div>
          </el-col>
        </el-row>
      </div>
    </basic-container>
  </div>
</template>

<script>

// 接口地址
import {getPage, getObj, addObj, putObj, delObj} from '@/api/viewgen/pagedevise'
import {getObj as getWxApp} from '@/api/wxmp/wxapp'
import {getByPageId} from '@/api/viewgen/spellGroup'
import {getObj2} from '@/api/mall/thememobile'
// 商城页面装修必须引入 ----- 开始 -----
// 商城装修为单独模块，可提供按需购买方式
// 全局搜【todo divpageWx】可查看所有关联到的文件
import Vue from 'vue'
import '@/views/viewgen/viewedit/component-library/plugins/index';
// 图片轮播组件 swiper组件 npm i vue-awesome-swiper
import VueAwesomeSwiper from 'vue-awesome-swiper';
// import 'swiper/dist/css/swiper.css';
Vue.use(VueAwesomeSwiper, /* { default global options } */);
import '@/views/viewgen/viewedit/component-library/directive'//自定义事件引入
import '@/views/viewgen/viewedit/component-library/libs/utils';//引入property 方法
import '../assets/iconfont/iconfont.css';// 字体
import VueClipboard from 'vue-clipboard2'; // 剪贴板组件
Vue.use(VueClipboard)
// 商城页面装修必须引入 ----- 结束 -----

import {pageUrls} from '@/components/wx-page-select/pageUrls'
import {h5HostMobile} from '@/config/env'

import draggable from "vuedraggable";
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';
// 组件
import swiperComponent from "../components/swiper.vue";
import imageComponent from "../components/image.vue";

// 组件设置
import searchSetting from "../component-setting/searchSetting.vue";
import swiperSetting from "../component-setting/swiperSetting.vue";
import imageSetting from "../component-setting/imageSetting.vue";

import navTitleComponent from "../components/navTitle.vue";
import navTitleSetting from "../component-setting/navTitleSetting.vue";
import navButtonComponent from "../components/navButton.vue";
import navButtonSetting from "../component-setting/navButtonSetting.vue";
import noticeComponent from "../components/notice.vue";
import noticeSetting from "../component-setting/noticeSetting.vue";
import titleTextComponent from "../components/titleText.vue";
import titleTextSetting from "../component-setting/titleTextSetting.vue";

import categoryComponent from "../components/base/category.vue";
import categorySetting from "../component-setting/base/categorySetting.vue";

import suspendBtnComponent from "../components/base/suspendBtn.vue";
import suspendBtnSetting from "../component-setting/base/suspendBtnSetting.vue";

import tabBarComponent from "../components/base/tabBar.vue";
import tabBarSetting from "../component-setting/base/tabBarSetting.vue";

import goodsComponent from "../components/goods.vue";
import goodsSetting from "../component-setting/goodsSetting.vue";

import goodsPayComponent from "../components/goodsDetail/goodsPay.vue";
import goodsPaySetting from "../component-setting/goodsDetail/goodsPaySetting.vue";


import goodsRowComponent from "../components/goodsRow.vue";
import goodsRowSetting from "../component-setting/goodsRowSetting.vue";
import goodsCategoryComponent from "../components/goodsCategory.vue";
import goodsCategorySetting from "../component-setting/goodsCategorySetting.vue";

import mapComponent  from "../components/base/Map.vue";
import mapSetting from "../component-setting/base/MapSetting.vue";
import areaImageComponent  from "../components/base/areaImage.vue";
import areaImageSetting from "../component-setting/base/areaImageSetting.vue";


import videoComponent from "../components/video.vue";
import videoSetting from "../component-setting/videoSetting.vue";
// 引入上拉组件
import pullUpComponent from "../components/base/pullUp.vue";
import pullUpSetting from "../component-setting/base/pullUpSetting.vue";


// 营销组件
import grouponInfoSetting from "@/components/viewgen/groupon-setting/main";
import couponSetting from "../component-setting/couponSetting.vue";
import bargainSetting from "../component-setting/bargainSetting.vue";
import grouponComponent from "../components/groupon.vue";
import grouponSetting from "../component-setting/grouponSetting.vue";
import seckillComponent from "../components/seckill.vue";
import seckillSetting from "../component-setting/seckillSetting.vue";
//作品组件
import goodsCoverComponent from "../components/goodsCover.vue";
import goodsContentComponent from "../components/goodsContent.vue";
import goodsCoverSetting from "../component-setting/goodsCoverSetting.vue";
import goodsContentSetting from "../component-setting/goodsContentSetting.vue";
import auctionTimeComponent from "../components/goodsDetail/auctionTime.vue";
import auctionTimeSetting from "../component-setting/goodsDetail/auctionTimeSetting.vue";
import AppointRulesSetting from "@/components/viewgen/appoint-rules-setting/main";

// 引入弹窗组件
import popupComponent from "../components/base/popup.vue";
import popupSetting from "../component-setting/base/popupSetting.vue";

export default {
  components: {
    AppointRulesSetting,
    draggable,
    imageComponent,
    //双列图片
    doubleRowImageComponent: () => {
      return import("../components/doubleRowImage.vue")
    },
    doubleRowImageSetting: () => {
      return import("../component-setting/doubleRowImageSetting.vue")
    },
    //富文本
    richTextComponent: () => {
      return import("../components/richText.vue")
    },
    richTextSetting: () => {
      return import("../component-setting/richTextSetting.vue")
    },
    //分割线
    cuttingLineComponent: () => {
      return import("../components/base/cuttingLine.vue")
    },
    cuttingLineSetting: () => {
      return import("../component-setting/base/cuttingLineSetting.vue")
    },
    //音乐组件
    musicComComponent: () => {
      return import("../components/base/musicCom.vue")
    },
    musicComSetting: () => {
      return import("../component-setting/base/musicComSetting.vue")
    },

    swiperComponent,
    navTitleComponent,
    navButtonComponent,
    noticeComponent,
    titleTextComponent,
    categoryComponent,
    categorySetting,
    suspendBtnComponent,
    tabBarComponent,
    tabBarSetting,
    suspendBtnSetting,
    // 注册弹窗组件
    popupComponent,
    popupSetting,
    goodsComponent,
    goodsPayComponent,
    goodsPaySetting,
    goodsRowComponent,
    goodsCategoryComponent,
    searchSetting,
    imageSetting,
    swiperSetting,
    navTitleSetting,
    navButtonSetting,
    noticeSetting,
    titleTextSetting,
    goodsSetting,
    goodsRowSetting,
    goodsCategorySetting,
    grouponComponent,
    seckillComponent,
    couponSetting,
    bargainSetting,
    grouponSetting,
    seckillSetting,
    goodsCoverComponent,
    goodsCoverSetting,
    goodsContentComponent,
    goodsContentSetting,
    auctionTimeComponent,
    auctionTimeSetting,
    grouponInfoSetting,
    mapComponent,
    mapSetting,
    areaImageComponent,
    areaImageSetting,
    videoComponent, videoSetting,
    //表单模块
    datePicker: () => {
      return import("../components/from/datePicker.vue")
    },
    dropDownSelect: () => {
      return import("../components/from/dropDownSelect.vue")
    },
    rateControl: () => {
      return import("../components/from/rateControl.vue")
    },
    imgUpload: () => {
      return import("../components/from/imgUpload.vue")
    },
    phoneCheck: () => {
      return import("../components/from/phoneCheck.vue")
    },
    radioSelect: () => {
      return import("../components/from/radioSelect.vue")
    },
    singleLineInput: () => {
      return import("../components/from/singleLineInput.vue")
    },
    submitButton: () => {
      return import("../components/from/submitButton.vue")
    },
    submitList: () => {
      return import("../components/from/submitList.vue")
    },
    datePickerSetting: () => {
      return import("../component-setting/from/datePickerSetting.vue")
    },
    dropDownSelectSetting: () => {
      return import("../component-setting/from/dropDownSelectSetting.vue")
    },
    rateControlSetting: () => {
      return import("../component-setting/from/rateControlSetting.vue")
    },
    imgUploadSetting: () => {
      return import("../component-setting/from/imgUploadSetting.vue")
    },
    phoneCheckSetting: () => {
      return import("../component-setting/from/phoneCheckSetting.vue")
    },
    radioSelectSetting: () => {
      return import("../component-setting/from/radioSelectSetting.vue")
    },
    singleLineInputSetting: () => {
      return import("../component-setting/from/singleLineInputSetting.vue")
    },
    submitButtonSetting: () => {
      return import("../component-setting/from/submitButtonSetting.vue")
    },
    submitListSetting: () => {
      return import("../component-setting/from/submitListSetting.vue")
    },
    //拼团模块
    captainComponent: () => {
      return import("../components/spellGroup/captain.vue")
    },
    captainSetting: () => {
      return import("../component-setting/spellGroup/captainSetting.vue")
    },
    memberComponent: () => {
      return import("../components/spellGroup/member.vue")
    },
    memberSetting: () => {
      return import("../component-setting/spellGroup/memberSetting.vue")
    },
    functionButtonComponent: () => {
      return import("../components/base/functionButton.vue")
    },
    functionButtonSetting: () => {
      return import("../component-setting/base/functionButtonSetting.vue")
    },
    groupFunctionComponent: () => {
      return import("../components/spellGroup/groupFunction.vue")
    },
    groupFunctionSetting: () => {
      return import("../component-setting/spellGroup/groupFunctionSetting.vue")
    },
    rollingListComponent: () => {
      return import("../components/spellGroup/rollingList.vue")
    },
    rollingListSetting: () => {
      return import("../component-setting/spellGroup/rollingListSetting.vue")
    },
    rollingBarrageComponent: () => {
      return import("../components/spellGroup/rollingBarrage.vue")
    },
    rollingBarrageSetting: () => {
      return import("../component-setting/spellGroup/rollingBarrageSetting.vue")
    },
    //客片分享
    imgShowComponent: () => {
      return import("../components/imgShare/imgShow.vue")
    },
    imgShowSetting: () => {
      return import("../component-setting/imgShare/imgShowSetting.vue")
    },
    shareMasterComponent: () => {
      return import("../components/imgShare/shareMaster.vue")
    },
    shareMasterSetting: () => {
      return import("../component-setting/imgShare/shareMasterSetting.vue")
    },
    progressComponent: () => {
      return import("../components/imgShare/progress.vue")
    },
    progressSetting: () => {
      return import("../component-setting/imgShare/progressSetting.vue")
    },
    countDownComponent: () => {
      return import("../components/imgShare/countDown.vue")
    },
    countDownSetting: () => {
      return import("../component-setting/imgShare/countDownSetting.vue")
    },
    thumbsUpButtonComponent: () => {
      return import("../components/imgShare/thumbsUpButton.vue")
    },
    thumbsUpButtonSetting: () => {
      return import("../component-setting/imgShare/thumbsUpButtonSetting.vue")
    },
    popularityComponent: () => {
      return import("../components/imgShare/popularity.vue")
    },
    popularitySetting: () => {
      return import("../component-setting/imgShare/popularitySetting.vue")
    },
    voteListComponent: () => {
      return import("../components/imgShare/voteList.vue")
    },
    voteListSetting: () => {
      return import("../component-setting/imgShare/voteListSetting.vue")
    },
    shareRulesSetting: () => {
      return import("@/components/viewgen/share-rules-setting/main")
    },
    pageSetting: () => {
      return import("@/components/viewgen/page-setting/main")
    },
    pullUpComponent,
    pullUpSetting,
  },
  props: {
    // initPageData:{
    //   type: Object,
    //   default: ()=> {}
    // },
  },
  created() {
    if (this.$route.params.id) {
      this.id = this.$route.params.id;
    } else {
      this.id = window.localStorage.getItem('viewEditId');
    }
    getObj(this.id).then(res => {
      let tableData = res.data.data.records;
      let clientTypeData = this.clientTypeData
      if (clientTypeData && clientTypeData.length > 0) {
        for (let i = 0; i < clientTypeData.length; i++) {
          clientTypeData[i].disabled = false
          for (let j = 0; j < tableData.length; j++) {
            if (clientTypeData[i].value == tableData[j].clientType) {
              clientTypeData[i].disabled = true
            }
          }
        }
      }
    }).catch(() => {});
  },
  mounted() {
    let that = this;
    // 滑动监听
    // that.$refs.componentsDiv.addEventListener("scroll", that.componentDivScroll);
    //为了防止火狐浏览器拖拽的时候以新标签打开，此代码真实有效
    document.body.ondrop = function (event) {
      event.preventDefault();
      event.stopPropagation();
    };
    this.initPage();
  },
  destroyed() {
    window.localStorage.removeItem('viewEditId');
  },
  data() {
    return {
      activeTabName: "0",
      thememobile: {
        backgroundColor: ''
      },
      clientTypeData: [{
        label: 'H5',
        value: 'H5',
        disabled: true
      }, {
        label: 'APP',
        value: 'APP',
        disabled: true
      }, {
        label: '小程序',
        value: 'MA',
        disabled: true
      }],
      id: null,
      wxApp:{},
      initPageData: {
        clientType: '',
        pageComponent: {
          componentsList: []
        },
        pageBase: {
          permitUserTag:[],//允许用户
          forbidUserTag:[],//禁止用户
          permitIsSystemUrl:'',//允许用户标记
          forbidIsSystemUrl:'',//禁止跳转标记
          permitPageUrl:'',//允许跳转地址
          forbidPageUrl:'',//禁止跳转跳转地址
          backgroundColor: '#ffffff',
          fontColor: '#409EFF',
          resource: "1",
          title: "",
          name: "",
          shareTitle: "",
          describe: "",
          shareImgUrl: "",
        }
      },
      groupon: {},//拼团活动
      spellGroupTime: [],//拼团时间
      imgShareRules: {},//客片分享
      imgShareRulesTime: [],//客片分享
      pickerOptions: {//拼团时间快捷选项
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      enabled: true,
      activeNames: ['imageModule', 'spellGroupModule', 'goodsModule', 'fromModule','imgShareModule','appointModule'],
      showSetBlock: false,
      settingPosit: {
        top: 0,
        left: 0,
        timeStamp: 0
      },
      addCompBtns: {
        baseModule: [

          {name: '视频', eventPayload: {name: 'videoComponent', payLoad: {}}},
          {
            name: '上拉组件',
            eventPayload: {name: 'pullUpComponent', payLoad: {type: 1}}
          },
          {
            name: '头部导航栏',
            eventPayload: {name: 'navTitleComponent', payLoad: {type: 1}}
          },
          {
            name: '单列图片',
            eventPayload: {name: 'imageComponent', payLoad: {type: 1, singleImg: true}}
          },
          {
            name: '双列图片',
            eventPayload: {name: 'doubleRowImageComponent', payLoad: {type: 1, singleImg: true}}
          },
          {
            name: '富文本',
            eventPayload: {name: 'richTextComponent', payLoad: {type: 1}}
          },
          {
            name: '分割线',
            eventPayload: {name: 'cuttingLineComponent', payLoad: {type: 1}}
          },
          {
            name: '弹窗广告',
            eventPayload: {name: 'popupComponent', payLoad: {type: 1}}
          },
          {
            name: '轮播图片',
            eventPayload: {name: 'swiperComponent', payLoad: {type: 1}}
          },
          {
            name: '导航按钮',
            eventPayload: {name: 'navButtonComponent', payLoad: {type: 1}}
          },
          {
            name: '通知公告',
            eventPayload: {name: 'noticeComponent', payLoad: {type: 1}}
          },
          {
            name: '标题文字',
            eventPayload: {name: 'titleTextComponent', payLoad: {type: 1}}
          },
          {
            name: '作品分类',
            eventPayload: {name: 'categoryComponent', payLoad: {type: 1}}
          },
          {
            name: '悬浮按钮',
            eventPayload: {name: 'suspendBtnComponent', payLoad: {type: 1}}
          },
          {
            name: '底部导航',
            eventPayload: {name: 'tabBarComponent', payLoad: {type: 1}}
          },
          {
            name: '多功能菜单',
            eventPayload: {name: 'functionButtonComponent', payLoad: {type: 1}}
          },
          {
            name: '地图控件',
            eventPayload: {name: 'mapComponent', payLoad: {type: 1}}
          },          {
            name: '热点图片',
            eventPayload: {name: 'areaImageComponent', payLoad: {type: 1}}
          },
        ],
        //拼团模块
        spellGroupModule: [
          {
            name: '团主控件',
            eventPayload: {name: 'captainComponent', payLoad: {type: 2}}
          },
          {
            name: '团员组件',
            eventPayload: {name: 'memberComponent', payLoad: {type: 2}}
          },
          {
            name: '滚动列表',
            eventPayload: {name: 'rollingListComponent', payLoad: {type: 2}}
          },
          {
            name: '滚动弹幕',
            eventPayload: {name: 'rollingBarrageComponent', payLoad: {type: 2}}
          },
          {
            name: '拼团多功能',
            eventPayload: {name: 'groupFunctionComponent', payLoad: {type: 2}}
          },
        ],
        goodsModule: [
          {
            name: '商品推荐',
            eventPayload: {name: 'goodsComponent', payLoad: {type: 3}},
            isShow: true,
          },
          {
            name: '商品购买',
            eventPayload: {name: 'goodsPayComponent', payLoad: {type: 3}},
            isShow: true,
          },
          {
            name: '作品甄选',
            eventPayload: {name: 'goodsRowComponent', payLoad: {type: 3}},
            isShow: true,
          },
          {
            name: '作品封面',
            eventPayload: {name: 'goodsCoverComponent', payLoad: {type: 3}},
            isShow: true,
          },
          {
            name: '作品内容',
            eventPayload: {name: 'goodsContentComponent', payLoad: {type: 3}},
            isShow: true,
          },
        ],
        //表单模块
        fromModule: [
          {
            name: '单行文字框',
            eventPayload: {name: 'singleLineInput', payLoad: {type: 4, ableSubmit: true}},
            isShow: true,
          },
          {
            name: '下拉选择框',
            eventPayload: {name: 'dropDownSelect', payLoad: {type: 4, ableSubmit: true}},
            isShow: true,
          },
          {
            name: '按钮单选框',
            eventPayload: {name: 'radioSelect', payLoad: {type: 4, ableSubmit: true}},
            isShow: true,
          },
          {
            name: '日期选择',
            eventPayload: {name: 'datePicker', payLoad: {type: 4, ableSubmit: true}},
            isShow: true,
          },
          {
            name: '手机验证',
            eventPayload: {name: 'phoneCheck', payLoad: {type: 4, ableSubmit: true}},
            isShow: true,
          },
          {
            name: '提交按钮',
            eventPayload: {name: 'submitButton', payLoad: {type: 4, ableSubmit: false}},
            isShow: true,
          },
          // {
          //   name: '提交列表',
          //   eventPayload: {name: 'submitList', payLoad: {type: 4, ableSubmit: true}},
          //   isShow: true,
          // },
          {
            name: '图片上传',
            eventPayload: {name: 'imgUpload', payLoad: {type: 4, ableSubmit: true}},
            isShow: true,
          },
          {
            name: '评分控件',
            eventPayload: {name: 'rateControl', payLoad: {type: 4, ableSubmit: true}},
            isShow: true,
          },
          // {
          //   name: '多功能菜单',
          //   eventPayload: {name: 'functionButtonComponent', payLoad: {type: 4}}
          // },
        ],
        //客照分享
        imgShareModule: [
          {
            // <u-input v-model="value" :type="type" :border="border" />
            name: '客片展示',
            eventPayload: {name: 'imgShowComponent', payLoad: {type: 5, ableSubmit: false}},
            isShow: true,
          },
          {
            name: '页面分享',
            eventPayload: {name: 'shareMasterComponent', payLoad: {type: 5, ableSubmit: true}},
            isShow: true,
          },
          {
            name: '活动进度',
            eventPayload: {name: 'progressComponent', payLoad: {type: 5, ableSubmit: true}},
            isShow: true,
          },
          {
            name: '投票倒计时',
            eventPayload: {name: 'countDownComponent', payLoad: {type: 5, ableSubmit: true}},
            isShow: true,
          },
          {
            name: '投票按钮',
            eventPayload: {name: 'thumbsUpButtonComponent', payLoad: {type: 5, ableSubmit: true}},
            isShow: true,
          },
          {
            name: '人气热度',
            eventPayload: {name: 'popularityComponent', payLoad: {type: 5, ableSubmit: false}},
            isShow: true,
          },
          {
            name: '投票列表',
            eventPayload: {name: 'voteListComponent', payLoad: {type: 5, ableSubmit: false}}
          },
        ],
        //档期选择
        appointModule: [
          {
            name: '作品选档',
            eventPayload: {name: 'auctionTimeComponent', payLoad: {type: 6}},
            isShow: true,
          },
        ],
      },
      // clickComIndex : null,        //当前点击的组件下标
      currentComType: "",             //当前点击的组件类型；
      showData: {},
      comConfig: {},
      showSetBtn: false,
      initPageHeight: 680 + 65 + 90,      //初始化显示高度为680(加上top栏高度  页面的顶部图片高度)
      setBlockTop: 0,              //设置框的相对top值
      setRowTop: 0,              //设置框的箭头的相对top值
      showSetCompet: "",             //显示设置的组件块
      pageDiyId: "",
      pageName: "",             //页面名称
      pageSort: [],             //页面分类

    };
  },
  computed: {
    ...mapState({
      pageSetData: state => state.divpageWx.pageSetData,
      clickComIndex: state => state.divpageWx.clickComIndex,
      showModal: state => state.divpageWx.showModal,
      pageId: state => state.divpageWx.pageId,//页面ID
      pageType: state => state.divpageWx.pageType,//页面ID
    }),
    componentsList: {
      get() {
        return this.$store.state.divpageWx.componentsList
      },
      set(newVal) {
        this.$store.commit('updateData', {componentsList: newVal})
      }
    },
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    initPage() {
      let that = this;
      if (this.$route.params.id) {
        that.updateData({pageId: this.$route.params.id})
        window.localStorage.setItem('viewEditId', this.pageId);

      } else {
        let viewEditId = window.localStorage.getItem('viewEditId');
        if (viewEditId) {
          that.updateData({pageId: viewEditId})
        }
      }
      this.id = this.pageId;
      // that.pageDiyId= that.$route.query.diyId || '';//预留
      // that.pageName = that.$route.query.pageName || '';
      // that.pageSort = that.$route.query.sort ? that.$route.query.sort.split(',') : [];
      this.getPageData();//页面数据
      // 监听页面刷新,保存当前ID
      window.addEventListener('beforeunload', e => {
        window.localStorage.setItem('viewEditId', this.pageId);
      });
    },
    getPageData() {
      if (this.id && this.id != 'null') {
        // console.log("加载id", this.id);
        getObj(this.id).then(res => {
          console.log("页面结果", res.data);
          if (res.data) {
            this.initPageData = res.data.data
            //拿取公众号
            getWxApp(this.initPageData.appId).then(response => {
              this.wxApp = response.data.data
            }).catch(() => {
            })
            //客片分享类型的页面进行活动拉取
            if(this.initPageData.pageType =="5"){
              // this.$refs.shareRulesSetting.get();
            }
            // this.getGroupInfo();//加载页面信息
            this.getComponentsDataById();
            let clientTypeData = this.clientTypeData
            if (!this.initPageData.pageBase) {
              this.initPageData.pageBase = {
                backgroundColor: '#ffffff',
                fontColor: '#409EFF',
                resource: "1",
                title: "",
                name: "",
              }
            }
            if (clientTypeData && clientTypeData.length > 0) {
              for (let i = 0; i < clientTypeData.length; i++) {
                clientTypeData[i].disabled = false
                for (let j = 0; j < this.tableData.length; j++) {
                  if (clientTypeData[i].value == this.initPageData.clientType) {
                    clientTypeData[i].disabled = true
                  }
                }
              }
            }
          }
        }).catch((e) => {
        })
      } else {
        this.initPageData = {
          clientType: '',
          pageComponent: {
            componentsList: []
          }
        }
        this.updateData({
          showModal: false,
          clickComIndex: null,
          componentsList: this.initPageData.pageComponent.componentsList
        })
      }
    },
    getGroupInfo(){
      console.log("加载拼团信息",this.id)
      if("2" != this.initPageData.pageType){
        return
      };
      getByPageId(this.id).then(res => {
        this.groupon = res.data.data;
        console.log("拼团信息res", res);
      }).catch(err => {
        console.log("拼团信息err",err)
      })
    },
    handleSave() {
      this.initPageData.pageComponent.componentsList = this.componentsList;
      console.log("保存餐素", this.initPageData)
      if (!this.initPageData.clientType) {
        this.$message({
          showClose: true,
          message: '请先选择一个应用端！',
          type: 'info'
        })
        return;
      }
      if (this.initPageData.id) {
        putObj(this.initPageData).then(response => {
          this.$message({
            showClose: true,
            message: '保存成功',
            type: 'success'
          })
        }).catch(() => {
        })
      } else {
        addObj(this.initPageData).then(res => {
          this.$message({
            showClose: true,
            message: '添加成功',
            type: 'success'
          })
          // this.$router.$avueRouter.closeTag();
          // this.$router.back();
          if (res.data.data.id) {
            console.log("返回结果", res)
            this.initPageData.id = res.data.data.id
            this.updateData({pageId: res.data.data.id})
            window.localStorage.setItem('viewEditId', this.pageId);
            this.initPage();
          }
        }).catch(() => {
        })
      }
      //对拼团活动的修改
      if(this.initPageData.pageType=="2") {
        console.log("需要调活动调保存")
        this.$refs.grouponInfoSetting.update()
      }
      //对客片分享的修改
      if(this.initPageData.pageType=="5") {
        console.log("需要客片分享调保存")
        this.$refs.shareRulesSetting.update();
      }
      //对档期选择的修改
      if(this.initPageData.pageType=="6") {
        console.log("需要客片分享调保存")
        this.$refs.appointRulesSetting.update();
      }
    },
    // 获取回显数据
    getComponentsDataById(id) {
      let that = this;
      that.componentsList = [];
      if (this.initPageData.pageComponent && this.initPageData.pageComponent.componentsList && this.initPageData.pageComponent.componentsList.length > 0) {
        that.componentsList = this.initPageData.pageComponent.componentsList
        // 数据筛选 避免无效数据
        that.componentsList = that.componentsList.filter(item => item.componentName);
        that.updateData({componentsList: that.componentsList});
      }
    },
    // 添加组件
    addComponents(cName, config = {}) {
      let that = this;
      //计算动态的id值
      let componentsLen = this.componentsList ? this.componentsList.length : 0;
      let dynamicID = componentsLen ? that.componentsList.reduce((item1, item2) => (item1.id > item2.id ? item1 : item2)).id + 1 : 1;

      that.componentsList.splice(this.clickComIndex===null?componentsLen:this.clickComIndex+1,0,{
        componentName: cName,
        id: dynamicID,
        data: {},
        config: config
      })
      that.updateData({componentsList: that.componentsList});
      // 显示最新添加的模块
      that.$nextTick(() => {
        let newComponentEle = that.$refs['drag-item-' + dynamicID][0];
        // newComponentEle.scrollIntoView({
        //     // behavior: 'smooth',
        //     block   : 'end'
        // })
        setTimeout(() => {
          var event = new MouseEvent("click");
          newComponentEle.dispatchEvent(event);
        }, 100)
      })
    },
    // 展示设置框
    showSetting(index, cid, cName) {

      let that = this;
      if (index != that.clickComIndex || !that.showSetBlock) {
        that.showSetBlock = false;
        that.$nextTick(() => {
          that.showSetBlock = true
        })
      }
      that.showSetCompet = that.$refs['drag-item-' + cid][0];
      let elPositionInfo = that.showSetCompet.getBoundingClientRect();
      that.updateData({clickComIndex: index})

      that.$set(that.settingPosit, 'top', elPositionInfo.top <= 160 ? 160 : elPositionInfo.top - 80)
      that.$set(that.settingPosit, 'left', elPositionInfo.width + 20);
      that.$set(that.settingPosit, 'timeStamp', new Date().getTime());

      this.currentComType = cName.split('Component')[0];
      this.showData = that.componentsList[that.clickComIndex]['data'];
      this.comConfig = that.componentsList[this.clickComIndex]['config'];
      this.showSetBtn = true;
      this.activeTabName = "1";
    },
    // 取消设置
    settingCancel() {
      let that = this;
      that.showData = {};
      that.updateData({clickComIndex: null})
      that.showSetBlock = false;
    },
    // 确认设置
    settingConfirm(data) {
      let that = this;
      that.$set(that.componentsList[that.clickComIndex], 'data', data)
      that.updateData({
        clickComIndex: null,
        componentsList: that.componentsList
      })
      that.showSetBlock = false;
    },
    // 处理组件实时更新
    handleComponentUpdate(data) {
      let that = this;
      // 只更新组件数据，不关闭设置面板
      that.$set(that.componentsList[that.clickComIndex], 'data', JSON.parse(JSON.stringify(data)))
      that.updateData({
        componentsList: that.componentsList
      })
    },
    // 删除组件
    deleteComponent() {
      let that = this;
      that.componentsList.splice(that.clickComIndex, 1)
      that.updateData({
        showModal: false,
        clickComIndex: null,
        componentsList: that.componentsList
      })
      that.showSetBlock = false;
      that.showSetBtn = false;
      that.activeTabName = "0";
    },
    //排序
    componentSort(type) {
      let that = this;
      if (type == 'up') {
        if (that.clickComIndex == 0) return false;
        that.dataInterchange(that.componentsList, that.clickComIndex - 1, that.clickComIndex);
        that.updateData({clickComIndex: that.clickComIndex - 1})
      } else {
        if (that.clickComIndex == that.componentsList.length - 1) return false;
        that.dataInterchange(that.componentsList, that.clickComIndex + 1, that.clickComIndex);
        that.updateData({clickComIndex: that.clickComIndex + 1})
      }
      that.showSetBlock = false;
      // 设置跟随；
      that.$nextTick(() => {
        let activeComponentData = that.componentsList[that.clickComIndex];
        let activeComponenttEle = that.$refs['drag-item-' + activeComponentData.id][0];
        // activeComponenttEle.scrollIntoView({block: 'center'});
        that.showSetting(that.clickComIndex, activeComponentData.id, activeComponentData.componentName);
      })
    },
    // 提交页面
    submitData() {
      let that = this;
      return new Promise((resolve, reject) => {
        if (that.componentsList && that.componentsList.length) {
          resolve(JSON.stringify(that.componentsList))
        } else {
          resolve()
          // reject({code: 100, msg: "未添加组件"})
        }
      })
    },
    // 拖动的事件等等=======================================================>
    datadragStart(e) {
      let that = this;
      that.updateData({clickComIndex: null})
      that.showSetBlock = false;
    },
    datadragUpdate(e) {
      e.preventDefault();
    },
    datadragEnd(e) {
      // console.log(e, "拖动结束");
    },
    datadragMove(e, originalEve) {
      return (e.draggedContext.element.text !== 'Gold（不可拖动元素）');
    },
    //====================================================================>
    // div中内容滚动检测 （右侧设置的动态显示）
    componentDivScroll(e) {
      // let that = this;
      // if(!that.showSetCompet) return false;
      //
      // let elPositionInfo = that.showSetCompet.getBoundingClientRect();
      // that.$set(that.settingPosit, 'top', elPositionInfo.top<=160 ? 160 : elPositionInfo.top )
      // let contentHeight = e.target.scrollHeight;
      // let viewHeight    = e.target.clientHeight;
      // let scrollTop     = e.target.scrollTop;
      //
      // let eleHeight     = elPositionInfo.height;
      // let eleOffsetTop  = that.showSetCompet.offsetTop;
      //
      // let AMEND_TOP = 30 + 65;    // 顶部消失的修正值
      // let AMEND_BOTTOM = -20;      // 底部消失的修正值
      // let topCritical    = scrollTop > (eleHeight + eleOffsetTop - AMEND_TOP);          // 顶部达到临界
      // let bottomCritical = eleOffsetTop > (scrollTop + viewHeight - AMEND_BOTTOM);      // 底部达到临界
      // (topCritical || bottomCritical) && that.settingCancel();
    },
    modifySetPosition(settingTop) {
      let that = this;
      that.$nextTick(() => {
        setTimeout(() => {
          let settingEle = that.$refs.settingBlock;
          if (!settingEle) return false;

          let setHeight = settingEle.getBoundingClientRect().height;
          if (settingTop + setHeight > that.initPageHeight) {
            that.setBlockTop = that.initPageHeight - setHeight;
            that.setRowTop = setHeight - (that.initPageHeight - settingTop);
          } else {
            that.setBlockTop = settingTop < 0 ? 0 : settingTop;
            that.setRowTop = 0;
          }
        }, 20)
      })
    },
    copyPageUrl() {
      let url = "";
      let list =  pageUrls;//app 页面地址
      for (let i in list ) {
        if(list[i].type == this.initPageData.pageType){
          // todo: 取消掉这个替换避免加上appid
          url = h5HostMobile.replace('https://',`https://${this.initPageData.appId}.`) + list[i].url + "?page_id=" + this.id + "&app_id=" + this.initPageData.appId+"&tenant_id="+this.initPageData.tenantId;
          // url = h5HostMobile +  list[i].url + "?page_id=" + this.id + "&app_id=" + this.initPageData.appId+"&tenant_id="+this.initPageData.tenantId;
        }
      }
      if('1' == this.wxApp.isComponent){
        url= url + '&component_appid='+this.wxApp.componentAppId;
      }
      console.log("urls",url);

      this.$copyText(url).then( e => {
        this.$message.success("复制成功")
        console.log(e)
      }, function (e) {
        this.$message.error("复制失败")
        console.log(e)
      })
    },
    getGoodsModuleShow(obj) {
      //内容展示只有在作品详情页出现
      if (this.initPageData.pageType != "3" && obj.name == "内容展示") {
        return false
      }
      return true;
    },
  },
  // 离开
  // beforeRouteLeave(to, from, next){
  //     let that = this;
  //     that.$confirm('离开后数据将清空', '提示', {
  //         confirmButtonText: '确定',
  //         cancelButtonText : '取消',
  //         type : 'warning'
  //     }).then(()=>{
  //         that.$parent.shwoBackup();
  //         // 清除遮罩
  //         that.$store.commit('updateData', {showModal: false});
  //         next()
  //     })
  // },
  watch: {
    showModal(newVal) {
      this.$nextTick(() => {
        this.showSetBtn = this.showSetBlock = newVal
      })
    },
    settingPosit: {
      handler(newVal) {
        this.modifySetPosition(newVal.top)
      },
      deep: true
    },
  }
};
</script>

<style lang="less" scoped>
@import '../pagedevise.less';
@import 'divpage.less';
@import '../colorui/main.css';
.base{
  height: 100vh;
}

.base_module_button {
  text-align: center;
  background: #f7f7f7;
  margin: 10px 0px;
  border: 1px #b3d4fc dashed;
  padding: 5px 0;
}
</style>
