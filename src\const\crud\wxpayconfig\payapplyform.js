export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  viewBtn: true,
  searchMenuSpan: 6,
  column: [
    {
      label: '超级管理员',
      prop: 'contactInfo',
      slot: true,
      display:false,
    },
    {
      label: '申请单状态',
      prop: 'applymentState',
      type: 'select',
      sortable: true,
      display:false,
      dicData: [{
        label: '未提交',
        value: ''
      },{
        label: '编辑中',
        value: 'APPLYMENT_STATE_EDITTING'
      },{
        label: '审核中',
        value: 'APPLYMENT_STATE_AUDITING'
      },{
        label: '已驳回',
        value: 'APPLYMENT_STATE_REJECTED'
      },{
        label: '待账户验证',
        value: 'APPLYMENT_STATE_TO_BE_CONFIRMED'
      },{
        label: '待签约',
        value: 'APPLYMENT_STATE_TO_BE_SIGNED'
      },{
        label: '开通权限中',
        value: 'APPLYMENT_STATE_SIGNING'
      },{
        label: '已完成',
        value: 'APPLYMENT_STATE_FINISHED'
      },{
        label: '已作废',
        value: 'APPLYMENT_STATE_CANCELED'
      }]
    },
    {
      label: '申请提示',
      prop: 'applymentStateMsg',
      display:false,
    },
    {
      label: '申请单号',
      prop: 'applymentId',
      display: false
    },{
      label: '商户号',
      prop: 'subMchid',
      display: false
    },
    {
      label: '驳回原因详情',
      prop: 'auditDetail',
      slot: true,
      display:false,
    },
    {
      label: '超级管理员签约',
      prop: 'signUrl',
      slot: true,
      display:false,
    },
  ],
  group: [
    {
      label: '超级管理员信息',
      prop: 'contactInfo',
      icon: 'el-icon-user',
      column: [
        {
          prop: 'contactInfo',
          formslot: true,
          span: 24
        }
      ]
    },{
      label: '主体资料',
      prop: 'subjectInfo',
      icon: 'el-icon-folder-remove\n',
      column: [
        {
          prop: 'subjectInfo',
          formslot: true,
          span: 24
        }
      ]
    },{
      label: '经营资料',
      prop: 'businessInfo',
      icon: 'el-icon-tickets',
      column: [
        {
          prop: 'businessInfo',
          formslot: true,
          span: 24
        }
      ]
    },{
      label: '结算银行',
      prop: 'bankAccountInfo',
      icon: 'el-icon-bank-card',
      column: [
        {
          prop: 'bankAccountInfo',
          formslot: true,
          span: 24
        }
      ]
    },{
      label: '结算规则',
      prop: 'settlementInfo',
      icon: 'el-icon-money',
      column: [
        {
          prop: 'settlementInfo',
          formslot: true,
          span: 24
        }
      ]
    }]
}
