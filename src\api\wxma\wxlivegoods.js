import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/weixin/wxmalivegoods/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/weixin/wxmalivegoods/' +obj.appId,
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/weixin/wxmalivegoods/' + id,
        method: 'get'
    })
}

export function delObj(appId,id) {
    return request({
        url: '/weixin/wxmalivegoods/' +appId + '/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/weixin/wxmalivegoods/' +obj.appId,
        method: 'put',
        data: obj
    })
}

export function audit(obj) {
  return request({
    url: '/weixin/wxmalivegoods/' +obj.appId+'/audit',
    method: 'post',
    data: obj
  })
}
