<template>
  <div class="submitButtonComponent"
       :style="{'justify-content':`${setData.location}`,background: `${setData.backgroundColor}`, marginBottom: `${setData.pageSpacing}px`}">
    <el-button class="submitButtonComponent_button"
               :size="setData.size!='100%'?setData.size:''"
               :style="{width:`${setData.size=='100%'?'100%':''}`,
                color:`${setData.fontColor}`,
                background:`${setData.buttonColor}`,}">
      {{ setData.content }}
    </el-button>
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';

export default {
  data() {
    return {};
  },
  components: {},
  props: {
    theme: {type: Object | Array},
    setData: {type: Object | Array},
    cId: {type: Number},
    noEditor: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {
  },
  mounted() {
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
  },
  watch: {
    setData(newVal, oldVal) {
    },
    componentsList(newVal, oldVal) {          //添加的时候出发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    }
  },
  beforeDestroy() {
    // this.$root.Bus.$off('addHotSpot')
  }
};
</script>
<style lang='less' scoped>
.submitButtonComponent {
  width: 100%;
  padding: 5px;
  background: #ffffff;
  background: #ffffff;
  display: flex;
}

.submitButtonComponent_button {
  display: inline-block;
}

</style>
