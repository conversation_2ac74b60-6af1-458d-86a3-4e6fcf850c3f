<!--
  - Copyright (C) 2018-2020
  - All rights reserved, Designed By www.gocreateone.com
  - 注意：
  - 本软件为www.gocreateone.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<!-- 店铺名 -->
<template>
    <div class="shopInfoComponent" :style="{paddingBottom: `${setData.pageSpacing}px`}">
      <div class="cu-card no-card  ">
        <div class="cu-item padding-sm">
          <div class="content shopinfo-detail   align-center flex solid">
            <img :src="shopInfo.imgUrl" >
            <div class="flex justify-between  align-center" style="width: 100%;">
              <div class="margin-left-sm">
                <div class="cuIcon-locationfill overflow-1">
                  <span class="text-df margin-left-xs">{{shopInfo.address}}</span>
                </div>
                <div class="flex collect overflow-1 margin-top-sm" >
                  <span class="cuIcon-mobilefill"></span>
                  <span class="phone text-df margin-left-xs">{{shopInfo.phone}}</span>
                </div>
              </div>
              <div class="text-right">
                <div class="flex text-right" >
                  <span class="margin-left-xs text-df">{{shopInfo.collectCount}} 人已收藏</span>
                  <div class="cu-btn sm margin-left-sm text-666" >
                    <span class="margin-right-xs" :class="'cuIcon-' + (shopInfo.collectId ? 'likefill text-red' : 'like text-red')"></span>{{shopInfo.collectId ? '已收藏' : '收藏'}}
                  </div>
                  <div class="cu-btn sm margin-left-sm text-666" >
                    <span  class="cuIcon-ticket text-green margin-right-xs"></span>领券
                  </div>
                  <div class="cu-btn sm margin-left-sm text-666"  >
                    <span class="cuIcon-share text-green "></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
</template>

<script>
import { mapState , mapGetters , mapMutations , mapActions } from 'vuex';
import placeholderImg from "../pages/page-components/placeholderImg";
import * as shopinfo from "@/api/mall/shopinfo";

export default {
    data() {
        return {
          defaultImage: require('../assets/images/icon/<EMAIL>'),
          shopInfo: {}
        };
    },
    components: { placeholderImg },
    props: {
        shopId: {
          type: String
        },
        theme : { type: Object | Array },
        setData : { type: Object | Array },
        cId     : { type: Number },
        noEditor: {
            type: Boolean,
            default: false,
        }
    },
    computed: {
        ...mapState({
            componentsList: state => state.divpageShop.componentsList,
        }),
    },
    created() {

    },
    mounted() {
      shopinfo.getObj(this.shopId).then(response => {
        this.shopInfo = response.data.data;
      })
    },
    methods: {
        ...mapMutations([
            'updateData'
        ]),
    },
    watch:{
        setData(newVal, oldVal){},
        componentsList(newVal, oldVal){          //添加的时候出发（解决没有移动的时候不emit数据）
            let that = this;
            that.updateData({componentsList: that.componentsList})
        }
    },
    beforeDestroy(){
        // this.$root.Bus.$off('addHotSpot')
    }
};
</script>
<style lang='less' scoped>

  @import '../colorui/main.css';
  .shopInfoComponent {
    font-weight: 300;

    .shopinfo-detail{
      padding: 10px!important;
    }
    .shopinfo-detail img {
      width: 70px !important;
      height: 70px !important;
    }

  }

</style>
