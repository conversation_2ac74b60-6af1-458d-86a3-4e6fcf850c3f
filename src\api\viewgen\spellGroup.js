import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/wxspellgroup/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/weixin/wxspellgroup',
    method: 'post',
    data: obj
  })
}

export function getByPageId(id) {
  return request({
    url: '/weixin/wxspellgroup/getbypage/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/wxspellgroup/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/wxspellgroup',
    method: 'put',
    data: obj
  })
}
