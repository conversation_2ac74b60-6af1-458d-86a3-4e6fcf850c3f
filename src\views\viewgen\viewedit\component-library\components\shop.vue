<template>
    <div class="shopComponent" :style="{marginBottom: `${setData.pageSpacing}px`}">
      <div style="white-space: nowrap;padding: 0px 15px;" v-if="setData.shopInfoData&&setData.shopInfoData.length > 0">
        <div class="cu-bar">
          <div class="shop-selection text-df margin-left" :style="{color: `${setData.titleColor}`}">
            <span class=" text-bold" :class="setData.titleIcon"></span>
            <span class="margin-left-xs">{{setData.title}}</span>
          </div>
          <div class="shop-more text-sm margin-right">更多<text class="cuIcon-right"></text></div>
        </div>
        <div class=" shop-detail" style="overflow-x:scroll;padding: 8px 0;position: relative;display:flex;">
          <div v-for="(item, index) in setData.shopInfoData" :key="index" style="float: left;margin-right: 8px;">
            <div  class="item shadow-warp flex shop-box radius">
              <div class="bg-mask flex shop-image radius">
                <img :src="item.imgUrl" class="radius" style="width: 100px;height: 100px;">
              </div>
              <div class="shop-information text-center">
                <div class="text-white enter-shop text-sm ">进店<text class="cuIcon-right"></text></div>
                <div class="bg-white round enter-bg"></div>
              </div>
              <div class="overflow-2 text-white text-center text-xs shop-name">{{item.name}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>

<script>
import { mapState , mapGetters , mapMutations , mapActions } from 'vuex';
import placeholderImg from "../pages/page-components/placeholderImg";


export default {
    data() {
        return {
          noPic: require('../assets/images/icon/<EMAIL>')
        };
    },
    components: { placeholderImg },
    props: {
        theme : { type: Object | Array },
        setData : { type: Object | Array },
        cId     : { type: Number },
        noEditor: {
            type: Boolean,
            default: false,
        }
    },
    computed: {
        ...mapState({
            componentsList: state => state.divpage.componentsList,
        }),
    },
    created() {
    },
    mounted() {
    },
    methods: {
        ...mapMutations([
            'updateData'
        ]),
    },
    watch:{
        setData(newVal, oldVal){},
        componentsList(newVal, oldVal){          //添加的时候出发（解决没有移动的时候不emit数据）
            let that = this;
            that.updateData({componentsList: that.componentsList})
        }
    },
    beforeDestroy(){
        // this.$root.Bus.$off('addHotSpot')
    }
};
</script>
<style lang='less' scoped>

  @import '../colorui/main.css';
  @import '../colorui/icon.css';
  .shopComponent {
    position: relative;
    display: block;
    width: 100%;
    background: #ffffff;
  }

  .overflow-1 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
  .overflow-2 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
</style>
