<!--商品分类标签-->
<template>
  <div class="cuttingLineSetting">
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">分割线</p>
      <div slot="mainContent">
        <el-form ref="form" label-width="100px" :model="formData">
          <el-divider>基础属性</el-divider>
          <el-form-item label="组件上边距">
            <el-input v-model="formData.paddingTop" size="mini" type="number" style="margin-top: 5px" placeholder="页面下边距">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="组件下边距">
            <el-input v-model="formData.paddingBottom" size="mini" type="number" style="margin-top: 5px" placeholder="页面下边距">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="背景颜色">
            <bg-color-select :thememobile="thememobile" :bgValue="formData.background"
                             @onChange="formData.background = $event"></bg-color-select>
          </el-form-item>
          <el-alert
            title="提示:"
            type="warning"
            description="主题色仅用于背景，实现和虚线需要颜色请选色植代码。"
            center
            :closable="false">
          </el-alert>
          <el-form-item label="分割类型">
            <el-radio-group v-model="formData.type" size="mini">
              <el-radio  :label="0">背景</el-radio>
              <el-radio  :label="1">实线</el-radio>
              <el-radio  :label="2">虚线</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="显示宽度" v-show="formData.type==0" >
            <el-input  v-model="formData.width" size="mini" type="number" placeholder="宽度" :min="0" :max="100" style="margin-top: 5px">
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
          <el-form-item label="显示高度" v-show="formData.type==0" >
            <el-input v-model="formData.height" size="mini" type="number" placeholder="宽度" :min="1"  style="margin-top: 5px">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
    </settingSlot>
  </div>

</template>
<script>

import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';
import settingSlot from '../settingSlot';
import bgColorSelect from "../../pages/page-components/bgColorSelect";

export default {
  components: {settingSlot, bgColorSelect},
  data() {
    return {
      formDataCopy: {
        paddingTop:0,
        paddingBottom:0,
        background:"#E88181",
        type:0,
        width:100,
        height:10,
      },
      formData: {}
    };
  },
  props: {
    thememobile: {type: Object | Array},
    showData: {
      type: Object,
      default: () => {
      }
    },
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
      clickComIndex: state => state.divpage.clickComIndex,
    })
  },
  mounted() {
    let that = this;
    if (that.IsEmptyObj(that.showData)) {
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
    } else {
      that.formData = that.showData
    }
    that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
    // that.updateData({
    //   componentsList: that.componentsList
    // })
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    // 删除按钮
    delBtn(index) {
      let that = this;
      that.$confirm('是否删除该按钮?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
        that.updateData({componentsList: that.componentsList});
      }).catch(() => {
      })
    },
    cancel() {
      this.$emit('cancel')
    },
    reset() {
      let that = this;
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      that.componentsList[that.clickComIndex]['data'] = this.formData;
    },
    confirm() {
      this.$emit('confirm', this.formData)
    },
    delete() {
      this.$emit('delete')
    },
  },
  watch: {
    showData: {
      handler(newVal, oldVal) {
        this.formData = newVal ? newVal : this.formData;
      },
      deep: true
    },
    thememobile() {
    },
  }
};
</script>
<style lang='less' scoped>

.el-form-item {
  margin-bottom: 0;
}

</style>
