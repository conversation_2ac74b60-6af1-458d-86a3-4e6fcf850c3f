import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/wxgoodsspu/page',
    method: 'get',
    params: query
  })
}

export function getCount(query) {
  return request({
    url: '/weixin/wxgoodsspu/count',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/weixin/wxgoodsspu',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/weixin/wxgoodsspu/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/wxgoodsspu/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/wxgoodsspu',
    method: 'put',
    data: obj
  })
}

export function putObjShelf(obj) {
  return request({
    url: '/weixin/wxgoodsspu/shelf',
    method: 'put',
    params: obj
  })
}

export function getStatistics(query) {
  return request({
    url: '/weixin/wxgoodsspu/statistics',
    method: 'get',
    params: query
  })
}
