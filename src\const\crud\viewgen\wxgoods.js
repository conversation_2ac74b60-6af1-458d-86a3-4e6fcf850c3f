export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  excelBtn: true,
  printBtn: true,
  viewBtn: false,
  addBtn: false,
  delBtn: false,
  editBtn: false,
  searchMenuSpan: 6,
  selection: true,
  column: [
    {
      label: '主键',
      prop: 'id',
      hide:true,
      addDisplay: false
    },
    {
      label: '排序',
      prop: 'orderNum',
      width: 100,
      slot: true
    },
    {
      label: '缩略图',
      prop: 'coverUrls',
      width: 100,
      slot: true
    },
    {
      label: '产品名称',
      prop: 'name',
      rules: [
        {
          max: 100,
          message: '长度在不能超过100个字符'
        },
      ]
    },
    {
      label: '上架开始时间',
      prop: 'shelfStartTime',
      sortable: true,
      dicData: [{
        label: '长期上架',
        value: '',
      }]
    },
    {
      label: '上架结束时间',
      prop: 'shelfEndTime',
      sortable: true,
      dicData: [{
        label: '长期上架',
        value: '',
      }]
    },
    {
      label: '是否上架',//（0:上架 1:下架 ）
      prop: 'shelfFlag',
      slot: true,
      sortable: true,
      dicData:[{ label: '上架', value: '0' }, { label: '下架', value: '1' }],//过滤
      filterMethod:function(value, row, column) {
        return row.shelfFlag === value;
      }

    },
    {
      label: '拍摄开始时间',
      prop: 'useStartTime',
      sortable: true,
      dicData: [{
        label: '长期拍摄',
        value: '',
      }]
    },
    {
      label: '拍摄结束时间',
      prop: 'useEndTime',
      sortable: true,
      dicData: [{
        label: '长期拍摄',
        value: '',
      }]
    },
  ]
}


//商品选择组件
export const tableOption2 = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  menu: false,
  excelBtn: true,
  printBtn: true,
  viewBtn: false,
  addBtn: false,
  delBtn: false,
  editBtn: false,
  selection: true,
  searchMenuSpan: 6,
  column: [
    {
      label: '主键',
      prop: 'id',
      hide:true,
      addDisplay: false
    },
    {
      label: '缩略图',
      prop: 'coverUrls',
      width: 100,
      slot: true
    },
    {
      label: '产品名称',
      prop: 'name',
      rules: [
        {
          max: 100,
          message: '长度在不能超过100个字符'
        },
      ]
    },
    {
      label: '上架开始时间',
      prop: 'shelfStartTime',
      sortable: true,
      dicData: [{
        label: '长期上架',
        value: '',
      }]
    },
    {
      label: '上架结束时间',
      prop: 'shelfEndTime',
      sortable: true,
      dicData: [{
        label: '长期上架',
        value: '',
      }]
    },
    {
      label: '是否上架',//（0:上架 1:下架 ）
      prop: 'shelfFlag',
      sortable: true,
      dicData:[{ label: '上架', value: '0' }, { label: '下架', value: '1' }],//过滤
      filterMethod:function(value, row, column) {
        return row.pageType === value;
      }
    },
    {
      label: '拍摄开始时间',
      prop: 'useStartTime',
      sortable: true,
      dicData: [{
        label: '长期拍摄',
        value: '',
      }]
    },
    {
      label: '拍摄结束时间',
      prop: 'useEndTime',
      sortable: true,
      dicData: [{
        label: '长期拍摄',
        value: '',
      }]
    },
  ]
}



//商品标签抽屉
export const tableOption3 = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  menu: true,
  excelBtn: true,
  printBtn: true,
  viewBtn: false,
  addBtn: false,
  delBtn: false,
  editBtn: false,
  selection: true,
  searchMenuSpan: 6,
  column: [
    {
      label: '主键',
      prop: 'id',
      hide:true,
      addDisplay: false
    },
    {
      label: '缩略图',
      prop: 'coverUrls',
      width: 100,
      slot: true
    },
    {
      label: '产品名称',
      prop: 'name',
      rules: [
        {
          max: 100,
          message: '长度在不能超过100个字符'
        },
      ]
    },
    {
      label: '上架开始时间',
      prop: 'shelfStartTime',
      sortable: true,
      dicData: [{
        label: '长期上架',
        value: '',
      }]
    },
    {
      label: '上架结束时间',
      prop: 'shelfEndTime',
      sortable: true,
      dicData: [{
        label: '长期上架',
        value: '',
      }]
    },
    {
      label: '是否上架',//（0:上架 1:下架 ）
      prop: 'shelfFlag',
      sortable: true,
      dicData:[{ label: '上架', value: '0' }, { label: '下架', value: '1' }],//过滤
      filterMethod:function(value, row, column) {
        return row.pageType === value;
      }
    },
    {
      label: '拍摄开始时间',
      prop: 'useStartTime',
      sortable: true,
      dicData: [{
        label: '长期拍摄',
        value: '',
      }]
    },
    {
      label: '拍摄结束时间',
      prop: 'useEndTime',
      sortable: true,
      dicData: [{
        label: '长期拍摄',
        value: '',
      }]
    },
  ]
}


