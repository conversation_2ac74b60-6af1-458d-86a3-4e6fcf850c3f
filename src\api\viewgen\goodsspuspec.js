import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/wxgoodsspuspec/page',
    method: 'get',
    params: query
  })
}

export function fetchTree(query) {
  return request({
    url: '/weixin/wxgoodsspuspec/tree',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/weixin/wxgoodsspuspec',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/weixin/wxgoodsspuspec/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/wxgoodsspuspec/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/wxgoodsspuspec',
    method: 'put',
    data: obj
  })
}
