export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  excelBtn: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  printBtn: true,
  viewBtn: false,
  searchMenuSpan: 6,
  column: [
    {
      label: '主键',
      prop: 'id',
      hide: true,
    },
    {
      label: '名称',
      prop: 'name',
    },
    {
      label: '消息内容',
      prop: 'content',
      hide: true,
    },
    {
      label: '创建时间',
      prop: 'createTime',
      sortable: true,
    },
    {
      label: '所属公众号',
      prop: 'appId',
      filters:true,
      filterMethod:function(value, row, column) {
        return row.appId === value;
      },
      dicData:[],
    },

  ]
}
