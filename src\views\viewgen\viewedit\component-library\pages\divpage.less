
.div-page-index {
  // position: relative;
  width: 100%;
  //overflow-y: auto;
  height: 98vh;
  .content {
    height: 90%;
    .left-class {
      border: 1px solid #d7dae2;
      padding: 20px;
      border-radius: 4px;
      min-width: 240px;
      max-width: 400px;
      //width:30vh;
    }

    .focus-class {
      border: 1px solid #409EFF !important;
    }

    .showContent {
      position: relative;
      min-width: 440px;
      margin-left: 10px;
      background-color: #FFFFFF;
      height: 100%;
      .pageContent {
        position: relative;
        margin: 0px 0 0 20px;
        padding: 0 0 0 0;
        width: 375px;
        height: 100%;
        box-shadow: 1px 2px 20px 2px rgba(0, 0, 0, 0.1);
        border: #e1e1e1 solid 1px;
        background: white;

        .pageTopBlock {
          top: 0;
          left: 0;
          width: 100%;
          height: 51px;
          position: absolute;

          .pageTopImg {
            width: 100%;
            height: 49px;
          }

          p {
            position: absolute;
            left: 0;
            bottom: 15px;
            width: 100%;
            text-align: center;
          }
        }

        .componentsList {
          height: 100%;
          overflow: scroll;
          //background: #f1f1f1;
          background-attachment:local;
          background-repeat: no-repeat;
          background-size: 100%;

          &::-webkit-scrollbar {
            display: none
          }
        ;
        }

        .drag-item {
          // padding: 10px;
          // width: 355px;
          margin: auto;
          position: relative;
          //background: #ffffff;

          &:hover {
            cursor: move;
            border: 1px dashed #409EFF;
          }
        }

        .modal {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, .6);
        }
      }

      .funBlock {
        position: absolute;
        right: -40px;
        top: 0;
        padding: 10px 5px;
        width: 30px;
        font-size: 20px;
        text-align: center;
        background: white;
        border-radius: 5px;
        box-shadow: 0 0 5px #a9a9a9;
        z-index: 101;

        .icon {
          & + .icon {
            margin-top: 10px;
          }

          &:hover {
            color: #409EFF;
            cursor: pointer;
          }
        }
      }
    }

    .btns {
      height: calc(~"100% - 10px");
      width: 300px;
      text-align: left;
      background: white;

      /deep/ .el-button {
        margin: 10px;
      }

      .funBtnItem {
        display: inline-block;
        padding: 20px 10px;
        width: calc(~"50% - 44px");
        text-align: center;
        cursor: pointer;

        .icon {
          font-size: 20px;
        }
      }
    }
  }

  .settingBlock {
    position: absolute;
    background: #fff;
    top: 1px;
    z-index: 100;
    width: 50%;
    height: 80vh;
    .pseudoRow {
      position: absolute;
      content: "";
      left: -5px;
      border: 5px solid #fff;
      transform: rotateZ(45deg);
    }
  }
}
