<template>
  <div class="goods-selector">
    <basic-container>
      <el-form :inline="true" class="search-form">
        <el-form-item label="商品名称">
          <el-input v-model="searchForm.name" placeholder="请输入商品名称" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
      
      <el-table
        :data="tableData"
        v-loading="tableLoading"
        @row-click="handleRowClick"
        highlight-current-row
        style="width: 100%">
        <el-table-column
          type="index"
          width="50">
        </el-table-column>
        <el-table-column
          prop="picUrls[0]"
          label="商品图片"
          width="120">
          <template slot-scope="scope">
            <el-image
              style="width: 80px; height: 80px"
              :src="scope.row.picUrls[0]"
              :preview-src-list="scope.row.picUrls">
            </el-image>
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          label="商品名称"
          min-width="200">
        </el-table-column>
        <el-table-column
          prop="priceDown"
          label="价格"
          width="150">
          <template slot-scope="scope">
            <div style="color: red">￥{{scope.row.priceDown}}{{scope.row.priceUp == scope.row.priceDown ? '' : '~￥'+
              scope.row.priceUp}}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="120">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click.stop="selectGoods(scope.row)">选择</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="page.currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="page.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total">
      </el-pagination>
    </basic-container>
  </div>
</template>

<script>
import {getPage} from '@/api/mall/goodsspu'

export default {
  name: 'GoodsSelector',
  data() {
    return {
      searchForm: {
        name: ''
      },
      tableData: [],
      tableLoading: false,
      page: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
        ascs: [],
        descs: 'create_time'
      }
    }
  },
  created() {
    this.getPage(this.page)
  },
  methods: {
    getPage(page, params) {
      this.tableLoading = true
      getPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
        shelf: '1' // 只查询上架商品
      }, params, this.searchForm)).then(response => {
        let tableData = response.data.data.records
        tableData.forEach(function (item) {
          if (!item.picUrls) {
            item.picUrls = []
          }
        })
        this.tableData = tableData
        this.page.total = response.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    handleSearch() {
      this.page.currentPage = 1
      this.getPage(this.page)
    },
    resetSearch() {
      this.searchForm = {
        name: ''
      }
      this.handleSearch()
    },
    handleSizeChange(val) {
      this.page.pageSize = val
      this.getPage(this.page)
    },
    handleCurrentChange(val) {
      this.page.currentPage = val
      this.getPage(this.page)
    },
    handleRowClick(row) {
      this.selectGoods(row)
    },
    selectGoods(goods) {
      this.$emit('select-goods', goods)
    }
  }
}
</script>

<style lang="less" scoped>
.goods-selector {
  position: relative;
  z-index: 2001;
  .search-form {
    margin-bottom: 20px;
  }
  
  .el-pagination {
    margin-top: 20px;
    text-align: right;
  }
}
</style> 