<!--
  公共组件————用户标签选择框
  使用场景
  1.商品选择处，打标签，关联起来
  2.页面装修处，返回标签
-->
<template>
  <div class="execution">
    <basic-container>
      <el-row type="flex" justify="start">
        <el-col >
          <el-input
            placeholder="请输入要搜索的标签"
            size="small"
            @keyup.enter.native="search"
            v-model="searchValue">
          </el-input>
        </el-col>
        <el-col >
          <el-button type="primary" size="small" @click="search">确认搜索</el-button>
          <el-button type="primary" size="small" @click="resetSearch">重置</el-button>
        </el-col>
        <el-col >
          <el-button type="primary" size="small" @click="openTagBox('add')"><i class="el-icon-circle-plus"></i>新建标签
          </el-button>
        </el-col>
      </el-row>
<!--      单选-->
      <el-row v-show="selectedType == 0">
        <div class="main_tag_show">
          <el-radio-group v-model="radioSelected" @change="radioClick">
            <div v-for="(tagType,index1) in tagAndTypeList"  :key="index1">
              <div :id="'tagTypeId'+tagType.id">
                <el-divider content-position="left">{{ tagType.tagTypeName }}</el-divider>
                <div class="main_tag" v-for="(tag,index2) in tagType.tagList" :key="index2">
                  <el-radio :label="tag">
                    <el-tag  v-popover="tag.id" size="small" :color="tag.backColor"
                            :style="getFontColor(tag.fontColor)">{{ tag.name }}
                    </el-tag>
                  </el-radio>
                </div>
              </div>
            </div>
          </el-radio-group>
        </div>
      </el-row>
<!--      多选-->
      <el-row v-show="selectedType == 1">
        <div class="main_tag_show">
          <el-checkbox-group v-model="checkSelected" @change="checkClick">
            <div v-for="(tagType,index1) in tagAndTypeList"  :key="index1">
              <div :id="'tagTypeId'+tagType.id">
                <el-divider content-position="left">{{ tagType.tagTypeName }}</el-divider>
                <div class="main_tag" v-for="(tag,index2) in tagType.tagList" :key="index2">
                  <el-checkbox :label="tag">
                    <el-tag  v-popover="tag.id" size="small" :color="tag.backColor"
                            :style="getFontColor(tag.fontColor)">{{ tag.name }}
                    </el-tag>
                  </el-checkbox>
                </div>
              </div>
            </div>
          </el-checkbox-group>
        </div>
      </el-row>
    </basic-container>
    <!--    添加标签框-->
    <el-dialog
      append-to-body
      :title="tagBox.title"
      :visible.sync="tagBox.visible"
      width="30%"
      :close-on-click-modal="false"
      center>
      <el-form ref="form" :model="tag" label-width="80px">
        <el-form-item label="标签名称">
          <el-input v-model="tag.name" :maxlength="25" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="标签分类">
          <el-select v-model="tag.typeId" clearable placeholder="请选择">
            <el-option
              v-for="item in tagAndTypeList"
              :key="item.typeId"
              :label="item.tagTypeName"
              :value="item.typeId">
            </el-option>
          </el-select>
          <el-button type="primary" size="mini" @click="openTagTypeBox">添加分类</el-button>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="背景色">
              <el-color-picker v-model="tag.backColor"></el-color-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="字体色">
              <el-color-picker v-model="tag.fontColor"></el-color-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="是否置顶">
          <el-switch v-model="tag.topFlag"></el-switch>
        </el-form-item>
        <el-form-item label="效果展示" v-show="tag.name">
          <el-tag size="small" :color="tag.backColor" :style="getFontColor(tag.fontColor)">{{ tag.name }}</el-tag>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="confirmTag">确认</el-button>
          <el-button @click="tagBox.visible = false">取消</el-button>
        </el-form-item>
      </el-form>

      <!--     嵌套添加分类框-->
      <el-dialog
        width="30%"
        title="添加分类"
        :visible.sync="tagTypeBoxVisible"
        :close-on-click-modal="false"
        append-to-body>
        <el-form ref="form" :model="tagType" label-width="80px">
          <el-form-item label="分类名称">
            <el-input v-model="tagType.name" :maxlength="25" show-word-limit></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="addTagType">立即创建</el-button>
            <el-button @click="tagTypeBoxVisible = false">取消</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
import {getUserTagAndType, addObj as addTag, delObj as delTag, getSysList as getTagList,} from '@/api/wxmp/wxusertags'
import {
  addObj as addTagType,
  putObj as putTagType,
  delObj as delTagType,
  getList as getTagTypeList
} from '@/api/wxmp/wxtagtype'
import {getList as getWxAppList} from '@/api/wxmp/wxapp'
import {tagTypeOption} from '@/const/crud/wxmp/wxtagtype'
import {mapGetters} from 'vuex'

export default {
  props: {
    appId: {//查询的公众号Id
      type: String,
      default: ''
    },
    selectedType: {  //选择类型  单选：0  多选:1
      type: Number
    },
    selectedTagId: {type: String | Array}//选中的标签  单个id  或  id数组
  },
  watch: {
    appId(newVal, oldVal) {
      // console.log("监听公众号的改变", newVal);
      this.getUserTagAndType();
    },
    selectedTagId(newVal, oldVal) {
      console.log("selectedTagId", newVal);
      if(newVal !=oldVal){
        this.ensureTagById(newVal)
      }
    }
  },
  data() {
    return {
      checkSelected:[],//多选
      radioSelected: '',//单选
      tableLoading: false,
      tagTypeOption: tagTypeOption,
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      tagBox: {
        title: '',
        visible: false,
        type: ''
      },
      tagTypeBoxVisible: false,//内部分类框
      openTagTypeManagerBoxVisible: false,//分类管理
      currentApp: "",//当前app
      searchValue: "",//标签搜索值
      tagAndTypeList: [],//标签数组
      tag: {
        fontColor: ''
      },
      tagType: {},
    };
  },
  created() {
  },
  mounted() {
    this.getUserTagAndType();
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.permissions['wxmp:wxusertags:add'] ? true : false,
        delBtn: this.permissions['wxmp:wxusertags:del'] ? true : false,
        editBtn: this.permissions['wxmp:wxusertags:edit'] ? true : false,
        viewBtn: this.permissions['wxmp:wxusertags:get'] ? true : false
      }
    }
  },
  methods: {
    //加载公众号列表
    getWxAppList() {
      getWxAppList({
        appType: '2'
      }).then(response => {
        let data = response.data;
        console.log("加载公众号列表", data)
        this.currentApp = data[0];
      }).catch((err) => {
        console.log(err)
      })
    },
    //加载标签
    getUserTagAndType() {
      if (!this.appId) {
        return
      }
      getUserTagAndType({
        appId: this.appId,
      }).then(res => {
        console.log("加载标签", res);
        this.tagAndTypeList = res.data.data;
        if(this.selectedTagId){
          this.ensureTagById(this.selectedTagId)
        }
      }).catch(err => {
        console.log(err)
      })
    },
    //搜索标签
    search() {
      if (!this.appId) {
        return
      }
      getUserTagAndType({
        appId: this.appId,
        name: this.searchValue
      }).then(res => {
        console.log("搜索标签", (res.data.data))
        this.tagAndTypeList = res.data.data;
        this.ensureTagById(this.selectedTagId);
      }).catch(err => {
        console.log(err)
      })
    },
    openTagBox(type, obj, att) {
      if (type == 'add') {
        this.tag = {
          name: "",
          backColor: '#409eff',
          fontColor: '#fff'
        }
        if (att == "att") {
          this.tagBox.attention = true;
        }
        this.tagBox.type = "add";
        this.tagBox.title = "添加标签";
      }
      if (type == 'put') {
        this.tagBox.type = "put";
        this.tagBox.title = "修改标签";
        this.tag = {
          id: obj.id,
          name: obj.name,
          backColor: obj.backColor,
          fontColor: obj.fontColor,
          typeId: obj.typeId,
          topFlag: obj.topFlag
        }
      }
      this.tagBox.visible = true;
    },
    openTagTypeBox() {
      this.tagTypeBoxVisible = true;
    },
    //转化得到字体颜色
    getFontColor(val) {
      if (!val) {
        return;
      }
      return "color:" + val;
    },
    // 添加标签
    confirmTag() {
      if (!this.tag.name) {
        this.$message.warning("请填写标签名称");
        return;
      }
      if (!this.tag.typeId) {
        this.$message.warning("请选择标签类型");
        return;
      }
      if (this.tagBox.type == 'add') {
        console.log("保存参数", this.tag)
        addTag({
          appId: this.appId,
          name: this.tag.name,
          backColor: this.tag.backColor,
          fontColor: this.tag.fontColor,
          typeId: this.tag.typeId,
          operationType:0
        }).then(res => {
          if (res.data.code == 0) {
            this.$message.success("添加成功")
            this.getUserTagAndType();
            this.tagBox.visible = false;
          }
        }).catch()
      }
    },
    // 添加分类
    addTagType() {
      if (!this.tagType.name) {
        this.$message.warning("请填写分类名称");
        return;
      }
      addTagType({
        appId: this.appId,
        name: this.tagType.name,
      }).then(res => {
        if (res.data.code == 0) {
          this.$message.success("添加成功")
          this.getUserTagAndType();
          this.tagTypeBoxVisible = false;
        }
      }).catch()
    },
    // 点击分类确定定位
    ensureLocation(id) {
      // todo 吸顶问题的解决
      // console.log(id)
      let name = ("tagTypeId" + id);
      document.getElementById(name).scrollIntoView({behavior: "smooth", block: "center", inline: "nearest"});
      // document.getElementById(name).scroll({
      //   top: height, //向上移动的距离，如果有fixede布局， 直接减去对应距离即可
      //   behavior: 'smooth', // 平滑移动
      // });
    },
    openDelTag(id) {
      this.$confirm('此操作将永久删除该标签, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delTag(id).then(res => {
          if (res.data.code == 0) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
            this.refreshChange();
          }
        }).catch()
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    //根据id选中标签
    ensureTagById(id){
      console.log("选中标签",id)
      if(this.selectedType == 0){ //单选
        if(!id){
          this.radioSelected = "";
          return
        }
        for (let i in this.tagAndTypeList) {
          for (let j in this.tagAndTypeList[i].tagList ) {
            if(this.tagAndTypeList[i].tagList[j].id == id){
              this.radioSelected = this.tagAndTypeList[i].tagList[j];
              break;
            }
          }
        }
      }else if(this.selectedType == 1){//多选
        if(!id || id.length == 0){
          this.checkSelected = [];
          return
        }
        this.checkSelected = [];
        // console.log("this.tagAndTypeList",this.tagAndTypeList)
          for (let j in this.tagAndTypeList) {
            for (let k in this.tagAndTypeList[j].tagList ) {
              for (let i = 0; i <id.length; i++) {
                if(this.tagAndTypeList[j].tagList[k].id == id[i]){
                  // console.log("选中",this.tagAndTypeList[j][k])
                  this.checkSelected.push(this.tagAndTypeList[j].tagList[k]);
                  break;
                }
              }
            }
          }
      }
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel(row, index) {
      let _this = this
      this.$confirm('是否确认删除此数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delTagType(row.id).then(() => {
          this.refreshChange();
        })
      }).then(data => {
        _this.$message({
          showClose: true,
          message: '删除成功',
          type: 'success'
        })
        this.getTagTypePage(this.page)
      }).catch(function (err) {
      })
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate(row, index, done, loading) {
      putTagType(row).then(response => {
        this.$message({
          showClose: true,
          message: '修改成功',
          type: 'success'
        })
        done()
        this.getTagTypePage(this.page);
        this.getTagTypeList();
      }).catch(() => {
        loading()
      })
    },
    /**
     * 刷新回调
     */
    refreshChange() {
      // this.getTagTypePage(this.page)
      this.getTagTypeList();
      this.getUserTagAndType();
    },
    handleSave(row, done, loading) {
      addTagType({
        appId: this.appId,
        name: row.name,
      }).then(response => {
        this.$message({
          showClose: true,
          message: '添加成功',
          type: 'success'
        })
        done()
        this.refreshChange()
      }).catch(() => {
        loading()
      })
    },
    //单选选中标签
    radioClick(obj){
      if (this.$listeners['ensureTag']) {
        this.$emit("ensureTag", obj);
      }
    },
    //多选选中标签
    checkClick(obj){
      if (this.$listeners['ensureTag']) {
        this.$emit("ensureTag", obj);
      }
    },
    //重置搜索
    resetSearch() {
      this.searchValue ="";
      getTagList({
        appId :this.appId,
      }).then(res=>{
        this.tagAndTypeList = res.data.data;
        this.ensureTagById(this.selectedTagId)
      }).catch(err=>{
        console.log(err)
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.main_popover_button {
  display: block;
  margin: 0px 0px 5px 5px;
  display: block;
  width: 140px
}

.aside_tag_type {
  //position: fixed;
  text-align: center;
}

.main_tag_show {
  padding-left: 10px;
}

.tag_type_li {
  padding-top: 10px;
  border: 1px solid white;
}

.tag_type_li:hover {
  color: #409eff;;
  border-bottom: 1px solid #409eff;;
  cursor: pointer;
}

.tag_type_name {
  overflow: hidden;
  text-overflow: ellipsis;
  padding-bottom: 15px;
}

.main_tag {
  display: inline-block;
  padding-right: 20px;
  padding-bottom: 30px;
}
</style>
