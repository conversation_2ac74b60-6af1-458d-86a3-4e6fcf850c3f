<template>
    <div class="videoComponent">
        <div class="video-container" :style="{
            padding: `${setData.padding || 10}px`,
            backgroundColor: setData.background || '#ffffff'
        }">
          <div class="video-title" v-if="setData.showTitle" :style="{
                color: setData.titleColor || '#333333',
                fontSize: `${setData.titleSize || 14}px`,
                textAlign: setData.titlePosition || 'center',
                padding: '8px 0',
                fontWeight: setData.titleBold ? 'bold' : 'normal'
            }">
            {{setData.title || '视频标题'}}
          </div>
            <video
                :src="setData.videoUrl"
                :poster="setData.coverUrl"
                :controls="setData.showControls"
                :autoplay="setData.autoplay"
                :loop="setData.loop"
                :muted="setData.muted"
                class="video-player"
                :style="{
                    borderRadius: `${setData.radius || 0}px`,
                    width: '100%',
                    height: setData.height ? `${setData.height}px` : 'auto'
                }"
            ></video>
        </div>
    </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex';

export default {
    data() {
        return {
        };
    },
    components: { },
    props: {
        theme: { type: Object | Array },
        setData: { type: Object | Array },
        cId: { type: Number }
    },
    computed: {
        ...mapState({
            componentsList: state => state.divpage.componentsList,
        }),
    },
    created() {},
    mounted() {},
    methods: {
        ...mapMutations([
            'updateData'
        ]),
    },
    watch:{
        componentsList(newVal, oldVal){          //添加的时候触发（解决没有移动的时候不emit数据）
            let that = this;
            that.updateData({componentsList: that.componentsList})
        }
    }
};
</script>
<style lang='less' scoped>
.videoComponent {
    position: relative;
    display: block;
    width: 100%;

    .video-container {
        position: relative;
        width: 100%;
    }

    .video-player {
        display: block;
    }

    .video-title {
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}
</style>
