<template>
  <div class="rollingListSetting">
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">滚动列表</p>
      <div slot="mainContent">
        <el-form ref="form" label-width="100px" :model="formData">
          <el-divider>基础属性</el-divider>
          <el-form-item label="进入数量">
            <el-input-number v-show="formData.enterFlag" controls-position="right" v-model="formData.enterNum" :min="0"  placeholder="请输入进入数量"></el-input-number>
            <el-switch
              v-model="formData.enterFlag"
              active-text="启用"
              inactive-text="关闭">
            </el-switch>
          </el-form-item>
          <el-form-item label="支付数量">
            <el-input-number v-show="formData.payFlag" controls-position="right" v-model="formData.payNum" :min="0"  placeholder="请输入支付数量"></el-input-number>
            <el-switch
              v-model="formData.payFlag"
              active-text="启用"
              inactive-text="关闭">
            </el-switch>
          </el-form-item>
          <el-divider>动态数据</el-divider>
          <el-form-item label="查看数据">
            <el-button type="text"  @click="openInteriorBox">内部数据</el-button>
          </el-form-item>
        </el-form>
      </div>
    </settingSlot>
    <el-dialog
      title="内部数据"
      :visible.sync="interiorBoxVisible "
      :close-on-click-modal="false"
      center
      :append-to-body="true"
      lock-scroll
      width="80%">
      <div style="overflow: hidden">
        <interiorData   :pageId="pageId"  v-on:ensureInterior="ensureInterior" @backFun="ensureInterior"></interiorData>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';
import draggable from "vuedraggable";
import settingSlot from '../settingSlot'
import store from "@/store";
import interiorData from "@/components/interior-data/main";

export default {
  components: {settingSlot,draggable,interiorData},
  data() {
    return {
      formDataCopy: {
        payNum:100,
        enterNum:1000,
        payFlag:true,
        enterFlag:true,
        pageId:'',
      },
      formData: {},
      interiorBoxVisible: false
    };
  },
  props: {
    thememobile: {type: Object | Array},
    showData: {
      type: Object,
      default: () => {
      }
    },
  },
  watch: {
    showData: {
      handler(newVal, oldVal) {
        this.formData = newVal ? newVal : this.formData;
      },
      deep: true
    },

  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
      clickComIndex: state => state.divpage.clickComIndex,
    })
  },
  created(){
    this.pageId = window.localStorage.getItem('viewEditId');
    console.log("滚动列表",this.pageId)
  },
  mounted() {
    let that = this;
    if (that.IsEmptyObj(that.showData)) {

      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
    } else {
      that.formData = that.showData
    }

    that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
    // that.updateData({
    //   componentsList: that.componentsList
    // })
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    // 删除按钮
    cancel() {
      this.$emit('cancel')
    },
    reset() {
      let that = this;
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      that.componentsList[that.clickComIndex]['data'] = this.formData;
    },
    confirm() {
      this.$emit('confirm', this.formData)
    },
    delete() {
      this.$emit('delete')
    },
    openInteriorBox() {
      this.interiorBoxVisible = true;
    },
    ensureInterior() {
    },
  },
};
</script>
<style lang='less' scoped>
</style>
