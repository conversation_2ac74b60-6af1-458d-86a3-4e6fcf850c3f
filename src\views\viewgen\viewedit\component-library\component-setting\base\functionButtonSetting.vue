<template>
  <div class="functionButtonSetting">
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">多功能按钮</p>
      <div slot="mainContent">
        <el-divider>基础属性</el-divider>
        <el-form ref="form" label-width="100px" :model="formData">
          <el-form-item label="页面上距">
            <el-input v-model="formData.pageMarginTop" size="mini" type="number" style="margin-top: 5px"
                      placeholder="与上面元素的间隔">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="页面下距">
            <el-input v-model="formData.pageMarginBottom" size="mini" type="number" style="margin-top: 5px"
                      placeholder="与下面元素的间隔">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="显示规则">
            <el-radio-group v-model="formData.showRule">
              <el-radio :label="1">一直显示</el-radio>
              <el-radio :label="2">未购买显示</el-radio>
              <el-radio :label="3">购买后显示</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="悬浮位置">
            <el-radio-group v-model="formData.location">
              <el-radio :label="1">不悬浮</el-radio>
              <el-radio :label="2">底部悬浮</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="组件高度">
            <el-input v-model="formData.height" size="mini" type="number" style="margin-top: 5px"
                      placeholder="">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-divider>按钮设置</el-divider>
          <draggable v-model="formData.buttonList" @update="datadragUpdate" @end="datadragEnd"  @start="datadragStart"
                     :move="datadragMove" :options="{animation:500,handle:'.drag_button'}">
            <transition-group>
              <div v-for="(item ,index) in formData.buttonList" :key="index">
<!--                文字按钮-->
                <div class="drag-item" v-if="item.showType ==1">
                  <el-form-item>
                    <el-button class="del_button" size="mini" icon="el-icon-delete-solid" @click="delButton(index)"
                               type="danger"></el-button>
                    <el-button class="drag_button" size="mini" icon="el-icon-rank"
                               type="success"></el-button>
                  </el-form-item>
                  <el-form-item label="按钮标题">
                    <el-input v-model="item.title" size="mini" placeholder="标题"></el-input>
                  </el-form-item>
                  <el-form-item label="按钮宽度">
                    <el-input v-model="item.width" size="mini" type="number" style="margin-top: 5px"
                              placeholder="按钮的宽度">
                      <template slot="append">px</template>
                    </el-input>
                  </el-form-item>
                  <el-form-item label="字体的间距">
                    <el-input v-model="item.fontSpacing" size="mini" type="number" style="margin-top: 5px"
                              placeholder="按钮的宽度">
                      <template slot="append">px</template>
                    </el-input>
                  </el-form-item>
                  <el-form-item label="背景颜色">
                    <el-input v-model="item.backColor" size="small" style="margin-top: 5px">
                      <template slot="append">
                        <el-color-picker size="mini" v-model="item.backColor"></el-color-picker>
                      </template>
                    </el-input>
                  </el-form-item>
                  <el-form-item label="字体颜色">
                    <el-input v-model="item.fontColor" size="small" style="margin-top: 5px">
                      <template slot="append">
                        <el-color-picker size="mini" v-model="item.fontColor"></el-color-picker>
                      </template>
                    </el-input>
                  </el-form-item>
                  <el-form-item label="字体加粗">
                    <el-switch
                      v-model="item.fontWeight"
                      active-text="加粗"
                      inactive-text="普通">
                    </el-switch>
                  </el-form-item>
                  <el-form-item label="字体大小">
                    <el-input v-model="item.fontSize" size="mini" type="number" style="margin-top: 5px"
                              placeholder="按钮字体大小"></el-input>
                  </el-form-item>
                  <el-form-item label="按钮类型">
                    <el-radio-group v-model="item.button.type"  @change="(val)=>{return radioChange(val,index)}">>
                      <el-radio :label="1">新页面</el-radio>
                      <el-radio :label="2">购买按钮</el-radio>
                      <el-radio :label="3">订单信息</el-radio>
                      <el-radio :label="4">弹窗显示</el-radio>
                      <el-radio :label="5">电话号码</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="跳转链接" v-show="item.button.type==1" >
                    <wx-page-select
                      :isSystemUrl="item.button.isSystemUrl"
                      @switchChange="item.button.isSystemUrl=$event"
                      :page="item.button.pageUrl"
                      @change="item.button.pageUrl=$event"></wx-page-select>
                  </el-form-item>
                  <div  v-show="item.button.type==2">
                    <el-form-item label="强制获取手机" >
                      <el-switch
                        v-model="item.button.phoneFlag"
                        active-text="是"
                        active-color="#13ce66"
                        inactive-text="否">
                      </el-switch>
                    </el-form-item>
                    <el-form-item label="支付后标签" >
                      <div @click="openUserTagBox(index)">
                        <el-button v-show="!(item.button.payTag && item.button.payTag.id)" icon="el-icon-plus" size="mini"></el-button>
                        <el-tag v-show="(item.button.payTag && item.button.payTag.id)"  size="medium" :color="item.button.payTag?item.button.payTag.backColor:''"
                                :style="item.button.payTag?getFontColor(item.button.payTag.fontColor):''">{{ item.button.payTag?item.button.payTag.name:'' }} </el-tag>
                      </div>
                    </el-form-item>
                    <el-form-item label="订单名称" >
                      <el-input v-model="item.button.name" size="mini"  style="margin-top: 5px"
                                placeholder="请输入订单名称">
                      </el-input>
                    </el-form-item>
                    <el-form-item label="支付金额" >
                      <el-input-number v-model="item.button.price" :precision="2" size="mini" controls-position="right" :min="0" :max="99999999"></el-input-number>
                    </el-form-item>
                    <el-form-item label="支付后跳转" >
                      <wx-page-select
                        :isSystemUrl="item.button.isSystemUrl"
                        @switchChange="item.button.isSystemUrl=$event"
                        :page="item.button.pageUrl"
                        @change="item.button.pageUrl=$event"></wx-page-select>
                    </el-form-item>
                  </div>
                  <el-form-item v-show="item.button.type==4" label="弹窗类型">
                    <el-radio-group v-model="item.button.popupType">
                      <el-radio :label="1">文字</el-radio>
                      <el-radio :label="2">图片</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="提示内容" v-show="item.button.type==4 && item.button.popupType ==1">
                    <el-input v-model="item.button.content" size="small" style="margin-top: 5px"></el-input>
                  </el-form-item>
                  <el-form-item label="图片选择" v-show="item.button.type==4 && item.button.popupType ==2">
                    <MaterialList :value="item.button.imgUrl?[item.button.imgUrl]:[]"
                                  @sureSuccess="item.button.imgUrl = $event?$event[0]:''"
                                  @deleteMaterial="item.button.imgUrl = ''"
                                  type="image" shopId="-1"
                                  :num=1
                                  :divStyle="'width:100%;height:95px;margin-bottom:8px;line-height: 100px;'"></MaterialList>
                  </el-form-item>
                  <el-form-item v-show="item.button.type==5" label="所需号码">
                    <el-input v-model="item.button.phone" size="mini" placeholder="手机号码"></el-input>
                  </el-form-item>
                </div>
<!--                图片按钮-->
                <div class="drag-item" v-if="item.showType ==2">
                  <el-form-item>
                    <el-button class="del_button" size="mini" icon="el-icon-delete-solid" @click="delButton(index)"
                               type="danger"></el-button>
                    <el-button class="drag_button" size="mini" icon="el-icon-rank"
                               type="success"></el-button>
                  </el-form-item>
                  <el-form-item label="图片选择">
                    <MaterialList :value="item.imgUrl?[item.imgUrl]:[]"
                                  @sureSuccess="item.imgUrl = $event?$event[0]:''"
                                  @deleteMaterial="item.imgUrl = ''"
                                  type="image" shopId="-1"
                                  :num=1
                                  :divStyle="'width:100%;height:95px;margin-bottom:8px;line-height: 100px;'"></MaterialList>
<!--                    <el-upload-->
<!--                      class="avatar-uploader"-->
<!--                      :action="actionUrl"-->
<!--                      :headers="header"-->
<!--                      :show-file-list="false"-->
<!--                      :on-success="handleAvatarSuccess"-->
<!--                      :before-upload="(file)=>{beforeAvatarUpload(file,index)}">-->
<!--                      <img v-if="item.imgUrl" :src="item.imgUrl" class="avatar">-->
<!--                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>-->
<!--                    </el-upload >-->
                  </el-form-item>
                  <el-form-item label="图片地址">
                    <el-input v-model="item.imgUrl" size="mini" type="text" style="margin-top: 5px">
                    </el-input>
                  </el-form-item>
                  <el-form-item label="按钮宽度">
                    <el-input v-model="item.width" size="mini" type="number" style="margin-top: 5px"
                              placeholder="按钮的宽度">
                      <template slot="append">px</template>
                    </el-input>
                  </el-form-item>
                  <el-form-item label="按钮类型">
                    <el-radio-group v-model="item.button.type"  @change="(val)=>{return radioChange(val,index)}">
                      <el-radio :label="1">新页面</el-radio>
                      <el-radio :label="2">购买按钮</el-radio>
                      <el-radio :label="3">订单信息</el-radio>
                      <el-radio :label="4">弹窗显示</el-radio>
                      <el-radio :label="5">电话号码</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="跳转链接" v-show="item.button.type==1" >
                    <wx-page-select
                      @switchChange="item.button.isSystemUrl=$event"
                      :clientType="'H5'" :page="item.button.pageUrl"
                      @change="item.button.pageUrl=$event"></wx-page-select>
                  </el-form-item>
                  <div  v-show="item.button.type==2">
                    <el-form-item label="强制获取手机" >
                      <el-switch
                        v-model="item.button.phoneFlag"
                        active-color="#13ce66"
                        active-text="是"
                        inactive-text="否">
                      </el-switch>
                    </el-form-item>
                    <el-form-item label="支付后标签" >
                        <el-button @click="openUserTagBox(index)" v-show="!(item.button.payTag && item.button.payTag.id)" icon="el-icon-plus" size="mini"></el-button>
                        <el-tag @click="openUserTagBox(index)" v-show="(item.button.payTag && item.button.payTag.id)"  size="medium" :color="item.button.payTag?item.button.payTag.backColor:''"
                                :style="item.button.payTag?getFontColor(item.button.payTag.fontColor):''">{{ item.button.payTag?item.button.payTag.name:'' }} </el-tag>
                        <el-button @click="deleteTag(item.button)" v-show="(item.button.payTag && item.button.payTag.id)" icon="el-icon-delete" type="danger" size="mini"></el-button>
                    </el-form-item>
                    <el-form-item label="订单名称" >
                      <el-input v-model="item.button.name" size="mini"  style="margin-top: 5px"
                                placeholder="请输入订单名称">
                        </el-input>
                    </el-form-item>
                    <el-form-item label="支付金额" >
                      <el-input-number v-model="item.button.price" :precision="2" size="mini" controls-position="right" :min="0" :max="9999999"></el-input-number>
                    </el-form-item>
                    <el-form-item label="支付后跳转" >
                      <wx-page-select
                        :isSystemUrl="item.button.isSystemUrl"
                        @switchChange="item.button.isSystemUrl=$event"
                        :page="item.button.pageUrl"
                        @change="item.button.pageUrl=$event"></wx-page-select>
                    </el-form-item>
                  </div>
                  <el-form-item v-show="item.button.type==4" label="弹窗类型">
                    <el-radio-group v-model="item.button.popupType">
                      <el-radio :label="1">文字</el-radio>
                      <el-radio :label="2">图片</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="提示内容" v-show="item.button.type==4 && item.button.popupType==1">
                    <el-input v-model="item.button.content" size="small" style="margin-top: 5px"></el-input>
                  </el-form-item>
                  <el-form-item label="图片选择" v-show="item.button.type==4 && item.button.popupType==2">
                    <MaterialList :value="item.button.imgUrl?[item.button.imgUrl]:[]"
                                  @sureSuccess="item.button.imgUrl = $event?$event[0]:''"
                                  @deleteMaterial="item.button.imgUrl = ''"
                                  type="image" shopId="-1"
                                  :num=1
                                  :divStyle="'width:100%;height:95px;margin-bottom:8px;line-height: 100px;'"></MaterialList>
                  </el-form-item>
                  <el-form-item v-show="item.button.type==5" label="所需号码">
                    <el-input v-model="item.button.phone" size="mini" placeholder="手机号码"></el-input>
                  </el-form-item>
                </div>
              </div>
            </transition-group>
          </draggable>
          <div>
            <el-button type="text" v-show="addFontButtonVisible" @click="addFontButton">添加文字按钮</el-button>
            <el-button type="text" v-show="addImgButtonVisible" @click="addImgButton">添加图片按钮</el-button>
          </div>
        </el-form>
      </div>
    </settingSlot>
    <!--    用户标签弹出框-->
    <el-dialog title="用户标签" :visible.sync="userTagBoxVisible" :append-to-body="true">
      <div style="overflow: hidden">
        <wx-user-tag  :selectedType="0"  :selectedTagId="userTagSelect"  :appId="appId" v-on:ensureTag="ensureTag" @backFun="ensureTag"></wx-user-tag>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';
import draggable from "vuedraggable";
import settingSlot from '../settingSlot'
import store from "@/store";
// import bgColorSelect from "../../pages/page-components/bgColorSelect";
import MaterialList from '@/components/material/wxlist.vue'
import wxPageSelect from '@/components/wx-page-select/Index.vue'
import wxUserTag from '@/views/wxmp/wxusertags/userTagSelect' // 用户标签
export default {
  components: {settingSlot,draggable,wxPageSelect,wxUserTag,MaterialList},
  data() {
    return {
      userTagBoxVisible:false,
      userTagSelect:{},//所选标签
      formDataCopy: {
        tabActiveName: "1",
        pageMarginTop: 0,
        pageMarginBottom: 0,
        showRule: 1,//显示规则
        location: 1,//定位规则
        height: 65,//高度
        buttonList: [
          {
            showType: 2, //1文字按钮 2图片按钮
            button:{
              type: 1, // 1新页面   2购买按钮  3订单信息  4弹窗显示  5电话号码
              isSystemUrl: false,
              pageUrl: '',
            },

            width: 30,
            imgUrl: '',
          },
          {
            showType: 2,
            button:{
              type: 1, // 1新页面   2购买按钮  3订单信息  4弹窗显示  5电话号码
              isSystemUrl: false,
              pageUrl: '',
            },
            width: 30,
            imgUrl: '',
          },
          {
            showType: 1,
            button: {
              type: 1, // 1新页面   2购买按钮  3订单信息  4弹窗显示  5电话号码
              isSystemUrl: false,
              pageUrl: '',
            },
            width: 40,
            title: "立即购买",
            imgUrl: "",
            fontColor: '#FFFFFF',
            backColor: '#f53f3f',
            fontSize: 15,
            fontSpacing: 0,
            fontWeight: false,
          },
        ],
      },
      formData: {},
      header: {Authorization: 'Bearer ' + store.getters.access_token},
      actionUrl: '',
      uploadIndex: '',
      addFontButtonVisible: true,
      addImgButtonVisible: true
    };
  },
  props: {
    thememobile: {type: Object | Array},
    showData: {
      type: Object,
      default: () => {
      }
    },
    appId: {
      type: String,
    },
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
      clickComIndex: state => state.divpage.clickComIndex,
    })
  },
  mounted() {
    let that = this;
    if (that.IsEmptyObj(that.showData)) {

      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
    } else {
      that.formData = that.showData
    }
    that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
    // that.updateData({
    //   componentsList: that.componentsList
    // })
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    // 删除按钮
    delBtn(index) {
      let that = this;
      that.$confirm('是否删除该按钮?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
        that.updateData({componentsList: that.componentsList});
      }).catch(() => {
      })
    },
    cancel() {
      this.$emit('cancel')
    },
    reset() {
      let that = this;
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      that.componentsList[that.clickComIndex]['data'] = this.formData;
    },
    confirm() {
      this.$emit('confirm', this.formData)
    },
    delete() {
      this.$emit('delete')
    },
    handleClick(tab, event) {
      this.formData.tabActiveName = tab.name;
    },
    addFontButton() {
      let obj = {
        showType: 1,
        button: {
          type: 1, // 1新页面   2购买按钮  3订单信息  4弹窗显示  5电话号码
          isSystemUrl: false,
          pageUrl: '',
        },
        width: 40,
        title: "立即购买",
        imgUrl: "",
        fontColor: '#FFFFFF',
        backColor: '#f53f3f',
        fontSize: 15,
        fontSpacing: 0,
        fontWeight: false,
      };
      this.formData.buttonList.push(obj);
      if( this.formData.buttonList.length>=4){
        this.addFontButtonVisible = false;
        this.addImgButtonVisible = false;
      }else{
        this.addFontButtonVisible = true;
        this.addImgButtonVisible = true;
      }
    },
    addImgButton() {
      this.formData.buttonList.push({
        showType: 2,
        width: 30,
        imgUrl: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
        button: 1,
      });
      if( this.formData.buttonList.length>=4){
        this.addFontButtonVisible = false;
        this.addImgButtonVisible = false;
      }else{
        this.addFontButtonVisible = true;
        this.addImgButtonVisible = true;
      }
    },
    delButton(index) {
      this.formData.buttonList.splice(index, 1);
      if( this.formData.buttonList.length>=4){
        this.addFontButtonVisible = false;
        this.addImgButtonVisible = false;
      }else{
        this.addFontButtonVisible = true;
        this.addImgButtonVisible = true;
      }
    },
    // 拖动的事件等等=======================================================>
    datadragStart(e) {
      let that = this;
      that.updateData({clickComIndex: null})
      that.showSetBlock = false;
    },
    datadragUpdate(e) {
      e.preventDefault();
    },
    datadragEnd(e) {
      // console.log(e, "拖动结束");
    },
    datadragMove(e, originalEve) {
      return (e.draggedContext.element.text !== 'Gold（不可拖动元素）');
    },
    // 拖动的事件等等=======================================================>

    //按钮的图片上传
    handleAvatarSuccess(res, file) {
      this.$set(this.formData.buttonList[this.uploadIndex],"imgUrl",res.link)
      // this.imageUrl = URL.createObjectURL(file.raw);
    },
    //弹窗图片的上传
    handleAvatar2Success(res, file) {
      this.$set(this.formData.buttonList[this.uploadIndex].button,"popupImgUrl",res.link)
      // this.imageUrl = URL.createObjectURL(file.raw);
    },
    beforeAvatarUpload(file,index) {
      const isJPG = file.type === 'image/jpeg';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!');
      }
      this.uploadIndex = index;
      return isJPG && isLt2M;
    },
    openUserTagBox(index) {
      console.log("index",index)
      this.userTagSelectFlag = index;

      this.userTagBoxVisible = true;
    },
    //删除标签
    deleteTag(obj){
      obj.payTag={};
    },
    //转化得到字体颜色
    getFontColor(val){
      if(!val){
        return;
      }
      return "color:" + val;
    },
    ensureTag(obj) {
      console.log("确认标签", obj)

      let tag = {
        id: obj.id,
        name: obj.name,
        fontColor: obj.fontColor,
        backColor: obj.backColor,
      };
      this.formData.buttonList[this.userTagSelectFlag].button.payTag = tag;
      console.log("确认tag", tag)
      console.log("确认buttonList", this.formData)
      this.userTagSelect = obj;
    },
    radioChange(value,index){
      console.log("切换的",value,index)
      let obj = {};
      if(1 == value){
        obj={
          type: value,
          isSystemUrl: false,
          pageUrl: '',
        }
      }else if(2 == value){
        obj={
          type: value,
          payTag: {},
          name:'',
          price:0,
          isSystemUrl: false,
          pageUrl: '',
        }
      }else if(3 == value){
        obj={
          type: value
        }
      }else if(4 == value){
        obj={
          type: value,
          content:'',
          imgUrl:'',
          popupType:1,
        }
      }else if(5 == value){
        obj={
          type: value
        }
      }
      this.formData.buttonList[index].button=obj
    }
  },

  watch: {
    showData: {
      handler(newVal, oldVal) {
        this.formData = newVal ? newVal : this.formData;
      },
      deep: true
    },
    thememobile() {
    },
  }
};
</script>
<style lang='less' scoped>

.el-form-item {
  margin-bottom: 0;
}

.drag-item {
  margin-bottom: 15px;
  border: 1px solid black;
}
.drag_button {
  display: inline;
  float: right;
}
.drag_button:hover {
  cursor: move;
}

.del_button {
  display: inline;
  float: right;
}

//上传的样式
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 50px;
  height: 50px;
  display: block;
}
//上传的样式
</style>
