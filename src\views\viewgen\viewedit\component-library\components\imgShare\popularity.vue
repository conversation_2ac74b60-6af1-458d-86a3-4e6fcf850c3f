<template>
  <div class="popularityComponent"
       :style="{marginBottom: `${setData.pageMarginBottom}px`,
       marginLeft: `${setData.pageMarginLeft}px`,
       marginRight: `${setData.pageMarginRight}px`,
       marginTop: `${setData.pageMarginTop}px`}">
        <div class="flex justify-center align-center" :style="{backgroundColor: setData.backColor,
          borderTopLeftRadius:`${setData.topBorderRadius}px`,
          borderTopRightRadius:`${setData.topBorderRadius}px`,
          borderBottomLeftRadius:`${setData.bottomBorderRadius}px`,
          borderBottomRightRadius:`${setData.bottomBorderRadius}px`}">
<!--          <div class="content" >-->
<!--            <div class="value" :style="{color: setData.fontColor}">20</div>-->
<!--            <div :style="{color: setData.iconColor}" class="cuIcon-appreciate"><p style="display: inline-block" :style="{color: setData.fontColor}">分享数</p></div>-->
<!--          </div>-->
          <div class="content"  :style="{borderLeftColor:setData.borderColor,borderRightColor:setData.borderColor}" style="border-right: 1px solid;">
            <div class="value" :style="{color: setData.fontColor}">20</div>
            <div :style="{color: setData.iconColor}" class="cuIcon-likefill"><p style="display: inline-block" :style="{color: setData.fontColor}">祝福数</p></div>
          </div>
          <div  class="content"  >
            <div class="value" :style="{color: setData.fontColor}">20</div>
            <div :style="{color: setData.iconColor}" class="cuIcon-hotfill"><p style="display: inline-block" :style="{color: setData.fontColor}">人气数值</p></div>
          </div>
          </div>
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';

export default {
  data() {
    return {};
  },
  components: {},
  props: {
    theme: {type: Object | Array},
    setData: {type: Object | Array},
    cId: {type: Number},
    noEditor: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {
  },
  mounted() {
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
  },
  watch: {
    setData(newVal, oldVal) {
    },
    componentsList(newVal, oldVal) {          //添加的时候出发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    }
  },
  beforeDestroy() {
    // this.$root.Bus.$off('addHotSpot')
  }
};
</script>
<style lang='less' scoped>

@import '../../colorui/main.css';
@import '../../colorui/icon.css';

.popularityComponent{
}
.content {
  width: 100%;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 15px;
}
.value {
  padding-bottom: 8px;
  font-size: 24px;
  font-weight: bolder;
}

</style>
