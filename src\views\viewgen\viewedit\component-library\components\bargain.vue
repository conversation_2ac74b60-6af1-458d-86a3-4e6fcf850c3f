<template>
    <div class="pageComponent" :style="{marginBottom: `${setData.pageSpacing}px`}" style="width: 96%;margin:auto;">
      <div class="wrapper-list"
           :style="{backgroundColor: setData.background, }"
           :class="setData.background&&setData.background.indexOf('bg-') != -1 ? setData.background : '' "
           >
        <img v-if="setData.backgroundImg" :src="setData.backgroundImg" style="width: 100%;height:100%;position: absolute;">

        <div class="cu-bar"  style="margin-left: 18px;">
          <div class="shop-selection text-df margin-top-sm margin-bottom" >
            <div class="margin-left-xs" :style="{color: `${setData.titleColor}`}">{{setData.title}}</div>
            <div class="margin-left-xs text-sm" :style="{color: `${setData.subtitleColor}`}">{{setData.subtitle}}</div>
          </div>
          <div class="bg-white round margin-bottom-sm " style="font-size: 6px;padding: 2px 10px;background: rgba(255,255,255,0.71);margin-right: 8px;">更多</div>
        </div>
        <div v-if="setData.goodsList&&setData.goodsList.length > 0" class=" shop-detail" style="overflow-x:scroll;padding: 8px 0;position: relative;display:flex;margin-left: 15px;">
          <div v-for="(item, index) in setData.goodsList" :key="index" style="float: left;margin-right: 8px;">
            <div class="item shadow-warp radius goods-card">
              <div class="img-box">
                <img :src="item.picUrl ? item.picUrl : noPic" mode="aspectFill"  class="card-img">
              </div>
              <div class="text-black margin-top-xs  overflow-2" style="padding: 0 5px;">{{item.name}}</div>
              <div class="  align-center" style="text-align: center;">
                <span class="cu-btn round sm bg-gradual-orange    ">最低{{item.bargainPrice?item.bargainPrice:0}}元</span>
              </div>
            </div>
          </div>
        </div>
      </div>
  </div>
</template>

<script>
  import { mapState , mapGetters , mapMutations , mapActions } from 'vuex';
  import placeholderImg from "../pages/page-components/placeholderImg";


  export default {
    data() {
      return {
        noPic: require('../assets/images/icon/<EMAIL>')
      };
    },
    components: { placeholderImg },
    props: {
      theme : { type: Object | Array },
      setData : { type: Object | Array },
      cId     : { type: Number },
      noEditor: {
        type: Boolean,
        default: false,
      }
    },
    computed: {
      ...mapState({
        componentsList: state => state.divpage.componentsList,
      }),
    },
    created() {
    },
    mounted() {
    },
    methods: {
      ...mapMutations([
        'updateData'
      ]),
    },
    watch:{
      setData(newVal, oldVal){},
      componentsList(newVal, oldVal){          //添加的时候出发（解决没有移动的时候不emit数据）
        let that = this;
        that.updateData({componentsList: that.componentsList})
      }
    },
    beforeDestroy(){
      // this.$root.Bus.$off('addHotSpot')
    }
  };
</script>
<style lang='less' scoped>

  @import '../colorui/main.css';
  @import '../colorui/icon.css';
  .pageComponent {
    position: relative;
    display: block;
    width: 100%;
    background: #ffffff;

    .wrapper-list{
      padding: 0px;
    }
    .goods-item{
      margin: auto !important;
      margin-top: 10px !important;
    }

    .row-img {
      margin-top: 13px;
      margin-left: -10px;
      width: 120px !important;
      height: 120px !important;
      border-radius: 5px
    }
    .card-img {
      width: 100% !important;
      height: 100% !important;
      border-radius: 5px
    }

    .buy{
      padding: 3px 10px 5px 10px;
    }

    .goods-container {
      justify-content: space-between;
      flex-wrap: wrap;
      box-sizing: content-box;
      padding: 10px;
    }

    .goods-box {
      width: 175px;
      height: 265px;
      background-color: #fff;
      overflow: hidden;
      margin-bottom: 10px;
      border-radius: 5px;
      box-shadow: 0 10px 10px #e5e5e5;
    }

    .goods-box .img-box {
      width: 100%;
      height: 175px;
      overflow: hidden;
    }

    .goods-box .img-box image {
      width: 100%;
      height: 265px;
    }
  }

  .overflow-1 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
  .overflow-2 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
</style>
