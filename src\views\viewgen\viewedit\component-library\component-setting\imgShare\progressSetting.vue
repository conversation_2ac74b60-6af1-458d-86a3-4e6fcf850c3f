<template>
  <div class="pageSetting">
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">活动进度</p>
      <div slot="mainContent">
        <el-divider>基础属性</el-divider>
        <el-form ref="form" label-width="100px" :model="formData">
          <el-form-item label="上边界">
            <el-input v-model="formData.pageMarginTop" size="mini" type="number" style="margin-top: 5px"
                      placeholder="与上面元素的间隔">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="下边界">
            <el-input v-model="formData.pageMarginBottom" size="mini" type="number" style="margin-top: 5px"
                      placeholder="与下面元素的间隔">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="左边界">
            <el-input v-model="formData.pageMarginLeft" size="mini" :mini="0" type="number" style="margin-top: 5px"
                      placeholder="组件内部与左边界的间隔">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="右边界">
            <el-input v-model="formData.pageMarginRight" size="mini" :mini="0" type="number" style="margin-top: 5px"
                      placeholder="组件内部与右边界的间隔">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="背景颜色">
            <el-input v-model="formData.backColor" size="small" style="margin-top: 5px">
              <template slot="append">
                <el-color-picker size="mini" v-model="formData.backColor"></el-color-picker>
              </template>
            </el-input>
          </el-form-item>
            <el-divider>内容设置</el-divider>
          <el-form-item  label="内容上距">
            <el-input v-model="formData.contentPaddingTop" size="mini"  :mini="0" type="number" style="margin-top: 5px" placeholder="">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item  label="内容下距">
            <el-input v-model="formData.contentPaddingBottom" size="mini"  :mini="0" type="number" style="margin-top: 5px" placeholder="">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item  label="内容左距">
            <el-input v-model="formData.contentPaddingLeft" size="mini"  :mini="0" type="number" style="margin-top: 5px" placeholder="">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item  label="内容右距">
            <el-input v-model="formData.contentPaddingRight" size="mini"  :mini="0" type="number" style="margin-top: 5px" placeholder="">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="尺寸大小">
            <el-slider v-model="formData.size" :min="16" :max="40"></el-slider>
          </el-form-item>
          <el-form-item label="进度颜色">
            <el-input v-model="formData.color" size="small" style="margin-top: 5px">
              <template slot="append">
                <el-color-picker size="mini" v-model="formData.color"></el-color-picker>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="百分数显示">
            <el-switch
              v-model="formData.percentFlag"
              active-text="显示"
              inactive-text="不显示">
            </el-switch>
          </el-form-item>
<!--          <el-form-item label="圆角设置">-->
<!--            <el-slider v-model="formData.borderRadius" :max="40"></el-slider>-->
<!--          </el-form-item>-->
        </el-form>
      </div>
    </settingSlot>
    <!--    <p style="display:none">{{getData}}</p>-->
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';

import settingSlot from '../settingSlot'
// import bgColorSelect from "../../pages/page-components/bgColorSelect";

export default {
  components: {settingSlot},
  data() {
    return {
      formDataCopy: {
        pageMarginTop: 0,
        pageMarginBottom: 0,
        pageMarginLeft: 0,
        pageMarginRight: 0,
        contentPaddingTop:0,
        contentPaddingBottom:0,
        contentPaddingLeft:0,
        contentPaddingRight:0,
        size: 18,
        color: "#409EFF",//进度颜色
        backColor: "#ffffff",//背景颜色
        percentFlag:true,
      },
      formData: {}
    };
  },
  props: {
    thememobile: {type: Object | Array},
    showData: {
      type: Object,
      default: () => {
      }
    },
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
      clickComIndex: state => state.divpage.clickComIndex,
    })
  },
  mounted() {
    let that = this;
    if (that.IsEmptyObj(that.showData)) {

      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
    } else {
      that.formData = that.showData
    }
    console.log("111  ", this.formData)
    that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
    // that.updateData({
    //   componentsList: that.componentsList
    // })
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    // 删除按钮
    delBtn(index) {
      let that = this;
      that.$confirm('是否删除该按钮?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
        that.updateData({componentsList: that.componentsList});
      }).catch(() => {
      })
    },
    cancel() {
      this.$emit('cancel')
    },
    reset() {
      let that = this;
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      that.componentsList[that.clickComIndex]['data'] = this.formData;
    },
    confirm() {
      this.$emit('confirm', this.formData)
    },
    delete() {
      this.$emit('delete')
    },
    handleClick(tab, event) {
      this.formData.tabActiveName = tab.name;
    }
  },

  watch: {
    showData: {
      handler(newVal, oldVal) {
        this.formData = newVal ? newVal : this.formData;
      },
      deep: true
    },
    thememobile() {
    },
  }
};
</script>
<style lang='less' scoped>

.el-form-item {
  margin-bottom: 0;
}
</style>
