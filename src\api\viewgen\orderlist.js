import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/orderinfo/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/weixin/orderinfo',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/weixin/orderinfo/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/orderinfo/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/orderinfo',
    method: 'put',
    data: obj
  })
}

export function batchUpdateStatus(obj) {
  return request({
    url: '/weixin/orderinfo/batch-update-status',
    method: 'put',
    data: obj
  })
}
