import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/wxgoodsspec/page',
    method: 'get',
    params: query
  })
}

export function getList(query) {
  return request({
    url: '/weixin/wxgoodsspec/list',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/weixin/wxgoodsspec',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/weixin/wxgoodsspec/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/wxgoodsspec/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/wxgoodsspec',
    method: 'put',
    data: obj
  })
}
