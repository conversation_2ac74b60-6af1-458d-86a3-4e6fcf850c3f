export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  addBtn:false,
  excelBtn: false,
  printBtn: false,
  viewBtn: false,
  refreshBtn: false,
  columnBtn: false,//列的显隐按钮
  searchMenuSpan: 6,
  column: [
    {
      label: '主键',
      prop: 'id',
      hide:true,
    },
    {
      label: '操作时间节点',
      prop: 'handleTime',
      sortable: true,
      width: 200,
    },
    {
      label: '操作时长',
      prop: 'duration',
      sortable: true,
      rules: [
      ]
    },
    {
      label: '进入数量',
      prop: 'enterNum',
      sortable: true,
      rules: [
      ]
    },
    {
      label: '支付数量',
      prop: 'payNum',
      sortable: true,
      rules: [
      ]
    },
  ]
}
