import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/auctioncalendar/page',
    method: 'get',
    params: query
  })
}

/**
 * 拿某一天时间
 * @param query
 * @returns {*}
 */
export function getDayByCon(query) {
  return request({
    url: '/weixin/auctioncalendar/oneday',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/weixin/auctioncalendar',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/weixin/auctioncalendar/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/auctioncalendar/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/auctioncalendar',
    method: 'put',
    data: obj
  })
}

/**
 *  拿取当前月份的日历时间
 * @param obj
 * @returns {*}
 */
export function getMonth(query) {
  return request({
    url: '/weixin/auctioncalendar/month',
    method: 'get',
    params: query
  })
}
