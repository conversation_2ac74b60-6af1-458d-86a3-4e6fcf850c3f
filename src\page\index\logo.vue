<template>
  <div class="avue-logo">
    <transition name="fade">
      <span v-if="keyCollapse" key="0">
        <img class="avue-logo-image-1" :src="tenantInfo.logo" style="">
      </span>
    </transition>
    <transition-group name="fade" >
      <template v-if="!keyCollapse">
        <el-row class="avue-logo-title" key="1">
          <el-col :span="24">
            <img class="avue-logo-image-2" :src="tenantInfo.logo">
          </el-col>
          <el-col :span="24">
            <span class="logo-title" @click="goHomeUrl">{{tenantInfo.name}}</span>
            <span class="logo-remark" v-if="tenantInfo.id != '-1'">{{shopInfo ? shopInfo.name : tenantInfo.homeUrl}}</span>
          </el-col>
        </el-row>
      </template>
    </transition-group>
    <div v-if="userInfo.type == '-1'" class="tenant-select">
      <el-select v-model="switchTenantId" placeholder="请选择" size="mini" @change="onChangeTenant">
        <el-option class=""
          v-for="item in tenantListMy"
          :key="item.id"
          :label="item.name"
          :value="item.id">
        </el-option>
      </el-select>
    </div>
  </div>
</template>

<script>
  import {mapGetters, mapState} from "vuex";
  import {getObj as getTenant, getListMy as getTenantListMy} from '@/api/upms/tenant'
  import {getObj as getShopInfo} from '@/api/mall/shopinfo'
  import { getStore, setStore } from '@/util/store'

  export default {
    name: "logo",
    data() {
      return {
        tenantInfo: '',
        shopInfo: '',
        tenantListMy: [],
        switchTenantId: getStore({ name: 'switchTenantId' })
      };
    },
    created() {
      this.getTenant()
      if(this.userInfo.type == '-1'){
        this.getTenantListMy()
      }
    },
    computed: {
      ...mapGetters(["keyCollapse", "tagWel"]),
      ...mapState({
        userInfo: state => state.user.userInfo
      }),
    },
    methods: {
      goHomeUrl(){
        window.open(this.tenantInfo.homeUrl, '_blank')
      },
      getTenant(){
        getTenant(this.userInfo.tenantId).then(response =>  {
          this.tenantInfo = response.data.data
          if(this.userInfo.type == '2'){
            getShopInfo(this.userInfo.shopId).then(response =>  {
              this.shopInfo = response.data.data
            })
          }
        })
      },
      getTenantListMy(){
        getTenantListMy().then(response =>  {
          let tenantListMy = response.data.data
          this.tenantListMy = tenantListMy
          let storeSwitchTenantId = getStore({ name: 'switchTenantId' })
          if(tenantListMy.length > 0
            && (!storeSwitchTenantId || tenantListMy.findIndex(item => item.id === storeSwitchTenantId) == -1)){
            //没有切换租户或没有绑定该切换租户，则默认切换到第一个租户
            setStore({
              name: 'switchTenantId',
              content: tenantListMy[0].id
            })
            this.switchTenantId = tenantListMy[0].id
            setTimeout(()=>{
              this.$router.go(0)
            },500);
          }
        })
      },
      onChangeTenant(tenantId){
        if(tenantId){
          setStore({
            name: 'switchTenantId',
            content: tenantId
          })
          this.$message({
            showClose: true,
            message: '租户切换成功',
            type: 'success'
          })
          this.$store.commit("DEL_ALL_TAG")
          this.$router.push({
            path: this.$router.$avueRouter.getPath({
              src: this.tagWel.value
            }),
            query: this.tagWel.query
          });
          setTimeout(()=>{
            this.$router.go(0)
          },1000);
        }
      },
    }
  };
</script>

<style scoped lang="scss">
  .fade-leave-active {
    transition: opacity 0.2s;
  }

  .fade-enter-active {
    transition: opacity 2.5s;
  }

  .fade-enter,
  .fade-leave-to {
    opacity: 0;
  }

  .avue-logo {
    position: fixed;
    top: 10px;
    left: 0;
    width: 220px;
    height: 120px;
    line-height: 60px;
    background-color: #20222a;
    overflow: hidden;
    color: rgba(255, 255, 255, 0.8);
    z-index: 1024;
    line-height: unset;
  }

  .avue-logo-image-1{
    width: 45px;
    height: 45px;
    display: flex;
    margin:20px auto;
  }

  .avue-logo-image-2{
    width: 45px;
    height: 45px;
    display: flex;
    margin: auto;
  }

  .logo-title{
    font-size: 16px;
    margin-top: 8px;
    color: #fff;
    display: flex;
    justify-content: center;
  }

  .logo-remark{
    justify-content: center;
    margin-top: 8px;
    display: flex;
    font-size: 12px;
  }

  .tenant-select{
    display: flex;
    justify-content: center;
    margin-top: 8px;
  }
</style>
