<!-- 商品分类组件 -->
<template>
    <div class="pageComponent" :style="{ backgroundColor: setData.background }">
      <div class="flex" :style="{backgroundColor: setData.background}"
           :class="setData.background && setData.background.indexOf('bg-') != -1 ? setData.background : '' ">
        <div class="flex-sub" style="padding: 5px;">
          <div class="cu-item radius" :style="{backgroundColor: setData.themeColor}"  :class="setData.themeColor&&setData.themeColor.indexOf('bg-') != -1 ? setData.themeColor : '' " >
            <div class="flex  text-white  electronic-coupons">
              <div class="flex-twice  shadow-blur radius t1-r " >
                <div class="margin-top-xs  text-sm text-center overflow-1"><span class="cuIcon-shop" style="margin-right: 3px;"></span>华为旗舰店</div>
                <div style="text-align: center;">
                  <span class="text-price text-xl "></span>
                  <span class="number ">50</span>
                </div>
                <div class="text-center">
                  <div style="font-size: 8px!important;font-weight: 300">满300元可用</div>
                </div>
              </div>
              <div class="flex-sub  shadow-blur radius text-center t1-l">
                <div class="text-xs margin-top-sm">代金券</div>
                <div class=" bg-white round sm margin-top-sm " style="font-size: 10px;margin: 10px 5px;color:red;">领取</div>
              </div>
            </div>
          </div>
        </div>
        <div class="flex-sub " style="padding: 5px 0;">
          <div class="cu-item radius" :style="{backgroundColor: setData.themeColor}"  :class="setData.themeColor&&setData.themeColor.indexOf('bg-') != -1 ? setData.themeColor : ''">
            <div class="flex radius text-white  electronic-coupons">
              <div class="flex-twice  shadow-blur radius t1-r">
                <div class="margin-top-xs  text-sm text-center overflow-1"><span class="cuIcon-shop" style="margin-right: 3px;"></span>苹果上海官方旗舰店分店</div>
                <div style="text-align: center;">
                  <span class="text-price text-xl "></span>
                  <span class="number ">2000</span>
                </div>
                <div class="text-center">
                  <div style="font-size: 8px!important;font-weight: 300">满10000元可用</div>
                </div>
              </div>
              <div class="flex-sub  shadow-blur radius text-center t1-l">
                <div class="text-xs margin-top-sm">代金券</div>
                <div class=" bg-gray round sm margin-top-sm text-gray" style="font-size: 10px;margin: 10px 5px;">已领取</div>
              </div>
            </div>
          </div>
        </div>
        <div style="writing-mode:vertical-rl;border: red solid 1px;margin:5px;text-align: center;color: red;border-radius: 4px;font-size: 12px;">
          领取更多
        </div>
      </div>
    </div>
</template>

<script>
import { mapState , mapGetters , mapMutations , mapActions } from 'vuex';

export default {
    data() {
        return {};
    },
    components: {  },
    props: {
        thememobile : { type: Object | Array },
        setData : { type: Object | Array },
        cId     : { type: Number }
    },
    computed: {
        ...mapState({
            componentsList: state => state.divpage.componentsList,
        }),
    },
    created() {},
    mounted() {},
    methods: {
        ...mapMutations([
            'updateData'
        ]),
    },
    watch:{
        componentsList(newVal, oldVal){          //添加的时候触发（解决没有移动的时候不emit数据）
            let that = this;
            that.updateData({componentsList: that.componentsList})
        }
    }
};
</script>
<style lang='less' scoped>

  @import '../colorui/main.css';
  @import '../colorui/icon.css';
  .pageComponent{
    .electronic-coupons {
      height: 70px;
    }

    .t1-r {
      background-size: 100% 60%;
      background-repeat: no-repeat;
    }

    .t1-l {
      background-size: 100% 60%;
      background-repeat: no-repeat;
      border-left: 1px dashed rgba(255, 255, 255, .3);
    }

    .store{
      margin-top: -10px;
    }

    .number{
      font-size: 18px;
    }

    .t2-r {
      background: radial-gradient(circle at top right, transparent 5px, #39b54a 0) top right, radial-gradient(circle at bottom right, transparent 5px, #39b54a 0) bottom right;
      background-size: 100% 60%;
      background-repeat: no-repeat;
    }

    .t2-l {
      background: radial-gradient(circle at top left, transparent 5px, #39b54a 0) top left, radial-gradient(circle at bottom left, transparent 5px, #39b54a 0) bottom left;
      background-size: 100% 60%;
      background-repeat: no-repeat;
      border-left: 1px dashed rgba(255, 255, 255, .3);
    }

    .overflow-1 {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
    }
  }
</style>
