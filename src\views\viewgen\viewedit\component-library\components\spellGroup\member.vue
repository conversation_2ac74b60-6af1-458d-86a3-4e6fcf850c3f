<template>
  <div class="memberComponent"
       :style="{marginBottom: `${setData.pageMarginBottom}px`,marginTop: `${setData.pageMarginTop}px`}">
    <div class="cu-item " >
      <div v-if="setData.tabActiveName =='1' || setData.tabActiveName =='2'" class="content  text-center">
        <div>
          <div class="cu-avatar round bg-img lg margin-xs text-yellow groupon-user"
               style="background-image:url('/img/spell-group/yiwen.png');height: 80px;width: 80px;">
          </div>
          <p>等待加入</p>
        </div>
        <div>
          <div class="cu-avatar round bg-img lg margin-xs text-yellow groupon-user"
               style="background-image:url('/img/spell-group/yiwen.png');height: 80px;width: 80px;">
          </div>
          <p>等待加入</p>
        </div>
      </div>

      <div class="captain_description">
        <p v-if="setData.tabActiveName =='1'">{{ setData.emptyMsg }}</p>
        <p v-if="setData.tabActiveName =='2'">{{ setData.fullMsg }}</p>
        <p v-if="setData.tabActiveName =='3'">{{ setData.soloMsg }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';


export default {
  data() {
    return {};
  },
  components: {},
  props: {
    theme: {type: Object | Array},
    setData: {type: Object | Array},
    cId: {type: Number},
    noEditor: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {
  },
  mounted() {
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
  },
  watch: {
    setData(newVal, oldVal) {
    },
    componentsList(newVal, oldVal) {          //添加的时候出发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    }
  },
  beforeDestroy() {
    // this.$root.Bus.$off('addHotSpot')
  }
};
</script>
<style lang='less' scoped>

@import '../../colorui/main.css';
@import '../../colorui/icon.css';

.content {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.captain_description {
  display: flex;
  padding-top: 15px;
  padding-bottom: 15px;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.captain_button {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
