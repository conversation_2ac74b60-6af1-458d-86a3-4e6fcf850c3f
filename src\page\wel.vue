<template>
  <div>
    <basic-container class="wel-view">
      <!--<iframe src="http://www.gocreateone.com" class="iframe-home"></iframe>-->
      <el-card class="box-card">
        <div class="terminal">
          <span style="height:30px;line-height: 30px; ">演示商城端访问：</span>
          <a class="terminal-list" type="primary" @click="showMaCode()">
            <img class="terminal-img"
              src="../views/mall/pagedevise/component-library/assets/images/icon-2/xiaochengxu.png">
            <a>微信小程序码</a>
          </a>
          <a class="terminal-list" type="primary" @click="showWxH5Code()">
            <img class="terminal-img"
              src="../views/mall/pagedevise/component-library/assets/images/icon-2/gongzhonghao.png">
            <a>微信公众号页面</a>
          </a>
          <a class="terminal-list" type="primary" @click="showH5Url()">
            <img class="terminal-img"
              src="../views/mall/pagedevise/component-library/assets/images/icon-2/shoujiliulanqi.png">
            <a>手机普通浏览器页面</a>
          </a>
          <a class="terminal-list" type="primary" @click="showPCUrl()">
            <img class="terminal-img"
              src="../views/mall/pagedevise/component-library/assets/images/icon-2/liulanqi.png">
            <a>PC端页面</a>
          </a>
          <a class="terminal-list" type="primary" @click="showAppUrl()">
            <img class="terminal-img"
              src="../views/mall/pagedevise/component-library/assets/images/icon-2/APP.png">
            <a>APP</a>
          </a>
        </div>
      </el-card>
      <el-card class="box-card">
        <avue-data-tabs :option="option1"></avue-data-tabs>
        <el-row style="margin-top: 20px;">
          <el-col :span="4">
            <div id="statisticsAppTypeChart" style="float:left;width:100%;height: 300px"></div>
          </el-col>
          <el-col :span="4">
            <div id="statisticsSexChart" style="float:left;width:100%;height: 300px"></div>
          </el-col>
          <el-col :span="4">
            <div id="statisticsAppTypeChart2" style="float:left;width:100%;height: 300px"></div>
          </el-col>
          <el-col :span="4">
            <div id="statisticsOrderTypeChart" style="float:left;width:100%;height: 300px"></div>
          </el-col>
          <el-col :span="8">
            <div id="statisticsProChart" style="width:100%;height: 300px"></div>
          </el-col>
        </el-row>
      </el-card>
      <el-card class="box-card" v-if="userInfo.type == '1' || userInfo.type == '-1'">
        <div class="data-view-content">
          <el-row :span="24" :gutter="10">
            <el-col :xs="24" :sm="24" :md="3" v-if="treeWxAppData.length > 1">
              <el-card shadow="never">
                <div slot="header">
                  <span>公众号名称</span>
                </div>
                <el-input placeholder="输入关键字进行过滤" size="mini" v-model="filterText">
                </el-input>
                <el-tree style="margin-top: 5px" :data="treeWxAppData" :props="treeWxAppProps"
                  :filter-node-method="filterNode" node-key="id" default-expand-all ref="tree" @node-click="nodeClick">
                </el-tree>
              </el-card>
            </el-col>
            <el-col :xs="24" :sm="24" :md="21">
              <div class="change-layout">
                <a href="javascript:;" class="change-item" @click="handleChangeDataViewFilter({ value: '', name: ''})"
                  :class="currentDataViewFilter === '' ? 'active' : '' ">
                  全部
                </a>
                <a href="javascript:;" class="change-item" v-for="item in dataViewFilterDict" :key="item.value"
                  @click="handleChangeDataViewFilter(item)"
                  :class="currentDataViewFilter === item.value ? 'active' : '' ">
                  {{ item.label }}
                </a>
              </div>
              <div class="wel-data-view" id="wel-data-view" ref="welDataView" style="width: 100%; height: 400px;">
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-alert title="Powered by www.gocreateone.com" type="info" :closable="false" center show-icon>
      </el-alert>
      <el-dialog :title="codeType == '1' ? '选择小程序' : '选择公众号'" :visible.sync="dialogWxApp" width="30%">
        <el-select v-model="wxApp.id" placeholder="请选择">
          <el-option v-for="item in wxAppData" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogWxApp = false">取 消</el-button>
          <el-button type="primary" @click="getMaCode" v-if="codeType == '1'">确认</el-button>
          <el-button type="primary" @click="getQrCode" v-if="codeType == '2'">确认</el-button>
        </span>
      </el-dialog>
      <el-dialog :title="codeType == '1' ? '小程序码，微信扫码进入该商城首页' : '公众号H5二维码，微信扫码进入该商城首页'" :visible.sync="dialogQrCode"
        center width="60%">
        <el-row v-if="codeType == '2'">
          <el-col :span="8">
            <vue-qr :text="qrCodeScene" :size="160" :dotScale=1></vue-qr>
            <p>160x160</p>
          </el-col>
          <el-col :span="8">
            <vue-qr :text="qrCodeScene" :size="200" :dotScale=1></vue-qr>
            <p>200x200</p>
          </el-col>
          <el-col :span="8">
            <vue-qr :text="qrCodeScene" :size="320" :dotScale=1></vue-qr>
            <p>320x320</p>
          </el-col>
        </el-row>
        <el-row v-if="codeType == '2'">
          可直接有微信中打开此链接：{{qrCodeScene}}
        </el-row>
        <div v-if="codeType == '1'" style="text-align: center">
          <img :src="'data:image/jpg;base64,'+qrCodeScene" />
        </div>
      </el-dialog>
    </basic-container>
  </div>
</template>

<script>


  import * as echarts from "echarts";
  // import chinaMap from "echarts/map/json/china.json";  //中国地图
  import chinaMap from "@/util/china.json";  //中国地图
  import {mapGetters} from 'vuex'
  import {getUserSummary,getUserCumulate} from '@/api/wxmp/wxsummary'
  import {getStatistics as getStatisticsGoodsSpu} from '@/api/mall/goodsspu'
  import {getStatistics as getStatisticsOrderInfo, getStatisticsByColumn as getStatisticsByColumnOrderInfo} from '@/api/mall/orderinfo'
  import {getStatistics as getStatisticsUserInfo, getStatisticsByColumn as getStatisticsByColumnUserInfo} from '@/api/mall/userinfo'
  import {getStatistics as getStatisticsShopUser} from '@/api/mall/usershop'
  import {getList as getWxAppList, getObj as getWxApp} from '@/api/wxmp/wxapp'
  import {getMaCode} from '@/api/wxma/wxapp'
  import {h5HostMobile, h5HostPC} from '@/config/env.js'
  import { getStore } from '@/util/store'

  export default {
    name: 'wel',
    data() {
      return {
        option1: {
          data: [{
              click: function(item) {
                // alert(JSON.stringify(item))
              },
              title: '新增商品',
              subtitle: '今天',
              count: 0,
              allcount: 0,
              text: '当前商品总数量',
              color: 'rgb(27, 201, 142)',
              key: '商'
            },
            {
              click: function(item) {
                // alert(JSON.stringify(item));
              },
              title: '新增用户',
              subtitle: '今天',
              count: 0,
              allcount: 0,
              text: '当前用户总数量',
              color: 'rgb(230, 71, 88)',
              key: '用'
            },
            {
              click: function(item) {
                // alert(JSON.stringify(item));
              },
              title: '新增订单',
              subtitle: '今天',
              count: 0,
              allcount: 0,
              text: '已付款订单总数量',
              color: 'rgb(178, 159, 255)',
              key: '订'
            }
          ]
        },
        filterText: '',
        treeWxAppProps: {
          label: 'name',
          value: 'id'
        },
        treeWxAppData: [],
        appId: null,
        startDate: this.$moment().add(-7, 'days').format('YYYY-MM-DD'),
        endDate: this.$moment().add(-1, 'days').format('YYYY-MM-DD'),
        currentDataViewFilter: "",
        dataViewFilterDict: [
          {
          label: "新增人数",
          value: "1",
          name: "plus"
        }, {
          label: "取消关注人数",
          value: "2",
          name: "reduce"
        }, {
          label: "净增人数",
          value: "3",
          name: "netGrowth"
        }, {
          label: "累计人数",
          value: "4",
          name: "count"
        }],
        xAxisData: [],
        selected: {
          '新增人数': true,
          '取消关注人数': true,
          '净增人数': true,
          '累计人数': true,
        },
        myChart: null,
        wxAppData: [],
        dialogWxApp: false,
        wxApp: {},
        h5HostMobile: h5HostMobile,
        h5HostPC: h5HostPC,
        dialogQrCode: false,
        qrCodeScene: '',
        codeType: '',
      }
    },
    computed: {
      ...mapGetters(['website', 'userInfo'])
    },
    watch: {
      filterText(val) {
        this.$refs.tree.filter(val)
      }
    },
    created() {
      this.getMallSummary()
      if(this.userInfo.type == '1' || this.userInfo.type == '-1'){
        this.getWxAppList()
      }
      // this.$notify({
      //   title: '购买需知',
      //   dangerouslyUseHTMLString: true,
      //   duration: 0,
      //   message: '<div>' +
      //     '<ul>' +
      //     '<li style="font-size: 16px">' +
      //     '* <a href="https://www.gocreateone.com/uniappddb.html" target="_blank" style="text-decoration: underline;color: green">官网</a>' +
      //     '为购买源码的唯一渠道，私聊或其他渠道都为<span style="color: red">盗版！</span>' +
      //     '</li>' +
      //     '<li>* 盗版无法取得官方授权是不能合法使用的，支持正版不要给自己或公司带来不必要的法律纠纷！</li>' +
      //     '</ul>' +
      //     '</div>'
      // })
      var userAgent = navigator.userAgent;
      let isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1;
      let isSafari = userAgent.indexOf("Chrome") == -1 && userAgent.indexOf("Safari") > -1;
      if (isIE || isSafari) {
        setTimeout(() => {
          this.$notify({
            title: '浏览器兼容',
            message: '请使用谷歌、火狐、360极速等主流浏览器进行操作，否则页面可能出现未知错误！',
            duration: 0,
            type: 'warning'
          });
        }, 500)
      }
    },
    methods: {
      filterNode(value, data) {
        if (!value) return true
        return data.name.indexOf(value) !== -1
      },
      //加载公众号列表
      getWxAppList() {
        getWxAppList({
          appType: '2'
        }).then(response => {
          let data = response.data
          this.treeWxAppData = data
          if (data && data.length > 0) {
            //默认加载第一个公众号的素材
            this.nodeClick({
              id: data[0].id
            })
          }
        }).catch(() => {

        })
      },
      nodeClick(data) {
        if (this.appId != data.id) {
          this.$nextTick(() => {
            if (this.$refs.tree) {
              this.$refs.tree.setCurrentKey(data.id)
            }
          })
          this.appId = data.id
          this.getSummary()
        }else{
          this.getSummary()
        }
      },
      getSummary() {
        this.xAxisData = []
        let days = this.$moment(this.endDate).diff(this.$moment(this.startDate), 'day') //相差天数
        for (let i = 0; i <= days; i++) {
          this.xAxisData.push({
            date: this.$moment(this.startDate).add(i, 'days').format('YYYY-MM-DD'),
            plus: 0,
            reduce: 0,
            netGrowth: 0,
            count: 0
          })
        }
        getUserSummary({
          appId: this.appId,
          startDate: this.startDate,
          endDate: this.endDate
        }).then(response => {
          let xAxisData = this.xAxisData
          let data = response.data.data?response.data.data:[]
          xAxisData.forEach((item, index, arr) => {
            data.forEach((item2, index2, arr2) => {
              if (item2.refDate.indexOf(item.date) >= 0) {
                item.plus = item2.newUser
                item.reduce = item2.cancelUser
                item.netGrowth = item2.newUser - item2.cancelUser
              }
            })
          })
          this.xAxisData = xAxisData
          getUserCumulate({
            appId: this.appId,
            startDate: this.startDate,
            endDate: this.endDate
          }).then(response => {
            let xAxisData2 = this.xAxisData
            let data2 = response.data.data?response.data.data:[]
            xAxisData2.forEach((item2, index, arr) => {
              data2.forEach((item3, index2, arr2) => {
                if (item3.refDate.indexOf(item2.date) >= 0) {
                  item2.count = item3.cumulateUser
                }
              })
            })
            this.xAxisData = xAxisData
            this.myChart = echarts.init(this.$refs['welDataView'])
            this.handleDrawViews()
          }).catch(() => {})
        }).catch(() => {})
      },
      handleChangeDataViewFilter({
        value,
        name
      }) {
        this.currentDataViewFilter = value
        switch (value) {
          case '1':
            this.selected = {
              '新增人数': true,
              '取消关注人数': false,
              '净增人数': false,
              '累计人数': false,
            }
            break;
          case '2':
            this.selected = {
              '新增人数': false,
              '取消关注人数': true,
              '净增人数': false,
              '累计人数': false,
            }
            break;
          case '3':
            this.selected = {
              '新增人数': false,
              '取消关注人数': false,
              '净增人数': true,
              '累计人数': false,
            }
            break;
          case '4':
            this.selected = {
              '新增人数': false,
              '取消关注人数': false,
              '净增人数': false,
              '累计人数': true,
            }
            break;
          default:
            this.selected = {
              '新增人数': true,
              '取消关注人数': true,
              '净增人数': true,
              '累计人数': true,
            }
            break;
        }
        this.handleDrawViews()
      },
      handleDrawViews() {
        if (this.myChart) {
          this.myChart.setOption({
            tooltip: {},
            legend: {
              show: true,
              selected: this.selected
            },
            xAxis: {
              data: this.xAxisData.map(item => item.date)
            },
            yAxis: [{
              type: 'value',
              interval: 5,
              min: 0,
              axisLine: {
                show: false
              },
              splitLine: {
                lineStyle: {
                  color: 'rgba(0, 204, 153, 0.5)'
                }
              },
              axisTick: {
                show: false,
              }
            }, {
              type: 'value',
              interval: 100,
              min: 0,
              axisLine: {
                show: false,
              },
              splitLine: {
                show: false
              },
              axisTick: {
                show: false,
              }
            }],
            series: [{
              name: '新增人数',
              type: 'line',
              data: this.xAxisData.map(item => item.plus),
              symbol: 'circle',
              symbolSize: 5,
              yAxisIndex: 0,
              lineStyle: {
                color: "#44B549"
              },
              itemStyle: {
                color: "#44B549",
                borderColor: "#44B549"
              },
              label: {
                show: true
              }
            }, {
              name: '取消关注人数',
              type: 'line',
              data: this.xAxisData.map(item => item.reduce),
              yAxisIndex: 0,
              lineStyle: {
                color: "#FF6633"
              },
              itemStyle: {
                color: "#FF6633",
                borderColor: "#FF6633"
              },
              label: {
                show: true
              }
            }, {
              name: '净增人数',
              type: 'line',
              data: this.xAxisData.map(item => item.netGrowth),
              yAxisIndex: 0,
              lineStyle: {
                color: "#00CC99"
              },
              itemStyle: {
                color: "#00CC99",
                borderColor: "#00CC99"
              },
              label: {
                show: true
              }
            }, {
              name: '累计人数',
              type: 'bar',
              data: this.xAxisData.map(item => item.count),
              barWidth: 30,
              yAxisIndex: 1,
              lineStyle: {
                color: "#33CCFF"
              },
              itemStyle: {
                color: "#33CCFF",
                borderColor: "#33CCFF"
              },
              label: {
                show: true,
                position: 'top'
              }
            }]
          });
        }
      },
      getMallSummary() {
        getStatisticsGoodsSpu().then(response => {
          let data = response.data.data
          this.option1.data[0].count = data.countToday
          this.option1.data[0].allcount = data.countTotal
        }).catch(() => {

        })
        if(this.userInfo.type == '1' || this.userInfo.type == '-1'){
          getStatisticsUserInfo().then(response => {
            let data = response.data.data
            this.option1.data[1].count = data.countToday
            this.option1.data[1].allcount = data.countTotal
          }).catch(() => {

          })
          getStatisticsByColumnUserInfo('app_type',{}).then(response => {
            let data = response.data.data
            data = data.map(item=>({
              name: item.name == 'MA' ? '小程序' : item.name == 'H5-WX' ? '公众号H5' : item.name == 'H5-PC' ? 'PC端H5' : item.name == 'H5' ? '移动端H5' : item.name,
              value: item.value
            }))
            // 基于准备好的dom，初始化echarts实例
            let statisticsAppTypeChart = echarts.init(document.getElementById('statisticsAppTypeChart'))
            // 绘制图表
            statisticsAppTypeChart.setOption({
              title : {
                text: '用户来源端比例',//主标题
                x:'center',
                textStyle:{
                  fontSize :'15'
                },
              },
              series: [
                {
                  name:'用户来源端比例',
                  type:'pie',
                  radius : '50%',
                  data: data,
                  itemStyle: {
                    emphasis: {
                      shadowBlur: 10,
                      shadowOffsetX: 0,
                      shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                  }
                }
              ]
            })
          }).catch(() => {

          })
          getStatisticsByColumnUserInfo('sex',{}).then(response => {
            let data = response.data.data
            data = data.map(item=>({
              name: item.name == '0' ? '未知' : item.name == '1' ? '男' : item.name == '2' ? '女' : !item.name ? '未设置' : item.name,
              value: item.value
            }))
            // 基于准备好的dom，初始化echarts实例
            let statisticsSexChart = echarts.init(document.getElementById('statisticsSexChart'))
            // 绘制图表
            statisticsSexChart.setOption({
              title : {
                text: '用户性别比例',//主标题
                x:'center',
                textStyle:{
                  fontSize :'15'
                },
              },
              series: [
                {
                  name:'用户性别比例',
                  type:'pie',
                  radius : '50%',
                  data: data,
                  itemStyle: {
                    emphasis: {
                      shadowBlur: 10,
                      shadowOffsetX: 0,
                      shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                  }
                }
              ]
            })
          }).catch(() => {

          })
          getStatisticsByColumnUserInfo('province',{}).then(async response => {
            echarts.registerMap("china", chinaMap);
            let data = response.data.data
            data = data.map(item=>({
              name: item.name ? item.name.replace('省','').replace('市','') : '未知',
              value: item.value
            }))
            // 基于准备好的dom，初始化echarts实例
            let statisticsProChart = echarts.init(document.getElementById('statisticsProChart'))
            // 绘制图表
            statisticsProChart.setOption({
              title : {
                text: '用户地区分布',//主标题
                x:'center',
                textStyle:{
                  fontSize :'15'
                },
              },
              dataRange: {
                x: 'right',
                min: 0,
                max: 1000,
                text: ['高', '低'],
                splitNumber: 0,
              },
              tooltip: {
                trigger: 'item',
              },
              series: [
                {
                  name:'用户地区分布',
                  type: 'map',
                  roam: false,
                  map: 'china',
                  selectedMode: 'multiple',
                  emphasis: {
                    label: {
                      show: true,
                    },
                  },

                  data: data,

                }
              ]
            })
          }).catch(() => {

          })
          getStatisticsByColumnOrderInfo('app_type',{
            isPay: '1'
          }).then(response => {
            let data = response.data.data
            data = data.map(item=>({
              name: item.name == 'MA' ? '小程序' : item.name == 'H5-WX' ? '公众号H5' : item.name == 'H5-PC' ? 'PC端H5' : item.name == 'H5' ? '移动端H5' : item.name,
              value: item.value
            }))
            // 基于准备好的dom，初始化echarts实例
            let statisticsAppTypeChart = echarts.init(document.getElementById('statisticsAppTypeChart2'))
            // 绘制图表
            statisticsAppTypeChart.setOption({
              title : {
                text: '订单来源端比例',//主标题
                x:'center',
                textStyle:{
                  fontSize :'15'
                },
              },
              series: [
                {
                  name:'订单来源端比例',
                  type:'pie',
                  radius : '50%',
                  data: data,
                  itemStyle: {
                    emphasis: {
                      shadowBlur: 10,
                      shadowOffsetX: 0,
                      shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                  }
                }
              ]
            })
          }).catch(() => {

          })
          getStatisticsByColumnOrderInfo('order_type',{
            isPay: '1'
          }).then(response => {
            let data = response.data.data
            data = data.map(item=>({
              name: item.name == '0' ? '普通订单' : item.name == '1' ? '砍价订单' : item.name == '2' ? '拼团订单' : item.name == '3' ? '秒杀订单' : item.name,
              value: item.value
            }))
            // 基于准备好的dom，初始化echarts实例
            let statisticsAppTypeChart = echarts.init(document.getElementById('statisticsOrderTypeChart'))
            // 绘制图表
            statisticsAppTypeChart.setOption({
              title : {
                text: '订单类型比例',//主标题
                x:'center',
                textStyle:{
                  fontSize :'15'
                },
              },
              series: [
                {
                  name:'订单类型比例',
                  type:'pie',
                  radius : '50%',
                  data: data,
                  itemStyle: {
                    emphasis: {
                      shadowBlur: 10,
                      shadowOffsetX: 0,
                      shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                  }
                }
              ]
            })
          }).catch(() => {

          })
        }
        if(this.userInfo.type == '2'){
          getStatisticsShopUser().then(response => {
            let data = response.data.data
            this.option1.data[1].count = data.countToday
            this.option1.data[1].allcount = data.countTotal
          }).catch(() => {

          })
        }
        getStatisticsOrderInfo({
          isPay: '1'
        }).then(response => {
          let data = response.data.data
          this.option1.data[2].count = data.countToday
          this.option1.data[2].allcount = data.countTotal
        }).catch(() => {

        })
      },
      getQrCode() {
        getWxApp(this.wxApp.id).then(response => {
          this.dialogWxApp = false
          this.wxApp = response.data.data
          let qrUrl = this.h5HostMobile + '?app_id=' + this.wxApp.id + '&tenant_id=' + this.wxApp.tenantId
          //第三方平台托管的需要加上component_appid
          if ('1' == this.wxApp.isComponent) {
            qrUrl = qrUrl + '&component_appid=' + this.wxApp.componentAppId;
          }
          this.dialogQrCode = true
          this.qrCodeScene = qrUrl
        }).catch(() => {

        })
      },
      //获取公众号h5二维码
      showWxH5Code() {
        this.codeType = '2'
        this.wxApp = {}
        this.tableLoading = true
        getWxAppList({
          appType: '2'
        }).then(response => {
          this.tableLoading = false
          let data = response.data
          if (data && data.length > 0) {
            if (data.length == 1) {
              this.wxApp = data[0]
              this.getQrCode()
            } else {
              this.wxAppData = data
              this.dialogWxApp = true
            }
          } else {
            this.$message({
              showClose: true,
              message: '没有可选择的公众号，请先添加',
              type: 'error'
            })
          }
        }).catch(() => {
          this.tableLoading = false
        })
      },
      showH5Url() {
        let tenantId = this.userInfo.tenantId
        if(this.userInfo.type == '-1'){
          tenantId = getStore({ name: 'switchTenantId' })
        }
        let url = this.h5HostMobile + '?tenant_id=' + tenantId
        this.$alert(url, '在手机普通浏览器中打开此链接', {
          confirmButtonText: '确定'
        });
      },
      //获取首页PC端页面地址
      showPCUrl() {
        let tenantId = this.userInfo.tenantId
        if(this.userInfo.type == '-1'){
          tenantId = getStore({ name: 'switchTenantId' })
        }
        let url = this.h5HostPC + '?tenant_id=' + tenantId
        window.open(url)
      },
      //获取小程序码
      showMaCode() {
        this.codeType = '1'
        this.tableLoading = true
        this.wxApp = {}
        getWxAppList({
          appType: '1'
        }).then(response => {
          this.tableLoading = false
          let data = response.data
          if (data && data.length > 0) {
            if (data.length == 1) {
              this.wxApp = data[0]
              this.getMaCode()
            } else {
              this.wxAppData = data
              this.dialogWxApp = true
            }
          } else {
            this.$message({
              showClose: true,
              message: '没有可选择的小程序，请先添加',
              type: 'error'
            })
          }
        }).catch(() => {
          this.tableLoading = false
        })
      },
      getMaCode() {
        getMaCode({
          appId: this.wxApp.id,
          scene: "scene"
        }).then(response => {
          this.dialogWxApp = false
          this.dialogQrCode = true
          this.qrCodeScene = response.data.data
        })
      },
      showAppUrl() {
        this.$alert('https://www.gocreateone.com/h-nd-108.html#_jcp=1', '请下载并安装App', {
          confirmButtonText: '确定'
        });
      },
    }
  }
</script>

<style lang="scss">
  .wel-view {
    height: calc(100% - 30px);
    overflow-y: scroll;

    .el-card__body {
      padding: 5px;
    }

    .data-view-content {
      padding: 10px;

      .change-layout {
        display: flex;
        flex-direction: row;

        .change-item+.change-item {
          border-left: none;
        }

        .change-item {
          padding: 0 10px;
          border: 1px solid #EEEEEE;
          line-height: 30px;
          text-decoration: none;
          outline: none;

          &.active {
            background: #44B549;
            color: #FFFFFF;
          }
        }
      }
    }
  }

  .terminal {
    padding: 10px;
    display: flex;
  }

  .terminal-list {
    cursor: pointer;
    align-items: center;
    display: flex;
    margin-right: 40px;
  }

  .terminal-img {
    width: 30px;
    height: 30px;
    margin-right: 10px;
  }

  .box-card{
    box-shadow: none !important;
    border-radius: 0px;
    border: none;
    border-bottom: solid #f1f1f1 1px;
  }
</style>
