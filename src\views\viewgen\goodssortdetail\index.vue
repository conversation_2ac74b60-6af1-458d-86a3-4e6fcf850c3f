<template>
  <div class="execution">
    <basic-container>
      <el-row type="flex"  justify="center">
        <h3>{{name}}</h3>
      </el-row>
      <avue-crud ref="crud"
                 :page="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 v-model="form"
                 @on-load="getPage"
                 @refresh-change="refreshChange"
                 @row-update="handleUpdate"
                 @row-save="handleSave"
                 @row-del="handleDel"
                 @sort-change="sortChange"
                 @search-change="searchChange">
        <template slot-scope="scope" slot="menuLeft">
          <el-button type="primary"
                     icon="el-icon-plus"
                     size="small"
                     @click.stop="openFormBox()">添加排序
          </el-button>
        </template>
        <template slot-scope="scope" slot="sortValue">
            <el-tag>{{scope.row.sortValue}} </el-tag>
        </template>
        <template slot-scope="scope" slot="menu">
          <el-button v-if="permissionList.delBtn" @click="handleDel(scope.row,scope.index)" icon="el-icon-delete"
                     type="text" size="small">剔除排序
          </el-button>
        </template>
      </avue-crud>
    </basic-container>


    <!--     提交表单-->
    <el-dialog
      title="作品列表"
      :visible.sync="goodsBoxVisible"
      :close-on-click-modal="false"
      @close="resetForm()"
      center
      :append-to-body="true"
      lock-scroll
      width="80%">
      <div style="overflow: hidden">
        <goods-select ref="goodsSelectBox" :sortId="sortId" @confirm="confirmSelectGoods"></goods-select>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {getPage, addBatch, addObj, putObj, delObj} from '@/api/viewgen/wxgoodssortdetail'
import {tableOption} from '@/const/crud/viewgen/wxgoodssortdetail'
import {mapGetters} from 'vuex'
import goodsSelect from "@/views/viewgen/goodslist/goodsSelect";

export default {
  components: {goodsSelect },
  name: 'wxgoodssort',
  data() {
    return {
      sortId: '',
      name: '',
      goodsBoxVisible: false,
      form: {
        name: '',
        appId: '',
        appIdDisabled: false,
      },
      formRules: {
        name: [
          {required: true, message: '请输入排序名称', trigger: 'submit'},
        ],
        appId: [
          {required: true, message: '请选择公众号', trigger: 'submit'}
        ],
      },
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption
    }
  },
  created() {
  },
  mounted() {
    this.initPage();
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.permissions['weixin:wxgoodssort:add'] ? true : false,
        delBtn: this.permissions['weixin:wxgoodssort:del'] ? true : false,
        editBtn: this.permissions['weixin:wxgoodssort:edit'] ? true : false,
        viewBtn: this.permissions['weixin:wxgoodssort:get'] ? true : false
      };
    }
  },
  methods: {
    initPage(){
      console.log("传来的id",this.$route.params.id)
      if (this.$route.params.id) {
        this.sortId = this.$route.params.id;
        this.name = this.$route.params.name;
        window.localStorage.setItem('goodsSortId', this.sortId);
        window.localStorage.setItem('goodsSortName', this.name);
        this.getPage(this.page)
      } else {
        //只存不删... 可能此页面后期独立出来 暂不处理
        this.sortId = window.localStorage.getItem('goodsSortId');
        this.name = window.localStorage.getItem('goodsSortName');
        this.getPage(this.page)
      }

    },
    searchChange(params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    sortChange(val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage(page, params) {
      this.tableLoading = true
      getPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
        sortId:this.sortId
      }, params, this.paramsSearch)).then(response => {
        this.tableData = response.data.data.records
        this.tableData.forEach(o=>{
          o.showPutBtn = false
        })
        this.page.total = response.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this
      this.$confirm('是否确认剔除此排序', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        return delObj(row.id)
      }).then(data => {
        _this.$message({
          showClose: true,
          message: '删除成功',
          type: 'success'
        })
        this.getPage(this.page)
      }).catch(function (err) {
      })
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      console.log()
      if ( row.sortValue>100000 || row.sortValue<-100000 ) {
        this.$message.warning("请填写正负十万内的数字")
        loading()
        return
      }
      putObj(row).then(response => {
        this.$message({
          showClose: true,
          message: '修改成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      addObj(row).then(response => {
        this.$message({
          showClose: true,
          message: '添加成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page)
    },
    resetForm() {
    },
    openFormBox(type, obj) {
      this.goodsBoxVisible = true;
      this.$refs.goodsSelectBox.refreshChange();
    },
    confirmFrom() {
      if(!this.form.id) {
        console.log(this.form)
        this.$refs[this.form].validate((valid) => {
          if (valid) {
            addObj(this.form).then(res => {
              this.$message({
                showClose: true,
                message: '添加成功',
                type: 'success'
              })
              this.goodsBoxVisible = false;
              this.refreshChange()
            }).catch(() => {
            })
          }
        });
      } else {
        this.$refs[this.form].validate((valid) => {
          if (valid) {
            putObj(this.form).then(response => {
              this.$message({
                showClose: true,
                message: '修改成功',
                type: 'success'
              })
              this.goodsBoxVisible = false;
              this.refreshChange()
            }).catch(() => {
            })
          }
        });
      }
    },
    //确认商品选择
    confirmSelectGoods(list){
      // console.log("阿哈哈哈",list)
      let addList = [];
      for (let i = 0; i < list.length; i++) {
        addList.push({goodsId:list[i].id,sortId:this.sortId,sortValue:0})
      }
      console.log("提交的",addList)
      addBatch(Object.assign(addList)).then(data => {
        this.$message({
          showClose: true,
          message: '添加成功',
          type: 'success'
        })
        this.refreshChange();
        this.goodsBoxVisible = false;
      }).catch(() => {
      })
    },
    //修改排序值
    putSortValue(obj){
      console.log("修改排序",obj)
      putObj(obj).then(response => {
        this.$message({
          showClose: true,
          message: '修改成功',
          type: 'success'
        })
        this.refreshChange(this.page)
      }).catch(() => {
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
