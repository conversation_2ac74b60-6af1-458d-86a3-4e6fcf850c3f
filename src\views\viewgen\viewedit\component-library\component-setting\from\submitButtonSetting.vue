<template>
  <div>
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">提交按钮</p>
      <div slot="hint"></div>
      <div slot="mainContent">
        <el-divider>基础属性</el-divider>
        <el-form ref="form" label-width="100px" :model="formData">
          <el-form-item label="按钮内容">
            <el-input v-model="formData.content" size="mini"  placeholder="按钮文字">
            </el-input>
          </el-form-item>
          <el-form-item label="按钮大小">
            <el-radio-group v-model="formData.size">
              <el-radio :label="'100%'">整体</el-radio>
              <el-radio :label="'default'">普通</el-radio>
              <el-radio :label="'mini'">小型</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="按钮位置">
            <el-radio-group v-model="formData.location">
              <el-radio :label="'flex-start'">靠左</el-radio>
              <el-radio :label="'center'">居中</el-radio>
              <el-radio :label="'flex-end'">靠右</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="按钮颜色">
            <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
              <el-input v-model="formData.buttonColor" size="small" >
                <template slot="append">
                  <el-color-picker size="mini" v-model="formData.buttonColor"></el-color-picker>
                </template>
              </el-input>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="字体颜色">
            <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
              <el-input v-model="formData.fontColor" size="small" >
                <template slot="append">
                  <el-color-picker size="mini" v-model="formData.fontColor"></el-color-picker>
                </template>
              </el-input>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="按钮类型">
            <el-radio-group v-model="formData.type">
              <el-radio :label="1">提交按钮</el-radio>
<!--              <el-radio :label="2">跳转按钮</el-radio>-->
            </el-radio-group>
          </el-form-item>
          <el-divider></el-divider>
          <div v-if="formData.type == 1">
            <el-form-item label="提交标签">
                <el-button @click="openTagBox" v-show="!formData.submitTag || !formData.submitTag.id " icon="el-icon-plus" size="mini"></el-button>
                <el-tag  @click="openTagBox" v-show="formData.submitTag && formData.submitTag.id"  size="medium" :color="formData.submitTag?formData.submitTag.backColor:''"
                        :style="formData.submitTag?getFontColor(formData.submitTag.fontColor):''">{{ formData.submitTag?formData.submitTag.name:'' }} </el-tag>
                <el-button @click="deleteSubmitTag" v-show="formData.submitTag && formData.submitTag.id" icon="el-icon-delete" type="danger" size="mini"></el-button>
          </el-form-item>
            <el-form-item label="提交提示">
                <el-input v-model="formData.submitMsg" size="mini" >
                </el-input>
            </el-form-item>
            <el-form-item label="跳转地址">
              <wx-page-select
                @switchChange="formData.jumpObj.isSystemUrl=$event"
                :isSystemUrl="formData.jumpObj.isSystemUrl"
                :page="formData.jumpObj.pageUrl"
                @change="formData.jumpObj.pageUrl=$event"></wx-page-select>
          </el-form-item>

          </div>
          <div v-if="formData.type == 2">
            <el-form-item label="跳转信息">
              <el-input v-model="formData.describe" size="mini"  placeholder="按钮文字">
              </el-input>
            </el-form-item>
          </div>
          <div v-if="formData.type == 3">
            <el-form-item label="支付金额">
              <el-input v-model="formData.describe" size="mini"  placeholder="按钮文字">
              </el-input>
            </el-form-item>
            <el-form-item label="支付后标签">
              <el-input v-model="formData.describe" size="mini"  placeholder="按钮文字">
              </el-input>
            </el-form-item>
            <el-form-item label="支付后跳转">
              <el-input v-model="formData.describe" size="mini"  placeholder="按钮文字">
              </el-input>
            </el-form-item>
            <el-form-item label="商品详情">
              <el-input v-model="formData.describe" size="mini"  placeholder="按钮文字">
              </el-input>
            </el-form-item>
            <el-form-item label="支付后背景">
              <el-input v-model="formData.describe" size="mini"  placeholder="按钮文字">
              </el-input>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </settingSlot>
    <!--      微信用户标签-->
    <el-dialog
      title="标签选择"
      :visible.sync="tagBoxVisible "
      :close-on-click-modal="false"
      center
      :append-to-body="true"
      lock-scroll
      width="80%">
      <div style="overflow: hidden">
        <wx-user-tag  :selectedType="0"  :selectedTagId="selectedTagId" :appId="appId"  v-on:ensureTag="ensureTag" @backFun="ensureTag"></wx-user-tag>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';

import settingSlot from './../settingSlot'
import MaterialList from '@/components/material/wxlist.vue'
import iconSelect from '../../pages/page-components/iconSelect.vue'
import wxUserTag from "@/views/wxmp/wxusertags/userTagSelect";
import WxPageSelect from '@/components/wx-page-select/Index.vue'
export default {
  components: {settingSlot, MaterialList,iconSelect,wxUserTag,WxPageSelect},
  data() {
    return {
      selectedTagId:'',
      tagBoxVisible:false,
      formDataCopy: {
        content: '提交按钮',
        size: '100%',
        location: 'center',
        buttonColor: '#FF0000',
        fontColor: '#FFFFFF',
        type: 1,//按钮是否加粗
        submitTag: {},//提交标签
        jumpObj: {
          isSystemUrl:true,
          pageUrl:'',
        },//跳转按钮
        submitMsg:'',
      },
      formData: {}
    };
  },
  props: {
    appId: {
      type: String,
    },
    clientType: [String],
    showData: {
      type: Object,
      default: () => {
      }
    },
    config: {
      type: Object,
      default: () => {
      }
    },
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
      clickComIndex: state => state.divpage.clickComIndex,
    })
  },
  watch: {
    showData: {
      handler(newVal, oldVal) {
        this.formData = newVal ? newVal : this.formData;
      },
      deep: true
    },
  },
  mounted() {
    let that = this;
    if (that.IsEmptyObj(that.showData)) {
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
    } else {
      that.formData = that.showData
    }
    that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
    // that.updateData({
    //   componentsList: that.componentsList
    // })
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),

    // 添加项目
    addItem() {
      let that = this;
      that.pushItem()
    },
    pushItem() {
      let that = this;
      if (that.formData.noticeList.length >= 10) {
        that.$message.error("项目不能超过10条")
        return false;
      }
      that.formData.noticeList.push({
        id: Math.random(),
        imageUrl: '',
        imgWidth: 0,
        imgHeight: 0,
        pageUrl: '',
        content: '',
        tag: ''
      })
    },
    // 删除项目
    delItem(index) {
      let that = this;
      if (that.formData.swiperList.length <= 1) {
        that.$message.error("请至少保留一条项目")
        return false;
      }
      that.$confirm('是否删除该项目?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$delete(that.formData.noticeList, index)
      }).catch(() => {
      })
    },
    // 删除按钮
    delBtn(index) {
      let that = this;
      that.$confirm('是否删除该按钮?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
        that.updateData({componentsList: that.componentsList});
      }).catch(() => {
      })
    },
    cancel() {
      this.$emit('cancel')
    },
    reset() {
      let that = this;
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      that.componentsList[that.clickComIndex]['data'] = this.formData;
    },
    confirm() {
      this.$emit('confirm', this.formData)
    },
    delete() {
      this.$emit('delete')
    },
    isCustomUrlChange(val) {
      if (val) {
        this.formData.pageUrl = "";
      } else {
        this.formData.pageUrl = this.$refs.pageSelect.page
      }
    },
    addMenu() {
      this.formData.menuList.push({
        title: "按钮1",
        isCustomUrl: false,
        pageUrl: ""
      })
    },
    delMenu(index) {
      this.formData.menuList.splice(index, 1);
    },
    // 拖动的事件等等=======================================================>
    datadragStart(e) {
      let that = this;
      that.updateData({clickComIndex: null})
      that.showSetBlock = false;
    },
    datadragUpdate(e) {
      e.preventDefault();
    },
    datadragEnd(e) {
      // console.log(e, "拖动结束");
    },
    datadragMove(e, originalEve) {
      return (e.draggedContext.element.text !== 'Gold（不可拖动元素）');
    },
    openTagBox() {
      if(this.formData.submitTag){
        this.selectedTagId = this.formData.submitTag.id;
      }
      // this.tagBoxFlag = flag;
      this.tagBoxVisible = true;
    },
    //转化得到字体颜色
    getFontColor(val) {
      if (!val) {
        return;
      }
      return "color:" + val;
    },
    ensureTag(obj) {
      console.log("确认标签", obj)
      this.formData.submitTag={
        id:obj.id,
        fontColor:obj.fontColor,
        backColor:obj.backColor,
        name:obj.name,
      }
      this.tagBoxVisible = false;
    },
    deleteSubmitTag() {
      this.formData.submitTag={}
    },
  },

};
</script>
<style lang='less' scoped>

@import '../../colorui/main.css';

.el-form-item {
  margin-bottom: 0;
}

.menu_list_title_name {
  display: inline;
}

.menu_list_title {
  display: block;
  width: 80%;
}

.menu_list_tag {
  display: none;
  float: right;
}

.drag-item {
  padding: 0px 0 5px 20px;
  margin-bottom: 15px;
  margin-top: 20px;
  border: 1px solid transparent;

  &:hover {
    cursor: move;
    border: 1px dashed #1fc421;

    .menu_list_tag {
      display: inline;
    }
  }
}
</style>
