<!-- 商品分类组件 -->
<template>
  <div class="auctionTimeComponent"
       :style="{paddingTop:`${setData.paddingTop}px`,paddingBottom:`${setData.paddingBottom}px`}">
    <div style="width: 100%;padding:0 5px">
      <div class="container">
        <!-- 店铺列表 -->
        <div class="flex shop_select align-center justify-center bg-white padding-top-sm">
          <div class="flex-sub">
            店铺选择:
          </div>
          <div class="flex-twice">
            XXXXXX店
          </div>
        </div>
        <!-- 日期列表 -->
        <div class="date_select flex align-center justify-center  padding-top-sm">
          <div class="flex-twice">
            <div>
              <div class="cuIcon-back" style="display: inline-block">
              </div>
              2022-03-04~2022-03-01
              <div class="cuIcon-right" style="display: inline-block">
              </div>
            </div>
          </div>
          <div class="flex-sub">
            更多时间选择
          </div>
        </div>
        <!--          星期-->
        <div class="date_overflow">
          <div>
            <div style="display: flex; justify-content: center;align-items: center">星期五</div>
            <div>2022-03-04</div>
          </div>
          <div>
            <div style="display: flex; justify-content: center;align-items: center">星期六</div>
            <div>2022-03-05</div>
          </div>
          <div>
            <div style="display: flex; justify-content: center;align-items: center">星期日</div>
            <div>2022-03-06</div>
          </div>
          <div>
            <div style="display: flex; justify-content: center;align-items: center">星期一</div>
            <div>2022-03-07</div>
          </div>
        </div>

        <!-- 时间选项 -->
        <div class="time-box">
          <div v-for="(item,index) in 8" :key="index" style="width: 25%">
            <div class="item">
              <div class="item-box" >
                <div class="box" >
                  <div style="display: flex;justify-content: center;  align-items: center">{{ (item+10)+":00:00 "}}</div>
                  <div style="display: flex;justify-content: center;  align-items: center">已约满</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 预约时间段 -->
        <div class="time_btn">
          <div style="display: flex; justify-content: center;align-items: center">
            <div >预约时间：</div>
            <div >星期四</div>
            <div>2022-02-12 08:00:00</div>
          </div>
          <!-- 确认按钮 -->
          <div class="bottom">
            <el-button size="small" >
              确认
            </el-button >
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';

export default {
  data() {
    return {};
  },
  components: {},
  props: {
    thememobile: {type: Object | Array},
    setData: {type: Object | Array},
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {

  },
  mounted() {
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
  },
  watch: {
    componentsList(newVal, oldVal) {          //添加的时候触发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    }
  }
};
</script>
<style lang='less' scoped>

@import '../../colorui/main.css';
@import '../../colorui/icon.css';

.auctionTimeComponent {
  display: flex;
  justify-content: center;

}

.shop_select {
  padding-left: 20px;
  font-weight: bold;
}

.date_select {
  padding: 10px 0;
}
.date_overflow {
  display: flex;
  padding: 10px 0;
  justify-content: space-around;
  border-bottom: 1px solid #e5e5e5;
}

.time-box {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  width: 100%;
  padding: 20px 0  0  0;
  // margin-top:10px;
  .item {
    margin: 10px 0;
    justify-content: center;
    align-items: center;
    .box {
      align-items: center;
      height: 80px;
      width: 80px;
      background: #fff;
      color: #333;
      border: 1px solid #EEEEEE;
      border-radius: 10px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
  }
}
.time_btn{
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}
.bottom {
  padding: 15px 0 15px 10px;
}
</style>
