<template>
  <div>
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">作品封面</p>
      <div slot="hint">
      </div>
      <div slot="mainContent">
        <el-form ref="form"  label-width="100px" :model="formData">
          <el-divider>基础属性</el-divider>
          <el-form-item label="组件上边距">
            <el-input-number v-model="formData.pagePaddingTop" :min="0" size="mini"   style="margin-top: 5px" >
            </el-input-number> px
          </el-form-item>
          <el-form-item label="组件下边距">
            <el-input-number v-model="formData.pagePaddingBottom" :min="0" size="mini"  style="margin-top: 5px" >
            </el-input-number> px
          </el-form-item>
          <el-form-item label="组件左边距">
            <el-input-number v-model="formData.pagePaddingLeft" :min="0" size="mini"  style="margin-top: 5px" >
            </el-input-number> px
          </el-form-item>
          <el-form-item label="组件右边距">
            <el-input-number v-model="formData.pagePaddingRight" :min="0" size="mini"  style="margin-top: 5px" >
            </el-input-number> px
          </el-form-item>
          <el-divider >内容显示</el-divider>
          <el-form-item label="加载方式" >
            <el-radio-group v-model="formData.loadingType">
              <el-radio :label="1">分页</el-radio>
<!--              <el-radio :label="2">下滑</el-radio>-->
            </el-radio-group>
          </el-form-item>
          <el-form-item label="横向数量">
              <el-input-number v-model="formData.rowNumber" size="mini" type="number" placeholder="数量"  style="margin-top: 5px" :min="1" :max="10"></el-input-number>
          </el-form-item>
          <el-form-item label="纵向数量">
            <el-radio-group v-model="formData.colNumber">
              <el-radio :label="1">单列</el-radio>
              <el-radio :label="2">两列</el-radio>
              <el-radio :label="3">三列</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item  label="图片尺寸 " >
            <el-radio-group v-model="formData.imgShowSize">
              <el-radio :label="1">比例一</el-radio>
              <el-radio :label="2">比例二</el-radio>
              <el-radio :label="3">比例三</el-radio>
              <el-radio :label="4">比例四</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="内容展示">
            <el-radio-group v-model="formData.searchType">
              <el-radio :label="1">默认</el-radio>
              <el-radio :label="2">按标签搜索</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="所选标签" v-show="formData.searchType==2">
              <div>
                <el-tag class="goods_form_tag" v-for="(tag,index) in formData.tagList" :key="index" size="medium"
                        :color="tag.backColor"
                        :style="{color:tag.fontColor?tag.fontColor:''}">{{ tag.name }}
                </el-tag>
                <el-button @click="openTagBox(formData.tagList)" icon="el-icon-plus" size="mini"></el-button>
              </div>
            </el-form-item>
          <el-form-item label="显示宽度" >
            <el-input-number  v-model="formData.width"  size="mini" :min="0" :max="100"></el-input-number> %
          </el-form-item>
<!--          <el-form-item label="图片间距" >-->
<!--            <el-input-number v-model="formData.imgPadding" size="mini" :min="0" style="margin-top: 5px"></el-input-number> px-->
<!--          </el-form-item>-->
          <el-form-item label="作品名称" >
            <el-switch
              v-model="formData.nameFlag"
              active-color="#13ce66"
              active-text="显示"
              inactive-text="不显示">
            </el-switch>
          </el-form-item>
<!--          <el-form-item label="作品标签" >-->
<!--            <el-switch-->
<!--              v-model="formData.tagFlag"-->
<!--              active-color="#13ce66"-->
<!--              active-value="0"-->
<!--              inactive-value="1"-->
<!--              active-text="显示"-->
<!--              inactive-text="不显示">-->
<!--            </el-switch>-->
<!--          </el-form-item>-->
          <el-divider content-position="center">跳转设置</el-divider>
          <div class="notDraggable" style="padding: 0 0 5px 20px">
            <wx-page-select  :isSystemUrl="formData.isSystemUrl" @switchChange="formData.isSystemUrl=$event" :clientType="clientType" :page="formData.pageUrl" @change="formData.pageUrl=$event"></wx-page-select>
          </div>
        </el-form>
      </div>
    </settingSlot>
    <!-- 商品标签栏 goodsTagBoxVisible-->
    <el-dialog
      :append-to-body="true"
      title="作品标签"
      :visible.sync="goodsTagBoxVisible"
      width="80%"
      center>
      <goods-tag-select ref="goodsTagSelect" :selectTagList="selectTagList"></goods-tag-select>
    </el-dialog>
  </div>
</template>

<script>

  import { mapState , mapGetters , mapMutations , mapActions } from 'vuex';

  import settingSlot from './settingSlot'
  import MaterialList from '@/components/material/wxlist.vue'
  import WxPageSelect from '@/components/wx-page-select/Index.vue'
  import {getTagAndType} from "@/api/viewgen/wxgoodstag";
  import goodsTagSelect from "@/views/viewgen/goodslist/goodsTagSelect";

  export default {
    components: { settingSlot, MaterialList, WxPageSelect,goodsTagSelect },
    data() {
      return {
        goodsTagBoxVisible: false,
        selectTagList:[],
        formDataCopy: {
          pagePaddingTop:0,
          pagePaddingBottom:0,
          pagePaddingLeft:0,
          pagePaddingRight:0,
          rowNumber: 1,
          colNumber: 1,
          loadingType: 1,
          imgShowSize: 1,
          searchType: 1,//内容展示形式 1默认  2所选标签
          nameFlag: true,
          // tagFlag: true,
          isSystemUrl: true,
          pageUrl: '',
          width: 100,
          // imgPadding: 0,
          fontSize: '16',
          color: '#000000',
          backColor: '#ffffff',
          title: '标题',
          tagList:[] ,//包含标签
        },
        formData : {}
      };
    },
    props: {
      clientType: [String],
      showData:{
        type: Object,
        default: ()=> {}
      },
      config   : {
        type: Object,
        default: ()=> {}
      }
    },
    created() {
    },
    computed: {
      ...mapState({
        componentsList: state => state.divpage.componentsList,
        clickComIndex : state => state.divpage.clickComIndex,
      })
    },
    watch: {
      showData: {
        handler(newVal, oldVal) {
          this.formData = newVal ? newVal : this.formData;
        },
        deep: true
      },
    },
    mounted(){
      let that = this;
      if(that.IsEmptyObj(that.showData)){
        that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      } else {
        that.formData = that.showData
      }
      that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
      // that.updateData({
      //   componentsList: that.componentsList
      // })
    },
    methods: {
      ...mapMutations([
        'updateData'
      ]),

      // 添加项目
      addItem(){
        let that = this;
        that.pushItem()
      },
      pushItem(){
        let that = this;
        if(that.formData.noticeList.length >=10){
          that.$message.error("项目不能超过10条")
          return false;
        }
        that.formData.noticeList.push({
          id       : Math.random(),
          imageUrl : '',
          imgWidth : 0,
          imgHeight: 0,
          pageUrl  : '',
          content  : '',
          tag: ''
        })
      },
      // 删除项目
      delItem(index){
        let that = this;
        if(that.formData.swiperList.length<=1){
          that.$message.error("请至少保留一条项目")
          return false;
        }
        that.$confirm('是否删除该项目?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText : '取消',
          type : 'warning'
        }).then(()=>{
          that.$delete(that.formData.noticeList, index)
        }).catch(()=>{})
      },
      // 删除按钮
      delBtn(index){
        let that = this;
        that.$confirm('是否删除该按钮?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText : '取消',
          type : 'warning'
        }).then(()=>{
          that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
          that.updateData({ componentsList: that.componentsList });
        }).catch(()=>{})
      },
      cancel(){
        this.$emit('cancel')
      },
      reset(){
        let that = this;
        that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
        that.componentsList[that.clickComIndex]['data'] = this.formData;
      },
      confirm(){
        this.$emit('confirm', this.formData)
      },
      delete(){
        this.$emit('delete')
      },
      openTagBox(list) {
        this.selectTagList = list;
        this.goodsTagBoxVisible = true;
      },
    },

  };
</script>
<style lang='less' scoped>

  @import '../colorui/main.css';
  .el-form-item{
    margin-bottom: 0;
  }
</style>
