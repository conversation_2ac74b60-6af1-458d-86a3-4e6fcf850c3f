import request from '@/router/axios'


export function getPreView(query) {
  return request({
    url: '/weixin/wxmpqrcodemessage/preview',
    method: 'get',
    params: query
  })
}

export function getPage(query) {
  return request({
    url: '/weixin/wxmpqrcodemessage/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/weixin/wxmpqrcodemessage',
    method: 'post',
    data: obj
  })
}
export function getObj(id) {
  return request({
    url: '/weixin/wxmpqrcodemessage/' + id,
    method: 'get'
  })
}

export function test() {
  return request({
    url: '/weixin/wxmpqrcodemessage/test',
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/wxmpqrcodemessage/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/wxmpqrcodemessage',
    method: 'put',
    data: obj
  })
}
