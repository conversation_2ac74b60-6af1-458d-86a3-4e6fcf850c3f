import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/material/page',
    method: 'get',
    params: query
  })
}

export function addObjList(obj) {
  return request({
    url: '/weixin/material/list',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/weixin/material/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/material/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/material',
    method: 'put',
    data: obj
  })
}
