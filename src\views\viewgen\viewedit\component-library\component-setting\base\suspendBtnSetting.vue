<!--商品分类标签-->
<template>
  <div class="suspendBtnSetting">
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">悬浮按钮</p>
      <div slot="mainContent">
        <el-form ref="form" label-width="100px" :model="formData">
          <el-divider>基础属性</el-divider>
          <el-form-item label="水平方式">
            <el-radio-group v-model="formData.horizontal">
              <el-radio :label="'left'">左</el-radio>
              <el-radio :label="'right'">右</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="垂直方式">
            <el-radio-group v-model="formData.vertical">
              <el-radio :label="'top'">上</el-radio>
              <el-radio :label="'bottom'">下</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="内容展开">
            <el-radio-group v-model="formData.direction">
              <el-radio :label="'horizontal'">水平显示</el-radio>
              <el-radio :label="'vertical'">垂直显示</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="按钮颜色">
            <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
              <el-input v-model="formData.buttonColor" size="small" style="margin-top: 5px">
                <template slot="append">
                  <el-color-picker size="mini" v-model="formData.buttonColor"></el-color-picker>
                </template>
              </el-input>
            </el-tooltip>
          </el-form-item>
          <el-divider>展开内容</el-divider>
          <draggable v-model="formData.content" @start="datadragStart" @update="datadragUpdate" @end="datadragEnd"
                     :move="datadragMove" :options="{filter:'.notDraggable', preventOnFilter: false,animation:500}">
            <transition-group>
              <div v-for="(item,index) in formData.content" :key="'dragItem'+index" class="drag-item">
                <el-form-item>
                  <el-button class="menu_list_tag" size="mini" icon="el-icon-delete-solid" @click="delContent(index)"
                             type="danger"></el-button>
                </el-form-item>
                <el-form-item label="内容名称">
                  <el-input class="menu_list_title notDraggable" :draggable="false" v-model="item.text" size="mini"
                            style="margin-top: 5px" :maxlength="4" show-word-limit></el-input>
                </el-form-item>
                <el-form-item label="图标显示" >
                  <div :draggable="false"  class="notDraggable">
                    <MaterialList :value="item.iconPath?[item.iconPath]:[]"
                                  @sureSuccess="item.iconPath = $event?$event[0]:''"
                                  @deleteMaterial="item.iconPath = ''"
                                  type="image" shopId="-1"
                                  :num=1
                                  :divStyle="'width:100%;height:95px;margin-bottom:8px;line-height: 100px;'"></MaterialList>
<!--                    <el-image-->
<!--                      v-show="item.iconPath"-->
<!--                      class="image_preview_image"-->
<!--                      fit="fill"-->
<!--                      :src="item.iconPath"-->
<!--                      :preview-src-list="[item.iconPath]">-->
<!--                    </el-image>-->
<!--                    <el-upload-->
<!--                      :file-list="uploadList.list"-->
<!--                      :show-file-list="false"-->
<!--                      v-show="!item.iconPath"-->
<!--                      :action="serverUrl"-->
<!--                      :headers="header"-->
<!--                      :before-upload="beforeUpload"-->
<!--                      accept=".jpg,.jpeg,.png,.bmp,.JPG,.JPEG,.PNG,.BMP"-->
<!--                      :on-success="(res,file,fileList)=>{return uploadSuccess(res,file,fileList,item,index)}"-->
<!--                      :on-error="(res)=>{return uploadError(res,index)}"-->
<!--                      :on-change="(file,fileList)=>{return handleUploadChange(file,fileList,index)}"-->
<!--                    >-->
<!--                      <el-image class="waite_upload_img">-->
<!--                        <div style="margin-top:32%" slot="error">-->
<!--                          <i v-show="uploadList[index].uploadIconVisible" class="el-icon-upload2">点击上传</i>-->
<!--                          <el-progress v-show="uploadList[index].processIconVisible" type="circle" :width="55"-->
<!--                                       :percentage="uploadList[index].percentage"></el-progress>-->
<!--                        </div>-->
<!--                      </el-image>-->
<!--                    </el-upload>-->
<!--                    <div v-show="item.iconPath" class="img_dialog">-->
<!--                        <span @click="handleRemove(item,index)">-->
<!--                          <i class="el-icon-delete"></i>-->
<!--                        </span>-->
<!--                    </div>-->
                  </div>
                </el-form-item>
                <el-form-item label="按钮类型">
                  <el-radio-group class="notDraggable" v-model="item.button.type"  @change="(val)=>{return radioChange(val,index)}">>
                    <el-radio :label="1">新页面</el-radio>
                    <el-radio :label="2">电话号码</el-radio>
                    <el-radio :label="3">弹窗显示</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label=跳转地址 v-if="item.button.type==1">
                  <wx-page-select class="notDraggable" :isSystemUrl="item.button.isSystemUrl" @switchChange="item.button.isSystemUrl=$event"
                                  :page="item.button.pageUrl" @change="item.button.pageUrl=$event"></wx-page-select>
                </el-form-item>
                <el-form-item v-show="item.button.type==2" label="所需号码">
                  <el-input class="notDraggable" v-model="item.button.phone" size="mini" placeholder="手机号码"></el-input>
                </el-form-item>
                <el-form-item v-show="item.button.type==3" label="弹窗类型">
                  <el-radio-group class="notDraggable" v-model="item.button.popupType">
                    <el-radio :label="1">文字</el-radio>
                    <el-radio :label="2">图片</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="提示内容" v-show="item.button.type==3 && item.button.popupType==1">
                  <el-input class="notDraggable" v-model="item.button.content" size="small" style="margin-top: 5px"></el-input>
                </el-form-item>
                <el-form-item label="图片选择" v-show="item.button.type==3 && item.button.popupType==2">
                  <MaterialList class="notDraggable" :value="item.button.imgUrl?[item.button.imgUrl]:[]"
                                @sureSuccess="item.button.imgUrl = $event?$event[0]:''"
                                @deleteMaterial="item.button.imgUrl = ''"
                                type="image" shopId="-1"
                                :num=1
                                :divStyle="'width:100%;height:95px;margin-bottom:8px;line-height: 100px;'"></MaterialList>
                </el-form-item>
              </div>
            </transition-group>
          </draggable>
          <el-button v-show="formData.content.length<4" style="margin-left: 20px" @click="addContent" size="medium">添加
          </el-button>
        </el-form>
      </div>
    </settingSlot>
  </div>

</template>
<script>

import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';
import settingSlot from '../settingSlot';
import draggable from "vuedraggable";
import WxPageSelect from '@/components/wx-page-select/Index.vue'
import store from "@/store";
import MaterialList from '@/components/material/wxlist.vue'
export default {
  components: {settingSlot, draggable, WxPageSelect,MaterialList},
  data() {
    return {
      serverUrl: "/upms/file/upload?fileType=image&dir=wx/pageView/shareImg", // 这里写你要上传的图片服务器地址
      header: {Authorization: 'Bearer ' + store.getters.access_token}, // 有的图片服务器要求请求头需要有token
      uploadList: [{ //过期遮照上传
        list: [],
        uploadIconVisible: true,
        processIconVisible: false,
      }],
      formDataCopy: {
        horizontal: 'left',
        vertical: 'bottom',
        direction: 'horizontal',
        buttonColor: '#3c3e49',
        backgroundColor: '#ffffff',
        content: [{
          iconPath: "",
          selectedIconPath: '',
          text: "名称",
          button:{
            type:1,
            isSystemUrl:false,
            pageUrl:false,
          }
        }],
      },
      formData: {}
    };
  },
  props: {
    thememobile: {type: Object | Array},
    showData: {
      type: Object,
      default: () => {
      }
    },
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
      clickComIndex: state => state.divpage.clickComIndex,
    })
  },
  mounted() {
    let that = this;
    if (that.IsEmptyObj(that.showData)) {
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
    } else {
      that.formData = that.showData
    }
    that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
    // that.updateData({
    //   componentsList: that.componentsList
    // })
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    // 删除按钮
    delBtn(index) {
      let that = this;
      that.$confirm('是否删除该按钮?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
        that.updateData({componentsList: that.componentsList});
      }).catch(() => {
      })
    },
    cancel() {
      this.$emit('cancel')
    },
    reset() {
      let that = this;
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      that.componentsList[that.clickComIndex]['data'] = this.formData;
    },
    confirm() {
      this.$emit('confirm', this.formData)
    },
    delete() {
      this.$emit('delete')
    },
    // 拖动的事件等等=======================================================>
    datadragStart(e) {
      let that = this;
      that.updateData({clickComIndex: null})
      that.showSetBlock = false;
    },
    datadragUpdate(e) {
      e.preventDefault();
    },
    datadragEnd(e) {
      // console.log(e, "拖动结束");
    },
    datadragMove(e, originalEve) {
      return (e.draggedContext.element.text !== 'Gold（不可拖动元素）');
    },
    // 拖动的事件等等=======================================================>
    addContent() {
      this.formData.content.push({
        iconPath: "",
        selectedIconPath: '',
        text: "名称",
        button:{
          type:1,
          isSystemUrl:false,
          pageUrl:false,
        }
      })

    },
    delContent(index) {
      this.formData.content.splice(index, 1);
    },
    // 图片上传start =======================================================>
    handleRemove(item, index) {
      this.uploadList[index].uploadList = [];
      this.uploadList[index].uploadIconVisible = true;
      this.uploadList[index].processIconVisible = false;
      item.iconPath = "";
    },
    uploadSuccess(res, file, fileList, item, index) {
      this.uploadList[index].list.push(res.link);
      item.iconPath = res.link;
      this.$message.success("上传成功")
    },
    uploadError(res, item) {
      this.uploadList[index].list = res.link;
      this.uploadList[index].uploadIconVisible = true;
      this.uploadList[index].processIconVisible = false;
      this.uploadList[index].uploadDisabled = false;
      this.$message.error("上传失败")
    },
    handleUploadChange(file, fileList, index) {
      if (file.status == "ready") {
        this.uploadList[index].percentage = file.percentage;
        this.uploadList[index].uploadDisabled = true;
        this.uploadList[index].uploadIconVisible = false;
        this.uploadList[index].processIconVisible = true;
      } else if (file.status == "success") {
        this.uploadList[index].uploadIconVisible = true;
        this.uploadList[index].processIconVisible = false;
        this.uploadList[index].uploadDisabled = false;
      }
    },
    beforeUpload(file, item) {
      const isLt1M = file.size / 1024 / 1024 < 1;
      if (!isLt1M) {
        this.$message.error('上传图片大小不能超过 1MB!');
        item.uploadIconVisible = true;
        item.processIconVisible = false;
        item.uploadDisabled = false;
      }
      return isLt1M;
    },
    // 图片上传end =======================================================>
    radioChange(value,index){
      console.log("切换的",value,index)
      let obj = {};
      if(1 == value){
        obj={
          type: value,
          isSystemUrl: false,
          pageUrl: '',
        }
      }else if(2 == value){
        obj={
          type: value,
          payTag: {},
          name:'',
          price:0,
          isSystemUrl: false,
          pageUrl: '',
        }
      }else if(3 == value){
        obj={
          type: value,
          content:'',
          imgUrl:'',
          popupType:1,
        }
      }
      this.formData.content[index].button=obj
    }
  },
  watch: {
    showData: {
      handler(newVal, oldVal) {
        this.formData = newVal ? newVal : this.formData;
      },
      deep: true
    },
    thememobile() {
    },
  }
};
</script>
<style lang='less' scoped>

.el-form-item {
  margin-bottom: 0;
}

.menu_list_title {
  display: block;
  width: 80%;
}

.menu_list_tag {
  float: right;
}

.drag-item {
  //padding: 0px 0 5px 20px;
  margin-bottom: 15px;
  margin-top: 20px;
  border: 1px solid transparent;

  &:hover {
    cursor: move;
    border: 1px dashed #1fc421;

    .menu_list_tag {
      display: inline;
    }
  }
}



</style>
