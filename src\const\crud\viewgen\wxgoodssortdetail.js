export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  align: 'center',
  menuAlign: 'center',
  searchShow: false,
  excelBtn: false,//导出
  addBtn: false,
  printBtn: true,
  viewBtn: false,
  editBtn: true,
  delBtn: false,
  cellBtn:true,
  searchMenuSpan: 6,
  column: [
    {
      label: '主键',
      prop: 'id',
      hide: true,
    },
    {
      label: '作品名称',
      prop: 'name',
    },
    {
      label: '排序值',
      prop: 'sortValue',
      type: 'number',
      rules: [{
        required: true,
        message: '请填写正负十万内的数字',
        trigger: 'blur'
      }],
      sortable: true,
      cell:true,
      slot: true,
    },

  ]
}
