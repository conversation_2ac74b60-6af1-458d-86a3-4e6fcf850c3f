<template>
  <div class="rollingBarrageComponent">
    <div class="box">
      <div class="barrage shadow-warp "
           :style="{'opacity':`${setData.opacity}`,background: `${setData.backColor}`,color: `${setData.fontColor}`}">
        XXXXXX{{ setData.subscribe }}
      </div>
    </div>
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';


export default {
  data() {
    return {};
  },
  components: {},
  props: {
    theme: {type: Object | Array},
    setData: {type: Object | Array},
    cId: {type: Number},
    noEditor: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {
  },
  mounted() {
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
  },
  watch: {
    setData(newVal, oldVal) {
    },
    componentsList(newVal, oldVal) {          //添加的时候出发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    }
  },
  beforeDestroy() {
    // this.$root.Bus.$off('addHotSpot')
  }
};
</script>
<style lang='less' scoped>

@import '../../colorui/main.css';
@import '../../colorui/icon.css';

.rollingBarrageComponent {
  overflow: hidden;
  background: #E1E1E1;

}
.box{
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center;     /* 垂直居中 */
  height: 100px;
  text-align: center;
}

.barrage {
  width: 150px;
  height: 30px;
  line-height: 30px;
  align-items: center;
  border-radius: 5px;
}

</style>
