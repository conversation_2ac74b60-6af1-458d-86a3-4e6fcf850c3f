<template>
  <div class="execution">
    <el-row type="flex"  justify="start">
      <el-col>
        <div  class="selected_app">
          所选公众号:
          <el-select :disabled="selectedAppIdDisabled" v-model="selectedAppId"  placeholder="请选择" @change="getTagAndType" >
            <el-option
              v-for="item in wxAppList"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </div>
      </el-col>
      <el-col>
        <el-button type="primary" @click="openTagBox('add')">新增标签</el-button>
      </el-col>
    </el-row>
    <div class="userTagBox">
      <div v-for="(item,index1) in tagsAndTypeList" :key="index1">
        <div class="userTagBox_type">
          <el-divider content-position="left">{{ item.tagTypeName }}</el-divider>
          <div class="" v-for="(tag,index2) in item.tagList" :key="index2">
            <diV @click.prevent="editTagLink(tag,index1,index2)" class="userTagBox_tag">
              <el-checkbox :value="tag.checked"></el-checkbox>
              <el-tag v-popover="tag.id" size="medium" :color="tag.backColor" :style="getFontColor(tag.fontColor)">
                {{ tag.name }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!--    添加标签框-->
    <el-dialog
      :title="tagBox.title"
      :visible.sync="tagBox.visible"
      width="30%"
      :close-on-click-modal="false"
      append-to-body
      center>
      <el-form ref="form" :model="tag" label-width="80px">
        <el-form-item label="标签名称">
          <el-input v-model="tag.name"  :maxlength="25"  show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="标签分类">
          <el-select v-model="tag.typeId" clearable placeholder="请选择">
            <el-option
              v-for="item in tagTypeList"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
          <el-button type="primary" size="mini" @click="openTagTypeBox">添加分类</el-button>
        </el-form-item>
        <el-row  >
          <el-col :span="12">
            <el-form-item label="背景色">
              <el-color-picker v-model="tag.backColor"></el-color-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="字体色">
              <el-color-picker v-model="tag.fontColor"></el-color-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="是否展示">
          <el-switch v-model="tag.showFlag" active-value="0" inactive-value="1"></el-switch>
        </el-form-item>
        <el-form-item label="效果展示" v-show="tag.name">
          <el-tag   size="medium" :color="tag.backColor" :style="getFontColor(tag.fontColor)">{{tag.name}}</el-tag>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="confirmTag">确认</el-button>
          <el-button @click="tagBox.visible = false">取消</el-button>
        </el-form-item>
      </el-form>

      <!--     嵌套添加分类框-->
      <el-dialog
        width="30%"
        title="添加分类"
        :visible.sync="tagTypeBoxVisible"
        :close-on-click-modal="false"
        append-to-body>
        <el-form ref="form" :model="tagType" label-width="80px">
          <el-form-item label="分类名称">
            <el-input v-model="tagType.name"  :maxlength="25"  show-word-limit></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="addTagType">立即创建</el-button>
            <el-button @click="tagTypeBoxVisible = false">取消</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </el-dialog>

  </div>
</template>

<script>
import {
  addAttention,
  addObj as addTag,
  getSysList as getTagList,
  getTagAndType,
  putObj as putTag
} from "@/api/viewgen/wxgoodstag";
import {getList as getWxAppList} from "@/api/wxmp/wxapp";
import {addObj as addTagType, getList as getTagTypeList} from "@/api/viewgen/wxgoodstagtype";

export default {
  name: 'goodsTagSelect',
  props: {
    selectTagList: {type: Object | Array},
    appId: {type: Object | Array},
  },
  watch: {
    selectTagList(val, oldVal) {
      this.getTagAndType();
    },
    appId(newVal,oldVal){
      if(newVal!=oldVal) {
        console.log("id改了 ",this.appId)
        this.selectedAppIdDisabled =true;
        this.selectedAppId = this.appId;
      }
    }
  },
  data() {
    return {
      selectedAppIdDisabled: false,
      selectedAppId: '',
      wxAppList: [],
      tagsAndTypeList: [],
      selectList: [],
      tagTypeList:[],
      tagBox: {
        title: '',
        visible: false,
        type: ''
      },
      tag:{
        fontColor: '',
        showFlag: '1',
      },
      tagTypeBoxVisible: false,//内部分类框
      tagType:{},
    }
  },
  created() {
    this.getWxApp()
  },
  mounted: function () {
  },
  computed: {},
  methods: {
    getWxApp(){
      getWxAppList({
        appType: '2'
      }).then(res => {
        let data = res.data
        this.wxAppList = data;
        //默认加载第一个公众号的素材
        console.log("加载工行综合",this.appId)
        if(!this.appId){
          this.selectedAppId= data[0].id;
        }else{
          this.selectedAppId = this.appId;
          this.selectedAppIdDisabled =true;
        }
        this.getTagAndType();
      }).catch(() => {
      })
    },
    getFontColor(val) {
      if (!val) {
        return;
      }
      return "color:" + val;
    },
    /**
     * 分类和标签的拿取
     */
    getTagAndType() {
      // console.log("分类和标签的拿取",this.selectedAppId)
      getTagAndType({appId:this.selectedAppId}).then(res => {
        this.tagsAndTypeList = res.data.data;
        this.changeTagList();
      })
    },
    //编辑所选标签
    editTagLink(tag, index1, index2) {
      if (tag.checked) {
        for (let i = 0; i < this.selectTagList.length; i++) {
          if (this.selectTagList[i].id == tag.id) {
            this.selectTagList.splice(i, 1);
            break;
          }
        }
      } else {
        this.selectTagList.push(tag);
      }
    },
    //得到标签和分类
    changeTagList(){
      for (let i in this.selectTagList) {
        for (let j in this.tagsAndTypeList) {
          for (let k in this.tagsAndTypeList[j].tagList) {
            if (this.selectTagList[i].id == this.tagsAndTypeList[j].tagList[k].id) {
              this.tagsAndTypeList[j].tagList[k].checked = true;
            }
          }
        }
      }
    },
    //打开新增/编辑标签框
    openTagBox(type,obj) {
      if(type == 'add') {
        this.tag = {
          name: "",
          backColor: '#409eff',
          fontColor: '#fff',
          showFlag: "0",
        }
        this.tagBox.type = "add";
        this.tagBox.title = "添加标签";
        this.getTagTypeList();
      }
      if(type == 'put'){
        this.tagBox.type = "put";
        this.tagBox.title = "修改标签";
        this.tag = {
          id: obj.id,
          name: obj.name,
          backColor: obj.backColor,
          fontColor: obj.fontColor,
          typeId: obj.typeId,
          showFlag: obj.showFlag,
        }
      }
      this.tagBox.visible = true;
    },
    // 添加标签
    confirmTag(){
      if (!this.tag.name) {
        this.$message.warning("请填写标签名称");
        return;
      }
      if (!this.tag.typeId) {
        this.$message.warning("请选择标签类型");
        return;
      }
      if(this.tagBox.type =='add') {
        console.log("保存参数", this.tag)
        addTag({
          name: this.tag.name,
          backColor: this.tag.backColor,
          fontColor: this.tag.fontColor,
          typeId: this.tag.typeId,
          showFlag: this.tag.showFlag,
        }).then(res => {
          if (res.data.code == 0) {
            this.$message.success("添加成功")
            this.getTagAndType();
            this.tagBox.visible = false;
          }
        }).catch()
      }
      if(this.tagBox.type =='put') {
        console.log("保存参数", this.tag)
        putTag({
          id: this.tag.id,
          name: this.tag.name,
          backColor: this.tag.backColor,
          fontColor: this.tag.fontColor,
          typeId: this.tag.typeId,
          showFlag: this.tag.showFlag,
        }).then(res => {
          if (res.data.code == 0) {
            this.$message.success("修改成功")
            // this.getTagList();
            this.tagBox.visible = false;
          }
        }).catch();
      }
    },
    //加载标签类别
    getTagTypeList() {
      getTagTypeList({
        appId:this.appId
      }).then(res=>{
        this.tagTypeList = res.data.data;
        // this.getTagList();
      }).catch(err=>{
        console.log(err)
      })
    },
    //打开内部分类框
    openTagTypeBox() {
      this.tagType = {};
      this.tagTypeBoxVisible = true;
    },
    // 添加分类
    addTagType() {
      if (!this.tagType.name) {
        this.$message.warning("请填写分类名称");
        return;
      }
      addTagType({
        name: this.tagType.name,
        appId:this.selectedAppId
      }).then(res => {
        if (res.data.code == 0) {
          this.$message.success("添加成功")
          this.getTagTypeList();
          this.tagTypeBoxVisible = false;
        }
      }).catch()
    },
  }
}
</script>

<style lang="scss" scoped>
.userTagBox {
  height: 400px;
  overflow: scroll
}

.userTagBox_type {
  overflow: auto;
}

.userTagBox_tag {
  display: block;
  float: left;
  padding: 10px;
}
</style>
