<!DOCTYPE html>
<html>
<head>
    <title>后台界面</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no">
    <meta name="referrer" content="no-referrer"/>
    <meta http-equiv="X-UA-Compatible" content="chrome=1" />
    <link rel="stylesheet" href="<%= BASE_URL %>cdn/iconfont/1.0.0/index.css">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <style>
        html,
        body,
        #app {
            height: 100%;
            margin: 0;
            padding: 0;
        }
        .gocreateone-home {
            background-color: #409EFF;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .gocreateone-home__main {
            user-select: none;
            width: 100%;
            flex-grow: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }
        .gocreateone-home__footer {
            width: 100%;
            flex-grow: 0;
            text-align: center;
            padding: 1em 0;
        }
        .gocreateone-home__footer>a {
            font-size: 12px;
            color: #fff;
            text-decoration: none;
        }
        .gocreateone-home__loading {
            height: 32px;
            width: 32px;
            margin-bottom: 20px;
        }
        .gocreateone-home__title {
            color: #FFF;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .gocreateone-home__sub-title {
            color: #FFF;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <noscript>
        <strong>很抱歉，如果没有 JavaScript 支持，网站将不能正常工作。请启用浏览器的 JavaScript 然后继续。</strong>
    </noscript>
    <div id="app">
        <div class="gocreateone-home">
            <div class="gocreateone-home__main">
                <img class="gocreateone-home__loading" src="./svg/loading-spin.svg" alt="loading">
                <div class="gocreateone-home__title">
                    正在加载资源
                </div>
                <div class="gocreateone-home__sub-title">
                    初次加载资源可能需要较多时间，请耐心等待
                </div>
            </div>
            <div class="gocreateone-home__footer">
                <a href="https://gocreateone.com" target="_blank">
                  Copyright © 2022 www.gocreateone.com</a>
            </div>
        </div>
    </div>
    <!-- axios -->
    <script src="<%= BASE_URL %>cdn/axios/1.0.0/axios.min.js" charset="utf-8"></script>
    <!-- excl导出 -->
    <script src="<%= BASE_URL %>cdn/filesaver/FileSaver.min.js"></script>
    <script src="<%= BASE_URL %>cdn/xlsx/xlsx.full.min.js"></script>
    <!-- 地图坐标选择 -->
    <script type="text/javascript" src='https://webapi.amap.com/maps?v=1.4.11&key=7ab53b28352e55dc5754699add0ad862&plugin=AMap.PlaceSearch'></script>
    <script src="https://webapi.amap.com/ui/1.0/main.js?v=1.0.11"></script>
</body>
</html>
