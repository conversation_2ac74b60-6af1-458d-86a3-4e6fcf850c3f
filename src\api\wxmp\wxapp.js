import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/wxapp/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/weixin/wxapp',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/weixin/wxapp/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/wxapp/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/wxapp',
    method: 'put',
    data: obj
  })
}

export function getList(query) {
  return request({
    url: '/weixin/wxapp/list',
    method: 'get',
    params: query
  })
}

export function createQrCode(obj) {
  return request({
    url: '/weixin/wxapp/qrCode',
    method: 'post',
    data: obj
  })
}

export function clearQuota(obj) {
  return request({
    url: '/weixin/wxapp/quota',
    method: 'put',
    data: obj
  })
}

export function getAccessToken(query) {
  return request({
    url: '/weixin/wxapp/access-token',
    method: 'get',
    params: query
  })
}

export function getJsapiTicket(query) {
  return request({
    url: '/weixin/wxapp/jsapi-ticket',
    method: 'get',
    params: query
  })
}

export function getAuthorizerInfo(id) {
  return request({
    url: '/weixin/open/api/authorizer-info/' + id,
    method: 'get'
  })
}

export function getQrCode(query) {
  return request({
    url: '/weixin/wxapp/qrCode',
    method: 'get',
    params: query
  })
}
