// 客服聊天认识的好友列表
export const tableOption = {
  header:false,//头部显示
  dialogDrag: false,
  border: false,
  // indexLabel: '序号',
  stripe: false,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  excelBtn: false,
  printBtn: false,
  viewBtn: false,
  refreshBtn: false,
  columnBtn: false,
  page: false,
  menu:false,
  menuWidth:0,
  searchMenuSpan: 6,
  size:'small',
  column: [
    {
      label: '好友列表',
      prop: 'id',
    },
  ]
}

// 好友列表完全列表
export const tableOption2 = {
  header:false,//头部显示
  dialogDrag: false,
  border: true,
  // indexLabel: '序号',
  stripe: false,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  excelBtn: false,
  printBtn: false,
  viewBtn: false,
  refreshBtn: false,
  columnBtn: false,
  page: true,
  menu:false,
  menuWidth:0,
  searchMenuSpan: 6,
  size:'small',
  column: [
    {
      label: '头像',
      prop: 'headimgUrl',
      imgWidth: 50,
      dataType: 'string',
      type: 'upload',
      listType: 'picture-img',
      editDisplay: false,
      fixed: true
    },
    {
      label: '昵称',
      prop: 'nickName',
      width: 100,
      sortable: true,
      search: true,
      editDisplay: false,
      fixed: true
    },
    {
      label: '是否订阅',
      prop: 'subscribe',
      width: 80,
      type: 'select',
      sortable: true,
      search: true,
      editDisplay: false,
      slot: true,
      dicUrl: '/upms/dict/type/wx_subscribe'
    },
    {
      label: 'openId',
      prop: 'openId',
      width: 220,
      search: true
    },
    {
      label: 'union_id',
      prop: 'unionId',
      width: 220,
      search: true
    },
    {
      label: '关注渠道',
      prop: 'subscribeScene',
      type: 'select',
      width: 100,
      sortable: true,
      search: true,
      editDisplay: false,
      dicUrl: '/upms/dict/type/wx_subscribe_scene'
    },
    {
      label: '关注时间',
      prop: 'subscribeTime',
      type: 'datetime',
      width: 150,
      sortable: true,
      editDisplay: false
    },
    {
      label: '性别',
      prop: 'sex',
      width: 60,
      type: 'select',
      sortable: true,
      search: true,
      editDisplay: false,
      slot: true,
      dicUrl: '/upms/dict/type/wx_sex'
    },
    {
      label: '国家',
      prop: 'country',
      sortable: true,
      search: true,
      editDisplay: false,
      width: 60
    },
    {
      label: '省份',
      prop: 'province',
      sortable: true,
      editDisplay: false,
      width: 60
    },
    {
      label: '城市',
      prop: 'city',
      sortable: true,
      search: true,
      editDisplay: false,
      width: 60
    },
    {
      label: '语言',
      prop: 'language',
      sortable: true,
      editDisplay: false,
      width: 60
    },
    {
      label: '标签',
      prop: 'tagidList',
      type: 'select',
      editDisplay: false,
      searchslot: true,
      search: true,
      props: {
        label: 'name',
        value: 'id'
      }
    },
    {
      label: '备注',
      prop: 'remark'
    },
    {
      label: '二维码扫码场景',
      prop: 'qrSceneStr',
      type: 'select',
      dicUrl: '/upms/dict/type/wx_qr_scene_str',
      sortable: true,
      search: true,
      editDisplay: false
    },
    {
      label: '关注次数',
      prop: 'subscribeNum',
      width: 50,
      sortable: true,
      editDisplay: false
    },
    {
      label: '创建时间',
      prop: 'createTime',
      type: 'datetime',
      sortable: true,
      hide: true,
      editDisplay: false
    },
    {
      label: '更新时间',
      prop: 'updateTime',
      type: 'datetime',
      sortable: true,
      hide: true,
      editDisplay: false
    },
    {
      label: '取关时间',
      prop: 'cancelSubscribeTime',
      type: 'datetime',
      sortable: true,
      hide: true,
      editDisplay: false
    },
    {
      label: '最近定位',
      prop: 'latitude',
      slot: true,
      editDisplay: false,
      viewDisplay: false,
      width: 60
    }
  ]
}
