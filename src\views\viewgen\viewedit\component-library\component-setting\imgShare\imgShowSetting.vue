<!--商品分类标签-->
<template>
  <div class="pageSetting">
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">客片展示</p>
      <div slot="mainContent">
        <el-form ref="form" label-width="100px" :model="formData">
          <el-divider>基础属性</el-divider>
          <el-form-item label="上边界">
            <el-input v-model="formData.pageMarginTop" size="mini" type="number" style="margin-top: 5px" placeholder="距离上方组件的长度">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="下边界">
            <el-input v-model="formData.pageMarginBottom" size="mini" type="number" style="margin-top: 5px" placeholder="距离下方组件的长度">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="左边界">
            <el-input v-model="formData.pageMarginLeft" size="mini" type="number" style="margin-top: 5px" placeholder="距离左边框的长度">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="右边界">
            <el-input v-model="formData.pageMarginRight" size="mini" type="number" style="margin-top: 5px" placeholder="距离右边框的长度">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-divider>内容设置</el-divider>
          <el-form-item  label="背景颜色">
              <el-input v-model="formData.imgBackGround" size="mini" >
                <template slot="append">
                  <el-color-picker size="mini" v-model="formData.imgBackGround"></el-color-picker>
                </template>
              </el-input>
          </el-form-item>
          <el-form-item label="显示宽度" >
            <el-input-number  v-model="formData.width"  size="mini" :min="0" :max="100"></el-input-number> %
          </el-form-item>
          <el-form-item  label="图片上距">
            <el-input v-model="formData.imgPaddingTop" size="mini"  :mini="0" type="number" style="margin-top: 5px" placeholder="">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item  label="图片下距">
            <el-input v-model="formData.imgPaddingBottom" size="mini"  :mini="0" type="number" style="margin-top: 5px" placeholder="">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="客片保存">
            <el-radio-group v-model="formData.saveRules" >
              <el-radio :label="0">可以保存</el-radio>
              <el-radio :label="1">不能保存</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="显示设置">
            <el-radio-group v-model="formData.showRules" >
              <el-radio :label="0">读取全部</el-radio>
              <el-radio :label="1">指定读取</el-radio>
            </el-radio-group>
          </el-form-item>
          <diV v-show="formData.showRules==1">
            <el-alert
              :closable="false"
              title="提示："
              type="success"
              description="在已签列表里面上传的客片顺序，指定第几张至第几张结束；解决的是将客户的照片通过当前设置分布到各个地方。">
            </el-alert>
            <el-form-item label="读取范围">
            <el-row>
              <el-col :span="10">
                <el-input-number v-model="formData.minLimit" size="mini" type="number" :min="0" placeholder="开始位置">
                </el-input-number>
              </el-col>
              <el-col :span="2" style="display: flex;justify-content: center;align-items: center;">至</el-col>
              <el-col :span="10">
                <el-input-number v-model="formData.maxLimit"  size="mini" type="number" :min="formData.minLimit"  placeholder="结束位置">
                </el-input-number>
              </el-col>
            </el-row>
          </el-form-item>
          </diV>
        </el-form>
      </div>
    </settingSlot>
<!--    <p style="display:none">{{getData}}</p>-->
  </div>
</template>

<script>
  import { mapState , mapGetters , mapMutations , mapActions } from 'vuex';

  import settingSlot from '../settingSlot'
  // import bgColorSelect from "../../pages/page-components/bgColorSelect";

  export default {
    components: { settingSlot },
    data() {
      return {
        formDataCopy : {
          pageMarginTop:0,
          pageMarginBottom:0,
          pageMarginLeft:0,
          pageMarginRight:0,
          imgBackGround:"#ffffff",
          width:100,
          imgPaddingTop:0,
          imgPaddingBottom:0,
          saveRules: 0,//是否显示
          showRules: 0,//是否显示
          minLimit: 0,
          maxLimit: 0,
        },
        formData : {}
      };
    },
    props: {
      thememobile : { type: Object | Array },
      showData:{
        type: Object,
        default: ()=> {}
      },
    },
    computed: {
      ...mapState({
        componentsList: state => state.divpage.componentsList,
        clickComIndex : state => state.divpage.clickComIndex,
      })
    },
    mounted(){
      let that = this;
      if(that.IsEmptyObj(that.showData)){

        that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      } else {
        that.formData = that.showData
      }
      console.log("111  ",this.formData)
      that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
      // that.updateData({
      //   componentsList: that.componentsList
      // })
    },
    methods: {
      ...mapMutations([
        'updateData'
      ]),
      // 删除按钮
      delBtn(index){
        let that = this;
        that.$confirm('是否删除该按钮?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText : '取消',
          type : 'warning'
        }).then(()=>{
          that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
          that.updateData({ componentsList: that.componentsList });
        }).catch(()=>{})
      },
      cancel(){
        this.$emit('cancel')
      },
      reset(){
        let that = this;
        that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
        that.componentsList[that.clickComIndex]['data'] = this.formData;
      },
      confirm(){
        this.$emit('confirm', this.formData)
      },
      delete(){
        this.$emit('delete')
      },
    },

    watch: {
      showData: {
        handler(newVal, oldVal) {
          this.formData = newVal ? newVal : this.formData;
        },
        deep: true
      },
      thememobile(){},
    }
  };
</script>
<style lang='less' scoped>

  .el-form-item{
    margin-bottom: 0;
  }
</style>
