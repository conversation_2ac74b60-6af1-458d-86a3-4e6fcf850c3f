<template>
  <div style="position: relative;">
    <div :style="{height: height}" class="qqface-container">
      <span class="qqface-wrapper" v-for="[key, value] of Object.entries(emoijs)" :key="value">
        <img src="/img/wx-emojis/qqFace.png" class="qqface"  :class="[`qqface${value}`]" @click="input(key)">
      </span>
<!--      删除按钮-->
<!--      <div class="picker-button" @click="deleteEmoji" v-if="button">-->
<!--        <svg height="24px" viewBox="0 0 24 24" width="24px" fill="#000000">-->
<!--          <path d="M0 0h24v24H0V0z" fill="none"/>-->
<!--          <path-->
<!--            d="M22 3H7c-.69 0-1.23.35-1.59.88L0 12l5.41 8.11c.********** 1.59.89h15c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H7.07L2.4 12l4.66-7H22v14zm-11.59-2L14 13.41 17.59 17 19 15.59 15.41 12 19 8.41 17.59 7 14 10.59 10.41 7 9 8.41 12.59 12 9 15.59z"/>-->
<!--        </svg>-->
<!--      </div>-->
    </div>
  </div>
</template>

<script>
const qqFaceList = {
  '[微笑]': '0',
  '[撇嘴]': '1',
  '[色]': '2',
  '[发呆]': '3',
  '[得意]': '4',
  '[流泪]': '5',
  '[害羞]': '6',
  '[闭嘴]': '7',
  '[睡]': '8',
  '[大哭]': '9',
  '[尴尬]': '10',
  '[发怒]': '11',
  '[调皮]': '12',
  '[呲牙]': '13',
  '[惊讶]': '14',
  '[难过]': '15',
  '[破涕为笑]': '16',
  '[囧]': '17',
  '[抓狂]': '18',
  '[吐]': '19',
  '[偷笑]': '20',
  '[愉快]': '21',
  '[白眼]': '22',
  '[傲慢]': '23',
  '[失望]': '24',
  '[困]': '25',
  '[惊恐]': '26',
  '[无语]': '27',
  '[憨笑]': '28',
  '[悠闲]': '29',
  '[嘿哈]': '30',
  '[咒骂]': '31',
  '[疑问]': '32',
  '[嘘]': '33',
  '[晕]': '34',
  '[脸红]': '35',
  '[衰]': '36',
  '[骷髅]': '37',
  '[敲打]': '38',
  '[再见]': '39',
  '[擦汗]': '40',
  '[抠鼻]': '41',
  '[鼓掌]': '42',
  '[捂脸]': '43',
  '[坏笑]': '44',
  '[恐惧]': '45',
  '[右哼哼]': '46',
  '[口罩]': '47',
  '[鄙视]': '48',
  '[委屈]': '49',
  '[快哭了]': '50',
  '[阴险]': '51',
  '[亲亲]': '52',
  '[笑脸]': '53',
  '[可怜]': '54',
  '[奸笑]': '55',
  '[机智]': '56',
  '[皱眉]': '57',
  '[耶]': '58',
  '[吃瓜]': '59',
  '[加油]': '60',
  '[汗]': '61',
  '[天啦]': '62',
  '[社会]': '63',
  '[旺柴]': '64',
  '[好的]': '65',
  '[打脸]': '66',
  '[哇]': '67',
  '[翻白眼]': '68',
  '[666]': '69',
  '[让我看看]': '70',
  '[叹气]': '71',
  '[苦涩]': '72',
  '[裂开]': '73',
  '[Emm]': '74',
  '[嘴唇]': '75',
  '[爱心]': '76',
  '[心碎]': '77',
  '[拥抱]': '78',
  '[强]': '79',
  '[弱]': '80',
  '[握手]': '81',
  '[胜利]': '82',
  '[抱拳]': '83',
  '[勾引]': '84',
  '[拳头]': '85',
  '[OK]': '86',
  '[合十]': '87',
  '[啤酒]': '88',
  '[咖啡]': '89',
  '[蛋糕]': '90',
  '[玫瑰]': '91',
  '[凋谢]': '92',
  '[菜刀]': '93',
  '[炸弹]': '94',
  '[便便]': '95',
  '[月亮]': '96',
  '[太阳]': '97',
  '[庆祝]': '98',
  '[礼物]': '99',
  '[红包]': '100',
  '[發]': '101',
  '[福]': '102',
  '[烟花]': '103',
  '[爆竹]': '104',
  '[猪头]': '105',
  '[跳跳]': '106',
  '[发抖]': '107',
  '[转圈]': '108'
}
// const emojisUrl = require(url)
export default {
  name: 'EmojiPicker',
  props: {
    value: {
      type: String,
      default: () => {
        return ''
      }
    },
    button: {
      type: Boolean
    },
    height: {
      type: String,
      default: 'auto'
    }
  },
  data() {
    return {
      emoijs: qqFaceList,

    }
  },
  // inject: ['emojisUrl'],
  methods: {
    input(key) {
      console.log("点击按钮",key)
      this.$emit('input', this.value + key)
    },
    deleteEmoji(text) {
      const reg = /\[[^\]]+?\]$/
      if (reg.test(text)) {
        return text.replace(reg, '')
      } else if (text) {
        return text.slice(0, -1)
      }
      this.$emit('input', deleteEmoji(this.value))
    }
  },
}
</script>

<style lang="scss">
.qqface-container {
  overflow-y: scroll;
  // position: relative;
  // height: 300px;
  .qqface-wrapper {
    display: inline-block;
    transform: scale(1.4);
    margin: 12px
  }
}

.picker-button {
  position: absolute;
  right: 20px;
  bottom: 20px;
  background: #fff;
  padding: 10px 20px 4px 20px;
  border-radius: 6px;
}

.qqface-wrapper {
  width: 24px;
  height: 24px;
  margin-bottom: -5px;
  position: relative;
  overflow: hidden;

  .qqface {
    width: 280px;
    position: absolute;

    &.qqface0 {
      clip-path: circle(126px at 12px 12px);
    }

    &.qqface1 {
      left: -36px;
      clip-path: circle(126px at 48px 12px);
    }

    &.qqface2 {
      left: -72px;
      clip-path: circle(126px at 84px 12px);
    }

    &.qqface3 {
      left: -109px;
      clip-path: circle(126px at 120px 12px);
    }

    &.qqface4 {
      left: -145px;
      clip-path: circle(126px at 158px 12px);
    }

    &.qqface5 {
      left: -182px;
      clip-path: circle(126px at 194px 12px);
    }

    &.qqface6 {
      left: -219px;
      clip-path: circle(126px at 230px 12px);
    }

    &.qqface7 {
      left: -256px;
      clip-path: circle(126px at 266px 12px);
    }

    &.qqface8 {
      top: -36px;
      clip-path: circle(126px at 12px 48px);
    }

    &.qqface9 {
      top: -36px;
      left: -36px;
      clip-path: circle(126px at 48px 48px);
    }

    &.qqface10 {
      top: -36px;
      left: -72px;
      clip-path: circle(126px at 84px 48px);
    }

    &.qqface11 {
      top: -36px;
      left: -110px;
      clip-path: circle(126px at 120px 48px);
    }

    &.qqface12 {
      top: -36px;
      left: -146px;
      clip-path: circle(126px at 158px 48px);
    }

    &.qqface13 {
      top: -36px;
      left: -182px;
      clip-path: circle(126px at 194px 48px);
    }

    &.qqface14 {
      top: -36px;
      left: -219px;
      clip-path: circle(126px at 230px 48px);
    }

    &.qqface15 {
      top: -36px;
      left: -256px;
      clip-path: circle(126px at 266px 48px);
    }

    &.qqface17 {
      top: -74px;
      clip-path: circle(126px at 12px 84px);
    }

    &.qqface18 {
      top: -74px;
      left: -36px;
      clip-path: circle(126px at 48px 84px);
    }

    &.qqface19 {
      top: -74px;
      left: -72px;
      clip-path: circle(126px at 84px 84px);
    }

    &.qqface20 {
      top: -74px;
      left: -109px;
      clip-path: circle(126px at 120px 84px);
    }

    &.qqface21 {
      top: -74px;
      left: -145px;
      clip-path: circle(126px at 158px 84px);
    }

    &.qqface22 {
      top: -74px;
      left: -182px;
      clip-path: circle(126px at 194px 84px);
    }

    &.qqface23 {
      top: -74px;
      left: -219px;
      clip-path: circle(126px at 230px 84px);
    }

    &.qqface25 {
      top: -74px;
      left: -256px;
      clip-path: circle(126px at 266px 84px);
    }

    &.qqface26 {
      top: -110px;
      clip-path: circle(126px at 12px 121px);
    }

    &.qqface28 {
      top: -110px;
      left: -36px;
      clip-path: circle(126px at 48px 121px);
    }

    &.qqface29 {
      top: -110px;
      left: -72px;
      clip-path: circle(126px at 84px 121px);
    }

    &.qqface31 {
      top: -110px;
      left: -110px;
      clip-path: circle(126px at 120px 121px);
    }

    &.qqface32 {
      top: -110px;
      left: -146px;
      clip-path: circle(126px at 158px 121px);
    }

    &.qqface33 {
      top: -110px;
      left: -182px;
      clip-path: circle(126px at 194px 121px);
    }

    &.qqface34 {
      top: -110px;
      left: -219px;
      clip-path: circle(126px at 230px 121px);
    }

    &.qqface36 {
      top: -110px;
      left: -256px;
      clip-path: circle(126px at 266px 121px);
    }

    &.qqface37 {
      top: -147px;
      clip-path: circle(126px at 12px 157px);
    }

    &.qqface38 {
      top: -147px;
      left: -36px;
      clip-path: circle(126px at 48px 157px);
    }

    &.qqface39 {
      top: -147px;
      left: -73px;
      clip-path: circle(126px at 85px 160px);
    }

    &.qqface40 {
      top: -147px;
      left: -109px;
      clip-path: circle(126px at 120px 157px);
    }

    &.qqface41 {
      top: -147px;
      left: -145px;
      clip-path: circle(126px at 158px 157px);
    }

    &.qqface42 {
      top: -147px;
      left: -183px;
      clip-path: circle(126px at 194px 157px);
    }

    &.qqface44 {
      top: -147px;
      left: -219px;
      clip-path: circle(126px at 230px 157px);
    }

    &.qqface46 {
      top: -147px;
      left: -256px;
      clip-path: circle(126px at 266px 157px);
    }

    &.qqface48 {
      top: -184px;
      clip-path: circle(126px at 12px 196px);
    }

    &.qqface49 {
      top: -184px;
      left: -36px;
      clip-path: circle(126px at 48px 196px);
    }

    &.qqface50 {
      top: -184px;
      left: -72px;
      clip-path: circle(126px at 84px 196px);
    }

    &.qqface51 {
      top: -184px;
      left: -109px;
      clip-path: circle(126px at 120px 196px);
    }

    &.qqface52 {
      top: -184px;
      left: -145px;
      clip-path: circle(126px at 158px 196px);
    }

    &.qqface54 {
      top: -184px;
      left: -182px;
      clip-path: circle(126px at 194px 196px);
    }

    &.qqface53 {
      top: -184px;
      left: -219px;
      clip-path: circle(126px at 230px 196px);
    }

    &.qqface47 {
      top: -184px;
      left: -256px;
      clip-path: circle(126px at 266px 198px);
    }

    &.qqface35 {
      top: -222px;
      clip-path: circle(126px at 12px 234px);
    }

    &.qqface16 {
      top: -222px;
      left: -36px;
      clip-path: circle(126px at 48px 234px);
    }

    &.qqface45 {
      top: -222px;
      left: -72px;
      clip-path: circle(126px at 84px 234px);
    }

    &.qqface24 {
      top: -222px;
      left: -109px;
      clip-path: circle(126px at 120px 234px);
    }

    &.qqface27 {
      top: -222px;
      left: -145px;
      clip-path: circle(126px at 158px 234px);
    }

    &.qqface30 {
      top: -222px;
      left: -182px;
      clip-path: circle(126px at 194px 234px);
    }

    &.qqface43 {
      top: -222px;
      left: -219px;
      clip-path: circle(126px at 230px 234px);
    }

    &.qqface55 {
      top: -222px;
      left: -256px;
      clip-path: circle(126px at 266px 234px);
    }

    &.qqface56 {
      top: -258px;
      clip-path: circle(126px at 12px 270px);
    }

    &.qqface57 {
      top: -258px;
      left: -36px;
      clip-path: circle(126px at 48px 270px);
    }

    &.qqface58 {
      top: -258px;
      left: -72px;
      clip-path: circle(126px at 84px 270px);
    }

    &.qqface59 {
      top: -258px;
      left: -109px;
      clip-path: circle(126px at 120px 270px);
    }

    &.qqface60 {
      top: -258px;
      left: -145px;
      clip-path: circle(126px at 158px 270px);
    }

    &.qqface61 {
      top: -258px;
      left: -182px;
      clip-path: circle(126px at 194px 270px);
    }

    &.qqface62 {
      top: -258px;
      left: -219px;
      clip-path: circle(126px at 230px 270px);
    }

    &.qqface74 {
      top: -258px;
      left: -256px;
      clip-path: circle(126px at 266px 270px);
    }

    &.qqface63 {
      top: -294px;
      clip-path: circle(126px at 12px 306px);
    }

    &.qqface64 {
      top: -294px;
      left: -36px;
      clip-path: circle(126px at 48px 306px);
    }

    &.qqface65 {
      top: -294px;
      left: -72px;
      clip-path: circle(126px at 84px 306px);
    }

    &.qqface66 {
      top: -294px;
      left: -109px;
      clip-path: circle(126px at 120px 306px);
    }

    &.qqface67 {
      top: -294px;
      left: -145px;
      clip-path: circle(126px at 158px 306px);
    }

    &.qqface68 {
      top: -294px;
      left: -182px;
      clip-path: circle(126px at 194px 306px);
    }

    &.qqface69 {
      top: -294px;
      left: -219px;
      clip-path: circle(126px at 230px 306px);
    }

    &.qqface70 {
      top: -294px;
      left: -256px;
      clip-path: circle(126px at 266px 306px);
    }

    &.qqface71 {
      top: -330px;
      clip-path: circle(126px at 12px 342px);
    }

    &.qqface72 {
      top: -330px;
      left: -36px;
      clip-path: circle(126px at 48px 342px);
    }

    &.qqface73 {
      top: -330px;
      left: -73px;
      clip-path: circle(126px at 84px 342px);
    }

    &.qqface75 {
      top: -330px;
      left: -109px;
      clip-path: circle(126px at 120px 342px);
    }

    &.qqface76 {
      top: -330px;
      left: -145px;
      clip-path: circle(126px at 158px 342px);
    }

    &.qqface77 {
      top: -330px;
      left: -182px;
      clip-path: circle(126px at 194px 342px);
    }

    &.qqface78 {
      top: -330px;
      left: -219px;
      clip-path: circle(126px at 230px 342px);
    }

    &.qqface79 {
      top: -330px;
      left: -256px;
      clip-path: circle(126px at 266px 342px);
    }

    &.qqface80 {
      top: -366px;
      clip-path: circle(126px at 12px 378px);
    }

    &.qqface81 {
      top: -366px;
      left: -36px;
      clip-path: circle(126px at 48px 378px);
    }

    &.qqface82 {
      top: -366px;
      left: -72px;
      clip-path: circle(126px at 84px 378px);
    }

    &.qqface83 {
      top: -366px;
      left: -109px;
      clip-path: circle(126px at 120px 378px);
    }

    &.qqface84 {
      top: -366px;
      left: -145px;
      clip-path: circle(126px at 158px 378px);
    }

    &.qqface85 {
      top: -366px;
      left: -182px;
      clip-path: circle(126px at 194px 378px);
    }

    &.qqface86 {
      top: -366px;
      left: -219px;
      clip-path: circle(126px at 230px 378px);
    }

    &.qqface87 {
      top: -366px;
      left: -256px;
      clip-path: circle(126px at 266px 378px);
    }

    &.qqface88 {
      top: -404px;
      clip-path: circle(126px at 12px 416px);
    }

    &.qqface89 {
      top: -404px;
      left: -36px;
      clip-path: circle(126px at 48px 416px);
    }

    &.qqface90 {
      top: -404px;
      left: -72px;
      clip-path: circle(126px at 84px 416px);
    }

    &.qqface91 {
      top: -404px;
      left: -109px;
      clip-path: circle(126px at 120px 416px);
    }

    &.qqface92 {
      top: -404px;
      left: -145px;
      clip-path: circle(126px at 158px 416px);
    }

    &.qqface93 {
      top: -404px;
      left: -182px;
      clip-path: circle(126px at 194px 416px);
    }

    &.qqface94 {
      top: -404px;
      left: -219px;
      clip-path: circle(126px at 230px 416px);
    }

    &.qqface95 {
      top: -404px;
      left: -256px;
      clip-path: circle(126px at 267px 416px);
    }

    &.qqface96 {
      top: -441px;
      clip-path: circle(126px at 12px 452px);
    }

    &.qqface97 {
      top: -441px;
      left: -36px;
      clip-path: circle(126px at 48px 452px);
    }

    &.qqface98 {
      top: -441px;
      left: -72px;
      clip-path: circle(126px at 84px 452px);
    }

    &.qqface99 {
      top: -441px;
      left: -109px;
      clip-path: circle(126px at 120px 452px);
    }

    &.qqface100 {
      top: -441px;
      left: -145px;
      clip-path: circle(126px at 158px 452px);
    }

    &.qqface101 {
      top: -441px;
      left: -182px;
      clip-path: circle(126px at 194px 452px);
    }

    &.qqface102 {
      top: -441px;
      left: -219px;
      clip-path: circle(126px at 230px 452px);
    }

    &.qqface103 {
      top: -441px;
      left: -256px;
      clip-path: circle(126px at 266px 452px);
    }

    &.qqface104 {
      top: -477px;
      clip-path: circle(126px at 12px 489px);
    }

    &.qqface105 {
      top: -477px;
      left: -36px;
      clip-path: circle(126px at 48px 489px);
    }

    &.qqface106 {
      top: -477px;
      left: -72px;
      clip-path: circle(126px at 84px 489px);
    }

    &.qqface107 {
      top: -477px;
      left: -109px;
      clip-path: circle(126px at 120px 489px);
    }

    &.qqface108 {
      top: -477px;
      left: -145px;
      clip-path: circle(126px at 158px 489px);
    }
  }

  &:after {
    content: "";
  }
}

</style>
