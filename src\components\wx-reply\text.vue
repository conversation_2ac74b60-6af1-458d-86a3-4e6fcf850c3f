<template>
    <div>
      <span slot="label"><i class="el-icon-document"></i> 文本</span>
      <el-input
        type="textarea"
        :rows="5"
        placeholder="请输入内容"
        v-model="objData.repContent">
      </el-input>
    </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import store from "@/store"

  export default {
    name: "wxReplyText",
    props: {
      objData:{
        type: Object
      },
    },
    data() {
      return {
        tempPlayerObj: {
          type: '2'
        },
      }
    },
    computed: {
      ...mapGetters(['permissions'])
    },
    methods:{
    }
  };
</script>

<style lang="scss" scoped>
</style>
