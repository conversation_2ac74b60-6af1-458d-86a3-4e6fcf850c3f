<!--商品分类标签-->
<template>
  <div class="cuttingLineSetting">
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">作品选档</p>
      <div slot="mainContent">
        <el-form ref="form" label-width="100px" :model="formData">
          <el-divider>基础属性</el-divider>
          <el-form-item label="组件上边距">
            <el-input v-model="formData.paddingTop" size="mini" type="number" style="margin-top: 5px"
                      placeholder="页面下边距">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="组件下边距">
            <el-input v-model="formData.paddingBottom" size="mini" type="number" style="margin-top: 5px"
                      placeholder="页面下边距">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
<!--          <el-form-item label="背景颜色">-->
<!--            <bg-color-select :thememobile="thememobile" :bgValue="formData.background"-->
<!--                             @onChange="formData.background = $event"></bg-color-select>-->
<!--          </el-form-item>-->
          <el-divider>档期设置</el-divider>
          <el-form-item label="已禁用显示">
            <el-input v-model="formData.disableText" size="mini" placeholder="禁用显示文本"></el-input>
          </el-form-item>
          <el-form-item label="未禁用显示">
            <el-input v-model="formData.unDisableText" size="mini" placeholder="未禁用显示文本"></el-input>
          </el-form-item>
          <el-form-item label="日期选中颜色">
            <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
              <el-input v-model="formData.selectedTabColor" size="small" style="margin-top: 5px">
                <template slot="append">
                  <el-color-picker size="mini" v-model="formData.selectedTabColor"></el-color-picker>
                </template>
              </el-input>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="时间选中颜色">
            <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
              <el-input v-model="formData.selectedItemColor" size="small" style="margin-top: 5px">
                <template slot="append">
                  <el-color-picker size="mini" v-model="formData.selectedItemColor"></el-color-picker>
                </template>
              </el-input>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="预约日期范围">
            <el-date-picker
              value-format="yyyy-MM-dd"
              type="daterange"
              size="mini"
              v-model="formData.dateRange"
              :picker-options="dateRangeOptions">
            </el-date-picker>
            <!--            <el-button type="primary" size="mini" icon="el-icon-edit" @click="openCalendar" circle></el-button>-->
          </el-form-item>
          <el-form-item label="最长显示天数">
            <el-input-number v-model="formData.maxDisplayDays" :min="1" size="small" style="margin-top: 5px"></el-input-number>
            <span style="margin-left: 5px">天</span>
          </el-form-item>
          <el-form-item label="剔除可选日期">
            <el-button type="primary" size="mini" @click="unableDateVisible = true">添加</el-button>
            <div v-for="(item,index) in  formData.unableDateList" :key="'unableDate'+index">
              <div style="display: inline-block;width: 100px;border: 1px solid #dddfe5;text-align: center;"
                   v-if="item.type == 1">{{ item.rule == 1 ? "法定节假日" : "周末" }}
              </div>
              <el-date-picker
                v-if="item.type == 2"
                :disabled="true"
                size="mini"
                type="daterange"
                value-format="yyyy-MM-dd"
                :value="item.date">
              </el-date-picker>
              <el-button type="danger" size="mini" @click="delUnableDate(index)">删除</el-button>
            </div>
          </el-form-item>
          <el-form-item label="预约时间范围">
            <div>
              上午
              <el-time-picker
                size="mini"
                is-range
                value-format="HH:mm:ss"
                :picker-options="{
                   selectableRange: '00:00:00 - 12:00:00'
                }"
                v-model="formData.timeRangeAm"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                @change="confirmTime"
                placeholder="选择时间范围">
              </el-time-picker>
            </div>
            <div>
              下午
              <el-time-picker
                size="mini"
                is-range
                :minTime="'12:00:00'"
                value-format="HH:mm:ss"
                v-model="formData.timeRangePm"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                @change="confirmTime"
                placeholder="选择时间范围">
              </el-time-picker>
            </div>
          </el-form-item>
          <el-form-item label="剔除可选时间">
            <el-button type="primary" size="mini" @click="unableTimeVisible = true">添加</el-button>
            <div v-for="(item,index) in  formData.unableTimeList" :key="'unableTime'+index">
              <el-time-picker
                :disabled="true"
                size="mini"
                is-range
                value-format="HH:mm:ss"
                :value="item">
              </el-time-picker>
              <el-button type="danger" size="mini" @click="delUnableTime(index)">删除</el-button>
            </div>
          </el-form-item>
          <el-form-item label="每段时间长度">
                <el-input-number v-model="formData.hourInterval" :min="0" :max="23" size="small"
                                 style="margin-top: 5px">
                </el-input-number>
                小时
              <el-input-number v-model="formData.minuteInterval" :min="0" :max="60" :step="5" size="small"
                                 style="margin-top: 5px">
              </el-input-number>
                分钟
          </el-form-item>
          <el-form-item label="档期可选次数">
                <el-input-number v-model="formData.maxSelectCount" :min="0" size="small"
                                 style="margin-top: 5px">
                </el-input-number>
                次
          </el-form-item>

          <el-divider>档期预览</el-divider>
          <el-form-item label="档期设置">
            <div class="schedule-preview">
              <div class="schedule-container">
                <div class="date-column">
                  <div class="date-title">预约日期</div>
                  <div class="date-list">
                    <div
                      v-for="(date, index) in previewDates"
                      :key="'date-'+index"
                      :class="['date-item', {active: selectedDateIndex === index}]"
                      @click="selectDate(index)">
                      <div class="date-day">{{date.date}}</div>
                      <div class="date-week">{{date.week}}</div>
                    </div>
                  </div>
                </div>
                <div class="time-column">
                  <div class="time-title">可选时间</div>
                  <div class="time-list">
                    <div
                      v-for="(timeSlot, index) in currentTimeSlots"
                      :key="'time-'+index"
                      :class="['time-item', {disabled: timeSlot.disabled}]">
                      <div class="time-item-row">
                        <span class="time-text">{{timeSlot.time}}</span>
                        <span v-if="timeSlot.disabled" class="disabled-text">{{formData.disableText}}</span>
                        <span v-else class="available-text">{{formData.unDisableText}}</span>
                      </div>
                      <div v-if="!timeSlot.disabled" class="appointment-count">
                        <span>可预约次数：</span>
                        <el-input-number
                          v-model="timeSlot.availableCount"
                          :min="0"
                          :max="99"
                          size="mini"
                          controls-position="right"
                          @change="updateTimeSlotCount(index, $event)">
                        </el-input-number>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-form>


        <!-- 日历弹出框 roomBoxVisible-->
        <el-dialog
          :append-to-body="true"
          title="档期选择"
          :visible.sync="calendarVisible"
          width="60%"
          center>
          <auction-calendar></auction-calendar>
        </el-dialog>
        <!-- 剔除可选日期 unableDateVisible-->
        <el-dialog
          :append-to-body="true"
          title="剔除可选日期"
          :visible.sync="unableDateVisible"
          @close="unableDateForm ={};"
          center>
          <div>
            <el-form :model="unableDateForm" label-width="100px"
                     class="demo-ruleForm">
              <el-form-item label="剔除类型">
                <el-radio-group v-model="unableDateForm.type">
                  <el-radio :label="1">特定日期</el-radio>
                  <el-radio :label="2">具体日期</el-radio>
                </el-radio-group>
              </el-form-item>
              <div v-if="unableDateForm.type==1">
                <el-form-item label="特定类型">
                  <el-radio-group v-model="unableDateForm.rule">
<!--                    <el-radio :label="1">法定节假日</el-radio>-->
                    <el-radio :label="2">周末</el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
              <el-form-item v-if="unableDateForm.type==2" label="具体日期" prop="region">
                <el-date-picker
                  size="mini"
                  type="daterange"
                  value-format="yyyy-MM-dd"
                  v-model="unableDateForm.date"
                  range-separator="至"
                  :picker-options="unableDateRangeOptions"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  placeholder="选择日期范围">
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="addUnableDate">确认</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-dialog>
        <!-- 剔除可选时间 unableTimeVisible-->
        <el-dialog
          :append-to-body="true"
          title="剔除可选时间"
          :visible.sync="unableTimeVisible"
          @close="unableTimeForm ={}"
          center>
          <div>
            <el-form :model="unableTimeForm" label-width="100px"
                     class="demo-ruleForm">
              <el-form-item label="具体时间">
                <el-time-picker
                  size="mini"
                  is-range
                  value-format="HH:mm:ss"
                  v-model="unableTimeForm.time"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  placeholder="选择时间范围">
                </el-time-picker>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="addUnableTime()">确认</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-dialog>
      </div>
    </settingSlot>
  </div>

</template>
<script>

import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';
import settingSlot from '../settingSlot';
import bgColorSelect from "../../pages/page-components/bgColorSelect";
import auctionCalendar from '@/views/viewgen/auctioncalendar/index'
import {dateFormat} from "@/util/date";

export default {
  components: {settingSlot, bgColorSelect, auctionCalendar},
  data() {
    return {
      unableDateForm: {},
      unableTimeForm: {},
      calendarVisible: false,
      unableTimeVisible: false,
      unableDateVisible: false,
      dateFlag: false,
      dateRange: [],
      selectedDateIndex: 0, // 当前选中的日期索引
      previewDates: [], // 预览的日期列表
      currentTimeSlots: [], // 当前选中日期的时间段列表
      dateRangeOptions: {
        disabledDate(time) {
          return time.getTime() < (Date.now() - (24 * 60 * 60 * 1000))
        },
      },
      //选择剔除日期参数
      unableDateRangeOptions: {
        disabledDate(time) {
          return time.getTime() < (Date.now() - (24 * 60 * 60 * 1000))
        },
      },
      formDataCopy: {
        paddingTop: 10,
        paddingBottom: 10,
        // background: "#E88181",
        isSection: false,//是否预约时间段
        isMultiple: false,//是否多选
        disableText: '已约满', //禁用显示的文本
        unDisableText: '可以约', //未禁用显示的文本
        hourInterval: 1, //时间间隔，小时为单位
        minuteInterval: 0, //时间间隔，分钟为单位
        selectedTabColor: '#27bb25',// 日期栏选中的颜色
        selectedItemColor: '#27bb25',// 时间选中的颜色
        dateRange: '',//日期范围
        maxDisplayDays: 30,//最长显示天数
        beginTime: '08:00:00',//开始时间
        endTime: '18:00:00',//结束时间
        appointTime: '',
        maxSelectCount: 1, //每个档期可选次数
        timeRangeAm: [new Date(2016, 8, 10, 0, 0), new Date(2016, 9, 10, 12, 0)],
        timeRangePm: [new Date(2016, 8, 10, 13, 0), new Date(2016, 9, 10, 18, 0)],
        shopList: [],//场地选择
        unableDateList: [],//剔除日期list
        unableTimeList: [],//剔除时间list
        disableTimeSlot: {
          begin_time: '17:00:00',
          end_time: '18:00:00'
        },
        scheduledData: {}, // 存储所有日期和时间段的预约次数信息
      },
      formData: {}
    };
  },
  props: {
    thememobile: {type: Object | Array},
    showData: {
      type: Object,
      default: () => {
      }
    },
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
      clickComIndex: state => state.divpage.clickComIndex,
    })
  },
  mounted() {
    let that = this;
    if (that.IsEmptyObj(that.showData)) {
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
    } else {
      that.formData = that.showData
      // 确保scheduledData存在
      if (!that.formData.scheduledData) {
        that.$set(that.formData, 'scheduledData', {});
      }
    }
    that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
    // that.updateData({
    //   componentsList: that.componentsList
    // })

    // 初始化档期预览数据
    this.$nextTick(() => {
      this.generatePreviewDates();
    });
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    // 删除按钮
    delBtn(index) {
      let that = this;
      that.$confirm('是否删除该按钮?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
        that.updateData({componentsList: that.componentsList});
      }).catch(() => {
      })
    },
    // 选择日期
    selectDate(index) {
      this.selectedDateIndex = index;
      this.generateTimeSlots();
    },
    // 生成预览日期列表
    generatePreviewDates() {
      this.previewDates = [];
      if (!this.formData.dateRange || this.formData.dateRange.length !== 2) {
        return;
      }

      const startDate = new Date(this.formData.dateRange[0]);
      const endDate = new Date(this.formData.dateRange[1]);
      const maxDays = this.formData.maxDisplayDays || 30;

      // 限制最大显示天数
      let currentDate = new Date();
      let dayCount = 0;

      while (currentDate <= endDate) {
        const dateStr = this.formatDate(currentDate);
        const dayOfWeek = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][currentDate.getDay()];

        // 检查是否在剔除日期列表中
        const isDisabled = this.isDateDisabled(currentDate);

        if (!isDisabled) {
          this.previewDates.push({
            date: dateStr,
            week: dayOfWeek,
            label: `${dateStr} ${dayOfWeek}`
          });
          dayCount++;
        }

        currentDate.setDate(currentDate.getDate() + 1);
      }

      // 确保 scheduledData 存在
      if (!this.formData.scheduledData) {
        this.$set(this.formData, 'scheduledData', {});
      }

      // 默认选中第一个日期
      this.selectedDateIndex = 0;
      this.generateTimeSlots();
    },
    // 检查日期是否被禁用
    isDateDisabled(date) {
      if (!this.formData.unableDateList || !this.formData.unableDateList.length) {
        return false;
      }

      const dayOfWeek = date.getDay();
      const dateStr = this.formatDate(date);

      for (const unableDate of this.formData.unableDateList) {
        // 类型1: 特定日期类型
        if (unableDate.type === 1) {
          // 规则2: 周末
          if (unableDate.rule === 2 && (dayOfWeek === 0 || dayOfWeek === 6)) {
            return true;
          }
        }
        // 类型2: 具体日期范围
        else if (unableDate.type === 2 && unableDate.date && unableDate.date.length === 2) {
          const startDisabled = unableDate.date[0];
          const endDisabled = unableDate.date[1];

          if (dateStr >= startDisabled && dateStr <= endDisabled) {
            return true;
          }
        }
      }

      return false;
    },
    // 生成时间段列表
    generateTimeSlots() {
      this.currentTimeSlots = [];
      if (this.selectedDateIndex < 0 || !this.previewDates[this.selectedDateIndex]) {
        return;
      }

      const selectedDate = this.previewDates[this.selectedDateIndex].date;

      // 确保当前日期在 scheduledData 中有对应的数据结构
      if (!this.formData.scheduledData) {
        this.$set(this.formData, 'scheduledData', {});
      }

      if (!this.formData.scheduledData[selectedDate]) {
        this.$set(this.formData.scheduledData, selectedDate, {});
      }

      // 上午时间段
      if (this.formData.timeRangeAm && this.formData.timeRangeAm.length === 2) {
        this.generateTimeSlotsForRange(selectedDate, this.formData.timeRangeAm[0], this.formData.timeRangeAm[1]);
      }

      // 下午时间段
      if (this.formData.timeRangePm && this.formData.timeRangePm.length === 2) {
        this.generateTimeSlotsForRange(selectedDate, this.formData.timeRangePm[0], this.formData.timeRangePm[1]);
      }
    },
    // 为指定时间范围生成时间段
    generateTimeSlotsForRange(date, startTime, endTime) {
      if (!startTime || !endTime) return;

      // 解析开始和结束时间
      const [startHour, startMinute] = startTime.split(':').map(Number);
      const [endHour, endMinute] = endTime.split(':').map(Number);

      // 设置起始时间
      const startDateTime = new Date(`${date} ${startTime}`);
      const endDateTime = new Date(`${date} ${endTime}`);

      // 获取时间间隔（小时和分钟）
      const hourInterval = this.formData.hourInterval || 0;
      const minuteInterval = this.formData.minuteInterval || 0;

      // 计算总分钟间隔
      const totalMinuteInterval = hourInterval * 60 + minuteInterval;
      if (totalMinuteInterval <= 0) return;

      // 生成时间段
      let currentTime = new Date(startDateTime);
      while (currentTime < endDateTime) {
        const nextTime = new Date(currentTime.getTime() + totalMinuteInterval * 60 * 1000);

        // 确保不超过结束时间
        if (nextTime > endDateTime) {
          break;
        }

        // 格式化时间
        const timeSlotKey = `${this.formatTime(currentTime)}-${this.formatTime(nextTime)}`;
        const isDisabled = this.isTimeSlotDisabled(currentTime, nextTime);

        // 从保存的数据中获取可预约次数，如果没有则使用默认值
        let availableCount = this.formData.maxSelectCount;
        if (this.formData.scheduledData[date] && this.formData.scheduledData[date][timeSlotKey] !== undefined) {
          availableCount = this.formData.scheduledData[date][timeSlotKey];
        }

        const timeSlot = {
          time: timeSlotKey,
          startTime: this.formatTime(currentTime),
          endTime: this.formatTime(nextTime),
          disabled: isDisabled,
          availableCount: availableCount
        };

        this.currentTimeSlots.push(timeSlot);

        // 移动到下一个时间段
        currentTime = nextTime;
      }
    },
    // 检查时间段是否被禁用
    isTimeSlotDisabled(startTime, endTime) {
      if (!this.formData.unableTimeList || !this.formData.unableTimeList.length) {
        return false;
      }

      const startTimeStr = this.formatTime(startTime);
      const endTimeStr = this.formatTime(endTime);

      for (const unableTime of this.formData.unableTimeList) {
        if (!unableTime || unableTime.length !== 2) continue;

        const unableStartTime = unableTime[0];
        const unableEndTime = unableTime[1];

        // 检查时间段是否有重叠
        if (
          (startTimeStr >= unableStartTime && startTimeStr < unableEndTime) ||
          (endTimeStr > unableStartTime && endTimeStr <= unableEndTime) ||
          (startTimeStr <= unableStartTime && endTimeStr >= unableEndTime)
        ) {
          return true;
        }
      }

      return false;
    },
    // 格式化日期为 YYYY-MM-DD
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    // 格式化时间为 HH:MM
    formatTime(date) {
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${hours}:${minutes}`;
    },
    cancel() {
      this.$emit('cancel')
    },
    reset() {
      let that = this;
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      that.componentsList[that.clickComIndex]['data'] = this.formData;
    },
    confirm() {
      // 确保scheduledData存在于formData中
      if (!this.formData.scheduledData) {
        this.$set(this.formData, 'scheduledData', {});
      }

      // 提交表单数据，包含scheduledData
      this.$emit('confirm', this.formData)
    },
    // 获取档期数据的JSON格式，用于提交到接口
    getScheduleData() {
      const scheduleData = [];

      // 如果没有scheduledData，返回空数组
      if (!this.formData.scheduledData) {
        return scheduleData;
      }

      // 遍历所有日期
      for (const date in this.formData.scheduledData) {
        const timeSlots = this.formData.scheduledData[date];

        // 遍历该日期的所有时间段
        for (const timeSlot in timeSlots) {
          const [startTime, endTime] = timeSlot.split('-');
          const availableCount = timeSlots[timeSlot];

          // 添加到结果数组
          scheduleData.push({
            date: date,
            startTime: startTime,
            endTime: endTime,
            availableCount: availableCount
          });
        }
      }

      return scheduleData;
    },
    // 从接口数据回填档期设置
    setScheduleData(scheduleData) {
      if (!Array.isArray(scheduleData) || scheduleData.length === 0) {
        return;
      }

      // 确保scheduledData存在
      if (!this.formData.scheduledData) {
        this.$set(this.formData, 'scheduledData', {});
      }

      // 遍历接口返回的数据
      scheduleData.forEach(item => {
        const { date, startTime, endTime, availableCount } = item;
        const timeSlot = `${startTime}-${endTime}`;

        // 确保该日期的数据结构存在
        if (!this.formData.scheduledData[date]) {
          this.$set(this.formData.scheduledData, date, {});
        }

        // 设置时间段的可预约次数
        this.$set(this.formData.scheduledData[date], timeSlot, availableCount);
      });

      // 重新生成预览数据
      this.generatePreviewDates();
    },
    delete() {
      this.$emit('delete')
    },
    openCalendar() {
      this.calendarVisible = true
    },
    confirmTime(e) {
      console.log(e)
      this.formData.beginTime = this.timeRange[0];
      this.formData.endTime = this.timeRange[1];
    },
    addUnableDate() {
      if (!this.unableDateForm.type) {
        this.$message.warning("请选择剔除  类型");
        return
      }
      if (this.unableDateForm.type == 1 && (!this.unableDateForm.rule)) {
        this.$message.warning("请选择特定类型");
        return
      } else if (this.unableDateForm.type == 2 && (!this.unableDateForm.date)) {
        this.$message.warning("请填具体日期");
        return
      }
      for (let i = 0; i < this.formData.unableDateList.length; i++) {
        if (this.unableDateForm.type == 1 && this.unableDateForm.type == this.formData.unableDateList[i].type && this.unableDateForm.rule == this.formData.unableDateList[i].rule) {
          this.$message.warning("请不要添加重复类型");
          return;
        }else if (this.unableDateForm.type == 2 ) {
          for (let j = 0; j < this.formData.unableDateList.length; j++) {
            let o = this.formData.unableDateList[j];
            if(o.type==2 &&
              (
                (  new Date(this.unableDateForm.date[0]) > new Date(o.date[0]).getTime() && new Date(this.unableDateForm.date[0])< new Date(o.date[1]).getTime())
                  ||
                (  new Date(this.unableDateForm.date[1]) > new Date(o.date[0]).getTime() && new Date(this.unableDateForm.date[1])< new Date(o.date[1]).getTime())
              )
            ){
              this.$message.warning("请不要添加重复日期");
              return false;
            }
          }
        }
      }

      this.unableDateVisible = false;
      let obj = {
        type: this.unableDateForm.type,
        rule: this.unableDateForm.rule,
        date: this.unableDateForm.date,
      }
      this.formData.unableDateList.push(obj)
    },
    delUnableDate(index) {
      this.formData.unableDateList.splice(index, 1);
    },
    getUnableDateRangeOptions(time) {
      return time.getTime() < (Date.now() - (24 * 60 * 60 * 1000))
    },
    addUnableTime() {
      if (!this.unableTimeForm.time) {
        this.$message.warning("请完善具体时间");
        return
      }
      let date = dateFormat(new Date()).split(" ")[0];
      for (let j = 0; j < this.formData.unableTimeList.length; j++) {
        let o = this.formData.unableTimeList[j];
        if(
          (  new Date(date+" "+ this.unableTimeForm.time[0]) > new Date(date+" "+ o[0]).getTime() && new Date(date+" "+ this.unableTimeForm.time[0])< new Date(date+" "+ o[1]).getTime())
          ||
          (  new Date(date+" "+ this.unableTimeForm.time[1]) > new Date(date+" "+ o[0]).getTime() && new Date(date+" "+ this.unableTimeForm.time[1])< new Date(date+" "+ o[1]).getTime())
        ){
          this.$message.warning("请不要添加重复时间");
          return false;
        }
      }

      this.formData.unableTimeList.push(this.unableTimeForm.time);
      this.unableTimeVisible = false;
    },
    delUnableTime(index) {
      this.formData.unableTimeList.splice(index, 1);
    },
    // 更新时间段可预约次数
    updateTimeSlotCount(index, value) {
      if (index >= 0 && index < this.currentTimeSlots.length) {
        const timeSlot = this.currentTimeSlots[index];
        const selectedDate = this.previewDates[this.selectedDateIndex].date;

        // 更新当前视图中的数据
        timeSlot.availableCount = value;

        // 确保数据结构存在
        if (!this.formData.scheduledData) {
          this.$set(this.formData, 'scheduledData', {});
        }

        if (!this.formData.scheduledData[selectedDate]) {
          this.$set(this.formData.scheduledData, selectedDate, {});
        }

        // 保存到持久化数据结构中
        this.$set(this.formData.scheduledData[selectedDate], timeSlot.time, value);
      }
    },
  },
  watch: {
    showData: {
      handler(newVal, oldVal) {
        this.formData = newVal ? newVal : this.formData;
      },
      deep: true
    },
    thememobile() {
    },
    'formData.dateRange': {
      handler() {
        this.generatePreviewDates();
      },
      deep: true
    },
    'formData.maxDisplayDays': {
      handler() {
        this.generatePreviewDates();
      }
    },
    'formData.unableDateList': {
      handler() {
        this.generatePreviewDates();
      },
      deep: true
    },
    'formData.timeRangeAm': {
      handler() {
        this.generateTimeSlots();
      },
      deep: true
    },
    'formData.timeRangePm': {
      handler() {
        this.generateTimeSlots();
      },
      deep: true
    },
    'formData.hourInterval': {
      handler() {
        this.generateTimeSlots();
      }
    },
    'formData.minuteInterval': {
      handler() {
        this.generateTimeSlots();
      }
    },
    'formData.unableTimeList': {
      handler() {
        this.generateTimeSlots();
      },
      deep: true
    }
  }
};
</script>
<style lang='less' scoped>

.el-form-item {
  margin-bottom: 0;
}

.schedule-preview {
  width: 100%;
  margin-top: 10px;

  .schedule-container {
    display: flex;
    border: 1px solid #EBEEF5;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .date-column {
      width: 100px;
      border-right: 1px solid #EBEEF5;

      .date-title {
        height: 36px;
        line-height: 36px;
        text-align: center;
        font-weight: bold;
        background-color: #f5f7fa;
        border-bottom: 1px solid #EBEEF5;
      }

      .date-list {
        height: 800px;
        overflow-y: auto;

        .date-item {
          padding: 10px 5px;
          text-align: center;
          cursor: pointer;
          border-bottom: 1px solid #EBEEF5;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          .date-day {
            font-size: 13px;
            font-weight: 500;
            margin-bottom: 0;
            line-height: 1.2;
          }

          .date-week {
            font-size: 12px;
            color: #909399;
            line-height: 1.2;
          }

          &:hover {
            background-color: #f5f7fa;
          }

          &.active {
            background-color: #ecf5ff;
            color: #409EFF;

            .date-week {
              color: #409EFF;
            }
          }
        }
      }
    }

    .time-column {
      flex: 1;

      .time-title {
        height: 36px;
        line-height: 36px;
        text-align: center;
        font-weight: bold;
        background-color: #f5f7fa;
        border-bottom: 1px solid #EBEEF5;
      }

      .time-list {
        height:800px;
        padding: 8px;
        overflow-y: auto;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: flex-start;

        .time-item {
          width: calc(50% - 10px);
          margin: 0 5px 10px;
          padding: 5px 15px;
          border: 1px solid #EBEEF5;
          border-radius: 4px;
          display: flex;
          flex-direction: column;

          &.disabled {
            background-color: #f5f7fa;
            color: #909399;
          }

          .time-item-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
          }

          .time-text {
            font-size: 13px;
          }

          .disabled-text {
            color: #F56C6C;
            font-size: 12px;
          }

          .available-text {
            color: #67C23A;
            font-size: 12px;
          }

          .appointment-count {
            margin-top: 3px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            width: 100%;

            .el-input-number {
              width: 90px;
            }
          }
        }
      }
    }
  }
}
</style>
