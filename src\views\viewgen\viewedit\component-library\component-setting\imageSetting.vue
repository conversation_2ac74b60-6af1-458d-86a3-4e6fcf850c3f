<template>
  <div class="compSetting">
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">单列图片</p>
      <div slot="hint">
<!--        <div class="tips-class " >提示：图片宽度自适应100%，高度建议不要过高≤300。</div>-->
      </div>
      <div slot="mainContent">
        <el-form ref="form"  label-width="100px" :model="formData">
          <el-divider>基础属性</el-divider>
          <el-form-item label="圆角设置">
            <el-slider v-model="formData.borderRadius" :max="40"></el-slider>
          </el-form-item>
          <el-form-item label="图片">
            <MaterialList :value="formData.imageUrl?[formData.imageUrl]:[]"  @sureSuccess="formData.imageUrl = $event?$event[0]:''" @deleteMaterial="formData.imageUrl = ''"
                          type="image"  :divStyle="'width:100%;margin-bottom:0px;height:90px;line-height: 90px;'" :num=1 ></MaterialList>
          </el-form-item>
          <el-form-item label="显示宽度" >
            <el-input v-model="formData.width" size="mini" type="number" placeholder="宽度" :min="0" :max="100" style="margin-top: 5px">
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
          <el-form-item label="跳转链接" >
            <wx-page-select :isSystemUrl="formData.isSystemUrl"  @switchChange="formData.isSystemUrl=$event" :page="formData.pageUrl" @change="formData.pageUrl=$event"></wx-page-select>
          </el-form-item>
        </el-form>
      </div>
    </settingSlot>
    <!--    <p style="display:none">{{getData}}</p>-->
  </div>
</template>

<script>

  import { mapState , mapGetters , mapMutations , mapActions } from 'vuex';

  import settingSlot from './settingSlot'
  import MaterialList from '@/components/material/wxlist.vue'
  import WxPageSelect from '@/components/wx-page-select/Index.vue'

  export default {
    components: { settingSlot, MaterialList, WxPageSelect  },
    data() {
      return {
        formDataCopy : {
          borderRadius:0,
          width: 100,
          isSystemUrl: true,
          imageUrl: '',
          pageUrl: ''
        },
        formData : {}
      };
    },
    props: {
      clientType: [String],
      showData:{
        type: Object,
        default: ()=> {}
      },
      config   : {
        type: Object,
        default: ()=> {}
      }
    },
    computed: {
      ...mapState({
        componentsList: state => state.divpage.componentsList,
        clickComIndex : state => state.divpage.clickComIndex,
      })
    },
    mounted(){
      let that = this;
      if(that.IsEmptyObj(that.showData)){
        that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      } else {
        that.formData = that.showData
      }
      that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
      // that.updateData({
      //   componentsList: that.componentsList
      // })
    },
    methods: {
      ...mapMutations([
        'updateData'
      ]),
      // 删除按钮
      delBtn(index){
        let that = this;
        that.$confirm('是否删除该按钮?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText : '取消',
          type : 'warning'
        }).then(()=>{
          that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
          that.updateData({ componentsList: that.componentsList });
        }).catch(()=>{})
      },
      cancel(){
        this.$emit('cancel')
      },
      reset(){
        let that = this;
        that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
        that.componentsList[that.clickComIndex]['data'] = this.formData;
      },
      confirm(){
        this.$emit('confirm', this.formData)
      },
      delete(){
        this.$emit('delete')
      }
    },
    watch: {
      showData: {
        handler(newVal, oldVal) {
          this.formData = newVal ? newVal : this.formData;
        },
        deep: true
      },
      clientType(){}
    }
  };
</script>
<style lang='less' scoped>
  .compSetting{
    /deep/ .el-form .el-form-item{
      margin-bottom: 8px;
    }

  }
  .el-form-item{
    margin-bottom: 0;
  }
</style>
