import request from '@/router/axios'


export function editTagLink(obj) {
  return request({
    url: '/weixin/wxgoodstaglink/edittaglink',
    method: 'post',
    data: obj
  })
}

export function getSomeoneTags(query) {
  return request({
    url: '/weixin/wxgoodstaglink/getSomeGoodsTags',
    method: 'get',
    params: query
  })
}

export function delTagLink(obj) {
  return request({
    url: '/weixin/wxgoodstaglink/del',
    method: 'delete',
    data: obj
  })
}

export function delAllTagLink(obj) {
  return request({
    url: '/weixin/wxgoodstaglink/delall',
    method: 'delete',
    data: obj
  })
}
