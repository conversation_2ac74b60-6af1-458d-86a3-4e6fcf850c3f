import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/mall/goodsspec/page',
    method: 'get',
    params: query
  })
}

export function getList(query) {
  return request({
    url: '/mall/goodsspec/list',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/mall/goodsspec',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/mall/goodsspec/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/mall/goodsspec/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/mall/goodsspec',
    method: 'put',
    data: obj
  })
}
