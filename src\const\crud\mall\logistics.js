/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.gocreateone.com
 * 注意：
 * 本软件为www.gocreateone.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  excelBtn: true,
  printBtn: true,
  viewBtn: true,
  labelWidth: 100,
  column: [
    {
      label: '物流编码',
      prop: 'code',
      rules: [
        {
          required: true,
          message: '物流编码不能为空',
          trigger: 'blur'
        }
      ]
    },
    {
      label: '物流名',
      prop: 'name',
      rules: [
        {
          required: true,
          message: '物流名不能为空',
          trigger: 'blur'
        }
      ]
    },
    {
      label: '是否显示',
      prop: 'enable',
      type: 'radio',
      slot: true,
      dicData: [{
        label: '关闭',
        value: '0'
      }, {
        label: '启用',
        value: '1'
      }],
      rules: [
        {
          required: true,
          message: '物流名不能为空',
          trigger: 'blur'
        }
      ]
    },
  ]
}
