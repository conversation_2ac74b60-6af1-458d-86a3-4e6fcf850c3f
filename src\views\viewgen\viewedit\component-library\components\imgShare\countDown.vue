<template>
  <div class="countDownComponent"
       :style="{marginBottom: `${setData.pageMarginBottom}px`,
       marginTop: `${setData.pageMarginTop}px`,
       marginLeft: `${setData.pageMarginLeft}px`,
       marginRight: `${setData.pageMarginRight}px`}">
    <div class="box" :style="{
         paddingBottom: `${setData.contentPaddingBottom}px`,
         paddingTop: `${setData.contentPaddingTop}px`,
         paddingLeft: `${setData.contentPaddingLeft}px`,
         paddingRight: `${setData.contentPaddingRight}px`,
         backgroundColor:setData.backColor,
         borderTopLeftRadius:`${setData.topBorderRadius}px`,
         borderTopRightRadius:`${setData.topBorderRadius}px`,
         borderBottomLeftRadius:`${setData.bottomBorderRadius}px`,
         borderBottomRightRadius:`${setData.bottomBorderRadius}px`}">
      <div class="time_box " :style="{borderColor:setData.color}">
        <div class="font_day" :style="{backgroundColor:setData.color,color:setData.fontColor}">{{ setData.title }}04天</div>
        <div class="font_min" >22:40:34</div>
      </div>
    </div>
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';


export default {
  data() {
    return {};
  },
  components: {},
  props: {
    theme: {type: Object | Array},
    setData: {type: Object | Array},
    cId: {type: Number},
    noEditor: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {
  },
  mounted() {
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
  },
  watch: {
    setData(newVal, oldVal) {
    },
    componentsList(newVal, oldVal) {          //添加的时候出发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    }
  },
  beforeDestroy() {
    // this.$root.Bus.$off('addHotSpot')
  }
};
</script>
<style lang='less' scoped>

@import '../../colorui/main.css';
@import '../../colorui/icon.css';

.box {
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  text-align: center;
}
.time_box{

  border-radius: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid;
}
.font_day{
  font-weight: bolder;
  padding: 4px 20px;
  border-radius: 10px;
}
.font_min{
  padding: 4px 10px;
}


</style>
