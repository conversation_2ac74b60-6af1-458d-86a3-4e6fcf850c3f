import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/upms/configmessage/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/upms/configmessage',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/upms/configmessage/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/upms/configmessage/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/upms/configmessage',
        method: 'put',
        data: obj
    })
}

export function getObj2() {
    return request({
        url: '/upms/configmessage',
        method: 'get'
    })
}
