<!--
  - Copyright (C) 2018-2020
  - All rights reserved, Designed By www.gocreateone.com
  - 注意：
  - 本软件为www.gocreateone.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<!-- 优惠券 -->
<template>
  <div class="pageComponent" :style="{ backgroundColor: setData.background }">
    <div class="flex justify-start" style="padding-left: 5px;" :style="{backgroundColor: setData.background}"
         :class="setData.background && setData.background.indexOf('bg-') != -1 ? setData.background : '' ">
      <div class="flex " v-for="(item,index) in couponInfoList" :key="index">
        <div v-if="item.type == '1'" style="padding: 5px 6px 5px 5px;width: 200px;">
          <div class="cu-item radius" :style="{backgroundColor: setData.themeColor}"  :class="setData.themeColor&&setData.themeColor.indexOf('bg-') != -1 ? setData.themeColor : '' " >
            <div class="flex  text-white  electronic-coupons">
              <div class="flex-twice  shadow-blur radius t1-r " >
                <div class="margin-top-xs  text-sm text-center overflow-1"><span class="cuIcon-shop" style="margin-right: 3px;"></span>{{item.shopInfo?item.shopInfo.name:''}}</div>
                <div style="text-align: center;">
                  <span class="text-price text-xl "></span>
                  <span class="number ">{{item.reduceAmount}}</span>
                </div>
                <div class="text-center">
                  <div style="font-size: 8px!important;font-weight: 300">满{{item.premiseAmount}}元可用</div>
                </div>
              </div>
              <div class="flex-sub  shadow-blur radius text-center t1-l">
                <div class="text-xs margin-top-sm">代金券</div>
                <div class=" bg-white round sm margin-top-sm " style="font-size: 10px;margin: 10px 5px;color:red;">领取</div>
              </div>
            </div>
          </div>
        </div>
        <div v-else-if="item.type == '2'" style="padding: 5px 6px 5px 5px;width: 200px;">
          <div class="cu-item radius" :style="{backgroundColor: setData.themeColor}"  :class="setData.themeColor&&setData.themeColor.indexOf('bg-') != -1 ? setData.themeColor : ''">
            <div class="flex radius text-white  electronic-coupons">
              <div class="flex-twice  shadow-blur radius t1-r">
                <div class="margin-top-xs  text-sm text-center overflow-1"><span class="cuIcon-shop" style="margin-right: 3px;"></span>{{item.shopInfo?item.shopInfo.name:''}}</div>
                <div style="text-align: center;">
                  <span class="text-price text-xl "></span>
                  <span class="number ">{{item.discount}}折</span>
                </div>
                <div class="text-center">
                  <div style="font-size: 8px!important;font-weight: 300">满{{item.premiseAmount}}元可用</div>
                </div>
              </div>
              <div class="flex-sub  shadow-blur radius text-center t1-l">
                <div class="text-xs margin-top-sm">折扣券</div>
                <div class=" bg-gray round sm margin-top-sm text-gray" style="font-size: 10px;margin: 10px 5px;">已领取</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div style="writing-mode:vertical-rl;border: red solid 1px;margin:5px 0;
      text-align: center;color: red;border-radius: 4px;font-size: 12px;right: 0;position: absolute;padding: 10px 0;">
        领取更多
      </div>
    </div>
  </div>
</template>

<script>

import { mapState , mapGetters , mapMutations , mapActions } from 'vuex';

import {getPage} from '@/api/mall/couponinfo'
export default {
  data() {
    return {
      couponInfoList: []
    };
  },
  components: {  },
  props: {
    shopId: {
      type: String
    },
    thememobile : { type: Object | Array },
    setData : { type: Object | Array },
    cId     : { type: Number }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpageShop.componentsList,
    }),
  },
  created() {},
  mounted() {
    if(this.setData.loadNumber){
      this.loadData()
    }
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    loadData(){
      getPage(Object.assign({
        current: 0,
        size: this.setData.loadNumber,
        descs: '',
        ascs: 'sort',
        shopId: this.shopId
      })).then(response => {
        this.couponInfoList = response.data.data.records

      }).catch(() => {
      })
    }
  },
  watch:{
    componentsList(newVal, oldVal){          //添加的时候触发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})

    },
    setData(newVal){
      this.loadData()
    }
  }
};
</script>
<style lang='less' scoped>

@import '../colorui/main.css';
@import '../colorui/icon.css';
.pageComponent{
  .electronic-coupons {
    height: 70px;
  }

  .t1-r {
    background-size: 100% 60%;
    background-repeat: no-repeat;
  }

  .t1-l {
    background-size: 100% 60%;
    background-repeat: no-repeat;
    border-left: 1px dashed rgba(255, 255, 255, .3);
  }

  .store{
    margin-top: -10px;
  }

  .number{
    font-size: 18px;
  }

  .t2-r {
    background: radial-gradient(circle at top right, transparent 5px, #39b54a 0) top right, radial-gradient(circle at bottom right, transparent 5px, #39b54a 0) bottom right;
    background-size: 100% 60%;
    background-repeat: no-repeat;
  }

  .t2-l {
    background: radial-gradient(circle at top left, transparent 5px, #39b54a 0) top left, radial-gradient(circle at bottom left, transparent 5px, #39b54a 0) bottom left;
    background-size: 100% 60%;
    background-repeat: no-repeat;
    border-left: 1px dashed rgba(255, 255, 255, .3);
  }

  .overflow-1 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
}
</style>
