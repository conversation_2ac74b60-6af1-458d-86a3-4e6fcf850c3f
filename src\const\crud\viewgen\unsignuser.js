/**
 * 客片分享  未签用户
 */


const validatePhone = (rule, value, callback) => {
  if(!value){
    callback();
  }
  const reg = /^1[3|4|5|7|8][0-9]\d{8}$/
  if (reg.test(value)) {
    callback();
  } else {
    return callback(new Error('请输入正确的手机号'));
  }
};
export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  excelBtn: true,
  printBtn: true,
  viewBtn: false,
  columnBtn: false,//列的显隐按钮
  searchMenuSpan: 6,
  column: [
    {
      label: '主键',
      prop: 'id',
      addDisplay: false,//能否新增
      editDisplay: false,//能否修改
    },
    {
      label: '单号',
      prop: 'orderNo',
      rules: [
        {
          required: true,
          message: '请输入单号',
          trigger: 'blur'
        },

      ],
    },
    {
      label: '姓名',
      prop: 'name',
    },
    {
      label: '女方手机',
      prop: 'womanPhone',
      rules: [
        { validator: validatePhone, trigger: 'blur' }
      ],
    },
    {
      label: '男方手机',
      prop: 'manPhone',
      rules: [
        { validator: validatePhone, trigger: 'blur' }
      ],
    },
    {
      label: '创建时间',
      prop: 'createTime',
      addDisplay: false,
      editDisplay: false,
      sortable: true,
    },
    // {
    //   label: '来源',
    //   type: 'select',
    //   prop: 'sourceType',
    //   dicData: [{
    //     label: '手动录入',
    //     value: '0'
    //   }],
    //   rules: [
    //     {
    //       required: true,
    //       message: '请选择来源',
    //       trigger: 'blur'
    //     },
    //   ],
    // },
  ]
}
