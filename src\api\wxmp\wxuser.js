import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/wxuser/page',
    method: 'get',
    params: query
  })
}

export function getPageAndCon(query) {
  return request({
    url: '/weixin/wxuser/pageAndCon',
    method: 'get',
    params: query
  })
}
export function getPageByTag(query) {
  return request({
    url: '/weixin/wxuser/pagetag',
    method: 'get',
    params: query
  })
}


export function addObj(obj) {
  return request({
    url: '/weixin/wxuser',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/weixin/wxuser/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/wxuser/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/wxuser',
    method: 'put',
    data: obj
  })
}

export function synchroWxUser(obj) {
  return request({
    url: '/weixin/wxuser/synchron',
    method: 'post',
    data: obj
  })
}

export function updateRemark(obj) {
  return request({
    url: '/weixin/wxuser/remark',
    method: 'put',
    data: obj
  })
}

export function tagging(obj) {
  return request({
    url: '/weixin/wxuser/tagid-list',
    method: 'put',
    data: obj
  })
}

/**
 * 根据openId 修改手机好号
 * @param obj
 * @returns {*}
 */
export function putPhone(obj) {
  return request({
    url: '/weixin/wxuser/phone',
    method: 'put',
    data: obj
  })
}

/**
 * 修改用户置顶
 * @param obj
 * @returns {*}
 */
export function putTopFlag(id) {
  return request({
    url: '/weixin/wxuser/top/'+ id,
    method: 'put',
  })
}
