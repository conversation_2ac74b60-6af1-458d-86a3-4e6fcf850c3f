<template>
  <div >
    <el-form  class="page_setting" :model="pageData" label-width="100px">
      <el-divider content-position="center">基础属性</el-divider>
      <el-form-item label="页面名称">
        <el-input v-model="pageData.pageName" size="mini">{{ pageData.pageName }}</el-input>
      </el-form-item>
      <el-form-item label="页面标题">
        <el-input v-model="pageData.pageBase.pageTitle" size="mini"></el-input>
      </el-form-item>
      <el-form-item label="是否启用">
        <el-switch
          v-model="pageData.enable"
          active-value="0"
          inactive-value="1"
          active-color="#13ce66"
          inactive-color="#ff4949">
        </el-switch>
      </el-form-item>
      <el-form-item label="应用端:">
        {{pageData.clientType}}
<!--        <div v-if="!(id&&id!='null')">-->
<!--          &lt;!&ndash;<div style="padding: 10px;" v-if="(id&&id!='null')">&ndash;&gt;-->
<!--          <el-select style="width: 76%;" v-model="pageData.clientType" placeholder="请选择" size="mini"-->
<!--                     :disabled="!!(id&&id!='null')">-->
<!--            <el-option-->
<!--              v-for="item in clientTypeData"-->
<!--              :key="item.value"-->
<!--              :label="item.label"-->
<!--              :value="item.value"-->
<!--              :disabled="item.disabled">-->
<!--            </el-option>-->
<!--          </el-select>-->
<!--        </div>-->
<!--        <div v-else-if="pageData.clientType" style="font-size: 15px;">-->
<!--                        <span-->
<!--                          style="color: red;margin-left: 10px;">{{-->
<!--                            pageData.clientType === 'MA' ? '小程序' : pageData.clientType-->
<!--                          }} 端</span>-->
<!--        </div>-->
      </el-form-item>
<!--      <el-form-item label="展示形式">-->
<!--        <el-radio-group v-model="pageData.pageBase.resource">-->
<!--          <el-radio label="1">仅限微信打开</el-radio>-->
<!--        </el-radio-group>-->
<!--      </el-form-item>-->
      <el-form-item label="背景图片">
        <MaterialLibrary :value="pageData.pageBase.background?[pageData.pageBase.background]:[]"
                      @sureSuccess="pageData.pageBase.background = $event?$event[0]:''"
                      @deleteMaterial="pageData.pageBase.background = ''"
                      type="image" :shopId="'-1'"
                      :num=1
                      :divStyle="'width:50%;height:95px;margin-bottom:8px;line-height: 100px;'"
                      default-library-type="system"></MaterialLibrary>
      </el-form-item>
      <el-form-item label="背景颜色">
        <el-color-picker v-model="pageData.pageBase.backgroundColor"></el-color-picker>
      </el-form-item>
<!--      <el-form-item label="字体颜色">-->
<!--        <el-color-picker v-model="pageData.pageBase.fontColor"></el-color-picker>-->
<!--      </el-form-item>-->
      <el-divider content-position="center">允许用户进入限制</el-divider>
      <el-form-item label="允许用户">
        <div @click="openTagBox('permit')">
          <el-button v-show="!pageData.pageBase.permitUserTag || pageData.pageBase.permitUserTag.length==0" icon="el-icon-plus" size="mini"></el-button>
          <el-tag :key="index" v-for="(item,index) in pageData.pageBase.permitUserTag" v-show="pageData.pageBase.permitUserTag"  size="medium" :color="item?item.backColor:''"
                  :style="item?getFontColor(item.fontColor):''">{{ item?item.name:'' }} </el-tag>
        </div>
      </el-form-item>
      <el-form-item label="限制跳转">
        <wx-page-select
          :isSystemUrl="pageData.pageBase.permitIsSystemUrl"
          @switchChange="permitCustomerUrlStwChange"
          :clientType="'H5'" :page="pageData.pageBase.permitPageUrl"
          @change="pageData.pageBase.permitPageUrl=$event"></wx-page-select>
      </el-form-item>
      <el-divider content-position="center">禁止用户进入限制</el-divider>
      <el-form-item label="禁止用户">
        <div @click="openTagBox('forbid')">
          <el-button v-show="!pageData.pageBase.forbidUserTag || pageData.pageBase.forbidUserTag.length==0" icon="el-icon-plus" size="mini"></el-button>
          <el-tag :key="index" v-for="(item,index) in pageData.pageBase.forbidUserTag" v-show="pageData.pageBase.forbidUserTag"  size="medium" :color="item?item.backColor:''"
                  :style="item?getFontColor(item.fontColor):''">{{ item?item.name:'' }} </el-tag>
        </div>
      </el-form-item>
      <el-form-item label="限制跳转">
        <wx-page-select
          :isSystemUrl="pageData.pageBase.forbidIsSystemUrl"
          @switchChange="forbidCustomerUrlStwChange"
          :clientType="'H5'" :page="pageData.pageBase.forbidPageUrl"
          @change="pageData.pageBase.forbidPageUrl=$event"></wx-page-select>
      </el-form-item>
      <el-divider content-position="center">微信分享设置</el-divider>
      <el-form-item label="分享标题">
        <el-input v-model="pageData.pageBase.shareTitle" size="mini"></el-input>
      </el-form-item>
      <el-form-item label="分享描述">
        <el-input v-model="pageData.pageBase.describe" size="mini"></el-input>
      </el-form-item>
      <el-form-item label="分享图标" prop="expireCoverImg">
        <MaterialLibrary :value="pageData.pageBase.shareImgUrl?[pageData.pageBase.shareImgUrl]:[]"
                      @sureSuccess="pageData.pageBase.shareImgUrl = $event?$event[0]:''"
                      @deleteMaterial="pageData.pageBase.shareImgUrl = ''"
                      type="image" :shopId="'-1'"
                      :num=1
                      :divStyle="'width:50%;height:95px;margin-bottom:8px;line-height: 100px;'"
                      default-library-type="system"></MaterialLibrary>
      </el-form-item>
    </el-form>
    <!--      微信用户标签-->
    <el-dialog
      title="标签选择"
      :visible.sync="userTagBoxVisible "
      :close-on-click-modal="false"
      center
      :append-to-body="true"
      lock-scroll
      width="80%">
      <div style="overflow: hidden">
        <wx-user-tag  :selectedType="1"  :selectedTagId="selectedTagId"  :appId="initPageData.appId" v-on:ensureTag="ensureTag" @backFun="ensureTag"></wx-user-tag>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import WxUserTag from '@/views/wxmp/wxusertags/userTagSelect' // 用户标签
  import WxPageSelect from '@/components/wx-page-select/Index.vue'
  import store from "@/store";
  import MaterialLibrary from '@/components/material/materialLibrary.vue'
  export default {
    name: "pageSetting",
    props: {
      initPageData: [Object],
    },
    components: {
      WxUserTag,
      WxPageSelect,
      MaterialLibrary
    },
    data() {
      return {
        serverUrl: "/upms/file/upload?fileType=image&dir=wx/pageView/shareImg", // 这里写你要上传的图片服务器地址
        header: {Authorization: 'Bearer ' + store.getters.access_token}, // 有的图片服务器要求请求头需要有token
        uploadExpireCover: { //过期遮照上传
          list: [],
          uploadIconVisible: true,
          processIconVisible: false,
        },
        pageData:this.initPageData,
        userTagBoxVisible:false,
        selectedTagId:[],//选中的tagId
        forbidUserTagList:[],//选中的tagId
      }
    },
    watch:{
      initPageData(newVal, oldVal){
        this.changePageDate(newVal);
      }
    },
    created() {
    },
    mounted(){
    },
    computed: {
    },
    methods:{
      //上传图片
      beforeUpload(file, item) {
        const isLt1M = file.size / 1024 / 1024 < 1;
        if (!isLt1M) {
          this.$message.error('上传图片大小不能超过 1MB!');
        }
        return isLt1M;
      },
      uploadSuccess(res, file, fileList, type, item) {
        this.pageData.pageBase.shareImgUrl = res.link;
        this.$message.success("上传成功")
      },
      uploadError(res, item) {
        item.uploadIconVisible = true;
        item.processIconVisible = false;
        item.uploadDisabled = false;
        this.$message.error("上传失败")
      },
      handleUploadChange(file, fileList, item) {
        if (file.status == "ready") {
          item.percentage = file.percentage;
          item.uploadDisabled = true;
          item.uploadIconVisible = false;
          item.processIconVisible = true;
        } else if (file.status == "success") {
          item.uploadIconVisible = true;
          item.processIconVisible = false;
          item.uploadDisabled = false;
        }
      },
      handleRemove(type, item, index) {
        this.pageData.pageBase.shareImgUrl = "";
      },
      openTagBox(flag) {
        let idList = [];
        // console.log("点开来",flag)
        if(flag == 'permit' && this.pageData.pageBase.permitUserTag && this.pageData.pageBase.permitUserTag.length>0){
          for (let i = 0; i <   this.pageData.pageBase.permitUserTag.length; i++) {
            idList.push(this.pageData.pageBase.permitUserTag[i].id);
          }
        }else if(flag == 'forbid'&& this.pageData.pageBase.forbidUserTag  && this.pageData.pageBase.forbidUserTag.length>0){
          for (let i = 0; i <   this.pageData.pageBase.forbidUserTag.length; i++) {
            idList.push(this.pageData.pageBase.forbidUserTag[i].id);
          }
        }
        this.selectedTagId = idList;
        // console.log("this.selectedTagId",this.selectedTagId)
        this.tagBoxFlag = flag;
        this.userTagBoxVisible = true;
      },
      //转化得到字体颜色
      getFontColor(val) {
        if (!val) {
          return;
        }
        return "color:" + val;
      },
      //允许用户进入  自定义地址开关改变
      permitCustomerUrlStwChange(val){
        this.pageData.pageBase.permitIsSystemUrl = val;
        this.pageData.pageBase.permitPageUrl = "";
      },
      //禁止用户进入 自定义地址开关改变
      forbidCustomerUrlStwChange(val){
        this.pageData.pageBase.forbidIsSystemUrl = val;
        this.pageData.pageBase.forbidPageUrl = "";
      },
      ensureTag(obj) {
        console.log("确认标签", obj)
        if (this.tagBoxFlag == 'permit') {
          this.pageData.pageBase.permitUserTag = obj;
        } else if (this.tagBoxFlag == 'forbid') {
          this.pageData.pageBase.forbidUserTag = obj;
        }
      },
      changePageDate(newVal){
        this.pageData = newVal;
        if(!this.pageData.backgroundColor){
          this.pageData.backgroundColor = "#f1f1f1";
        }
      }
    }
  };
</script>

<style lang="scss" scoped>

/**
 * 此处可考虑修改
 */
.page_setting{
  overflow: scroll;
  height:80vh;
}

.el-form-item {
  margin-bottom: 0;
}
.img_dialog {
  position: absolute;
  left: 80%;
  top: 0;
  width: 30px;
  opacity: 0;
}

.image_preview {
  position: relative;
  float: left;
  display: inline;
  margin: 0px 15px 10px 0px;

  .image_preview_image {
    border: 1px solid transparent;
    width: 150px;
    height: 150px;
  }

  &:hover .img_dialog {
    text-align: center;
    cursor: default;
    text-align: center;
    color: #fff;
    opacity: 1;
    font-size: 20px;
    background-color: rgba(0, 0, 0, .5);
    transition: opacity .3s;
  }
}

.waite_upload_img {
  border: 1px #8c939d dashed;
  border-radius: 6px;
  width: 150px;
  height: 150px
}
</style>
