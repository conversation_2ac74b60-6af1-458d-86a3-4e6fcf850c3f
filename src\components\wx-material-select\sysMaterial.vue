<!--微信模块素材库  系统内部的-->
<template>
  <div>
      <div style="margin-bottom: 5px">
        <el-select v-model="appId" placeholder="请选择公众号" @change="changeAppInfo">
          <el-option
            v-for="item in appList"
            :key="item.id"
            :label="item.name"
            :value="item.id">
          </el-option>
        </el-select>
      </div>
      <el-container>
        <el-aside width="unset">
          <div style="margin-bottom: 10px;margin-top: 20px;">
            <el-button
              class="el-icon-plus"
              size="small"
              @click="materialgroupAdd()">
              添加分组
            </el-button>
          </div>
          <el-tabs  style="height: 400px;" tab-position="left" v-model="materialgroupObjId" v-loading="materialgroupLoading" @tab-click="tabClick">
            <el-tab-pane v-for="(item,index) in materialgroupList"
                         :key="index"
                         :name="item.id">
              <span slot="label"> {{item.name}}</span>
            </el-tab-pane>
          </el-tabs>
        </el-aside>
        <el-main>
          <el-card>
            <div slot="header">
              <el-row>
                <el-col :span="12">
                  <span>{{materialgroupObj.name}}</span>
                  <span v-if="materialgroupObj.id != '-1'">
                    <el-button size="small" type="text" class="el-icon-edit" style="margin-left: 10px;" @click="materialgroupEdit(materialgroupObj)">重命名</el-button>
                    <el-button size="small" type="text" class="el-icon-delete" style="margin-left: 10px;color: red" @click="materialgroupDelete(materialgroupObj)">删除</el-button>
                  </span>
                </el-col>
                <el-col :span="12" style="text-align: right;">
                  <el-upload
                    action="/upms/file/upload?fileType=image&dir=material/"
                    :headers="headers"
                    :file-list="[]"
                    :limit="10"
                    :multiple="true"
                    :on-progress="handleProgress"
                    :on-exceed="handleExceed"
                    :before-upload="beforeUpload"
                    :on-success="handleSuccess"
                    :on-error="handleError">
                    <el-button size="small" type="primary">点击上传</el-button>
                  </el-upload>
                </el-col>
              </el-row>
            </div>
            <div v-loading="tableLoading">
              <el-alert
                v-if="tableData.length <= 0"
                title="暂无数据"
                type="info"
                :closable="false"
                center
                show-icon>
              </el-alert>
              <el-row :gutter="5">
                <el-checkbox-group v-model="urls" :max="num - value.length">
                  <el-col :span="4" v-for="(item,index) in tableData" :key="index">
                  <el-card :body-style="{ padding: '5px' }">
                    <el-image
                      style="width: 100%;height: 200px"
                      :src="item.url"
                      fit="contain"
                      :preview-src-list="[item.url]"></el-image>
                    <el-button size="mini" type="success" @click="selectMaterial(item)">选择<i class="el-icon-circle-check el-icon--right"></i></el-button>
                    <p class="item-name">{{item.name}}</p>
                    <div>
                      <el-row class="compile">
                        <el-col :span="6" class="col-do">
                          <el-button type="text" class="button-do" @click="materialRename(item)">改名</el-button>
                        </el-col>
                        <el-col :span="6" class="col-do">
                          <el-button type="text" class="button-do" @click="materialUrl(item)">链接</el-button>
                        </el-col>
                        <el-col :span="6" class="col-do">
                          <el-dropdown trigger="click" @command="handleCommand">
                            <el-button type="text" class="button-do">分组<i class="el-icon-arrow-down"></i></el-button>
                            <el-dropdown-menu slot="dropdown">
                              <el-dropdown-item v-for="(item2,index) in materialgroupList"
                                                v-if="index > 0"
                                                :key="index"
                                                :command="item.id + '-' + item2.id"
                                                :disabled="item.groupId == item2.id">{{item2.name}}</el-dropdown-item>
                            </el-dropdown-menu>
                          </el-dropdown>
                        </el-col>
                        <el-col :span="6" class="col-do">
                          <el-button type="text" class="button-do" style="color: red" @click="materialDel(item)">删除</el-button>
                        </el-col>
                      </el-row>
                    </div>
                  </el-card>
                </el-col>
                </el-checkbox-group>
              </el-row>
              <el-pagination
                @size-change="sizeChange"
                @current-change="currentChange"
                :current-page.sync="page.currentPage"
                :page-sizes="[12, 24]"
                :page-size="page.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="page.total"
                class="pagination" style="margin-top: 20px;">
              </el-pagination>
            </div>
          </el-card>
        </el-main>
      </el-container>
  </div>

</template>

<script>
  import { getPage as materialgroupPage, addObj as  materialgroupAdd, delObj as materialgroupDel, putObj as materialgroupEdit} from '@/api/viewgen/wxmaterialgroup'
  import {getPage, addObjList, delObj, putObj} from '@/api/viewgen/wxmaterial'
  import {getList as appList} from '@/api/wxmp/wxapp'
  import store from "@/store"
  import {mapGetters} from 'vuex'
  import { getStore } from '@/util/store'

  export default {
    name: "materialWxList",
    props: {
      //素材数据
      value:{
        type: Array,
        default() {
          return []
        },
      },
      //素材类型
      type:{
        type: String,
      },
      //自定义图片style
      divStyle:{
        type: String
      },
      num:{
        type: Number,
        default() {
          return 5
        },
      },
      //宽度
      width: {
        type: Number,
        default() {
          return 150
        }
      },
      //宽度
      height: {
        type: Number,
        default() {
          return 150
        }
      }
    },
    watch:{
    },
    data() {
      return {
        headers:{
          Authorization: 'Bearer ' + store.getters.access_token
        },
        saveList:[],//oss保存成功的list
        dialogVisible: false,
        url: '',
        listDialogVisible: false,
        materialgroupList: [],
        materialgroupObjId: '',
        materialgroupObj: {},
        materialgroupLoading: false,
        tableData: [],
        page: {
          total: 0, // 总页数
          currentPage: 1, // 当前页数
          pageSize: 12, // 每页显示多少条
          ascs: [],//升序字段
          descs: 'create_time'//降序字段
        },
        tableLoading: false,
        groupId: null,
        urls: [],
        appList: [],
        appId: null,
      }
    },
    created() {
      if(this.userInfo.type == '-1'){
        let switchTenantId = getStore({ name: 'switchTenantId' })
        if(switchTenantId){
          this.headers['switch-tenant-id'] = switchTenantId
        }
      }
      this.getAppList();
    },
    computed: {
      ...mapGetters(['userInfo'])
    },
    methods:{
      getAppList(){
        appList().then(res => {
          let appList = res.data;
          let type = store.getters.userInfo.type
          console.log("store",store.getters.userInfo)
          if(type == '1'){
            appList.unshift({
              id: '-1',
              name: '微信公共素材'
            })
          }
          this.appList = appList
          if(appList.length > 0){
            this.changeAppInfo(appList[0].id)
          }
        })
      },
      changeAppInfo(e){
        this.appId = e
        this.materialgroupPage()
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage
        this.getPage(this.page)
      },
      moveMaterial(index,type){
        if(type == 'up'){
          let tempOption = this.value[index - 1]
          this.$set(this.value, index - 1, this.value[index])
          this.$set(this.value, index, tempOption)
        }
        if(type == 'down'){
          let tempOption = this.value[index + 1]
          this.$set(this.value, index + 1, this.value[index])
          this.$set(this.value, index, tempOption)
        }
      },
      zoomMaterial(index){
        this.dialogVisible = true
        this.url = this.value[index]
      },
      deleteMaterial(index){
        let that = this
        this.$confirm('是否确认删除？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function() {
          that.value.splice(index,1)
          that.urls = [];
          that.$emit('deleteMaterial',that.urls)//点击确认后的回调
        })
      },
      materialgroupPage(){
        this.materialgroupLoading = true
        materialgroupPage({
          total: 0, // 总页数
          current: 1, // 当前页数
          size: 999, // 每页显示多少条
          ascs: [],//升序字段
          descs: 'create_time',//降序字段
          appId: this.appId
        }).then(response => {
          this.materialgroupLoading = false
          let materialgroupList = response.data.data.records
          materialgroupList.unshift({
            id: '-1',
            name: '全部分组'
          })
          this.materialgroupList = materialgroupList
          this.tabClick({
            index: 0
          })
        })
      },
      materialgroupDelete(materialgroupObj){
        let that = this
        this.$confirm('是否确认删除该分组？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function() {
          materialgroupDel(materialgroupObj.id)
            .then(function() {
              that.$delete(that.materialgroupList, materialgroupObj.index)
            })
        })
      },
      materialgroupEdit(materialgroupObj){
        let that = this
        this.$prompt('请输入分组名', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValue: materialgroupObj.name,
        }).then(({ value }) => {
          materialgroupEdit({
            id: materialgroupObj.id,
            name: value
          }).then(function() {
            materialgroupObj.name = value
            that.$set(that.materialgroupList, materialgroupObj.index, materialgroupObj)
          })
        }).catch(() => {

        })
      },
      materialgroupAdd(){
        let that = this
        this.$prompt('请输入分组名', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /[\S]/,
          inputErrorMessage: '分组名不能为空'
        }).then(({ value }) => {
          materialgroupAdd({
            appId: this.appId,
            name: value
          }).then(function() {
            that.materialgroupPage()
          })
        }).catch(() => {

        })
      },
      tabClick(tab,event){
        this.urls = []
        let index = Number(tab.index)
        let materialgroupObj = this.materialgroupList[index]
        materialgroupObj.index = index
        this.materialgroupObj = materialgroupObj
        this.materialgroupObjId = materialgroupObj.id
        this.page.currentPage = 1
        this.page.total = 0
        if(materialgroupObj.id != '-1'){
          this.groupId = materialgroupObj.id
        }else{
          this.groupId = null
        }
        this.getPage(this.page)
      },
      getPage(page, params) {
        this.tableLoading = true
        getPage(Object.assign({
          current: page.currentPage,
          size: page.pageSize,
          descs: this.page.descs,
          ascs: this.page.ascs,
        }, {
          groupId: this.groupId,
          appId: this.appId
        })).then(response => {
          let tableData = response.data.data.records
          this.page.total = response.data.data.total
          this.page.currentPage = page.currentPage
          this.page.pageSize = page.pageSize
          this.tableData = tableData
          this.tableLoading = false
        }).catch(() => {
          this.tableLoading=false
        })
      },
      sizeChange(val) {
        this.page.currentPage = 1
        this.page.pageSize = val
        this.getPage(this.page)
      },
      materialRename(item){
        let that = this
        this.$prompt('请输入素材名', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValue: item.name,
        }).then(({ value }) => {
          putObj({
            id: item.id,
            name: value
          }).then(function() {
            that.getPage(that.page)
          })
        }).catch(() => {

        })
      },
      materialUrl(item){
        let that = this
        this.$prompt('素材链接', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValue: item.url,
        }).then(({ value }) => {

        }).catch(() => {

        })
      },
      materialDel(item){
        let that = this
        this.$confirm('是否确认删除该素材？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function() {
          delObj(item.id)
            .then(function() {
              let index = that.urls.indexOf(item.url)
              if(index != -1){
                that.urls.splice(index,1)
              }
              that.getPage(that.page)
            })
        })
      },
      handleCommand(command) {
        let that = this
        let s = command.split('-')
        putObj({
          id: s[0],
          groupId: s[1]
        }).then(function() {
          that.getPage(that.page)
        })
      },
      handleProgress(event, file, fileList){
        // let uploadProgress = file.percentage.toFixed(0)
        // this.uploadProgress = uploadProgress
      },
      handleSuccess(res, file, fileList) {
        let that = this
        this.uploadProgress = 0
        console.log("file", file)
        console.log("fileList", fileList)
        this.saveList.push(file);
        let list=[];
        if( this.saveList.length == fileList.length) {
          for (let i = 0; i < this.saveList.length; i++) {
            let obj = {
              type: '1',
              groupId: this.groupId != '-1' ? this.groupId : null,
              name: this.saveList[i].name,
              url: this.saveList[i].response.link,
              appId: this.appId
            }
            list.push(obj);
          }
          addObjList({
            list: list
          }).then( ()=> {
            this.saveList=[];
            that.getPage(that.page)
          })
        }
      },
      handleError(err, file, fileList){
        this.$message.error(err+'')
      },
      beforeUpload(file){
        const isPic =
          file.type === "image/jpeg" ||
          file.type === "image/png" ||
          file.type === "image/gif" ||
          file.type === "image/jpg"
        const isLt1M = file.size / 1024 / 1024 < 1
        if (!isPic) {
          this.$message.error("上传图片只能是 JPG、JPEG、PNG、GIF 格式!")
          return false
        }
        if (!isLt1M) {
          this.$message.error('上传头像图片大小不能超过 1MB!')
        }
        return isPic && isLt1M
      },
      handleExceed(files, fileList) {
        this.$message.warning('上传一次性限制选择 10 个文件，本次选择了 '+files.length+'个文件');
      },
      selectMaterial(val){
        console.log("选中",val)
        this.$emit('selectMaterial', {
          name:val.name,
          url:val.url,
        })//点击确认后的回调
      }
    }
  };
</script>

<style lang="scss" scoped>
  .material-name{
    padding: 0px 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    height: 20px;
    font-size: 13px;
    margin-top: 10px;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }

  .compile{
    padding-top: 10px;
    padding-bottom: 10px;
  }

  .col-do{
    text-align: center;
  }

  .button-do{
    padding: unset!important;
    font-size: 12px;
  }
</style>
