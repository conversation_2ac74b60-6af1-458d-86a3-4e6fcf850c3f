<!--档期管理-->
<template>
  <div class="execution">
    <el-button type="primary" style="margin-left: 20px" size="mini" @click="openShopInfoBox">添加店铺</el-button>
    <div class="shop_room_tree">
      <el-tree style="font-size: 18px;padding: 5px"
               :data="treeData"
               :default-expand-all="true"
               :props="defaultProps">
        <div  class="tree_content" slot-scope="{ node, data }">
          <span >{{ node.label }}</span>
          <span >
              <el-button
                v-if="data.hasOwnProperty('roomList')"
                type="primary"
                size="mini"
                @click.stop="() => openRoomBox(data)">
                添加场地
              </el-button>
              <el-button
                type="danger"
                size="mini"
                @click.stop="() => remove(node)">
                删除
              </el-button>
          </span>
        </div>
      </el-tree>
    </div>
    <!-- 添加店铺框 shopInfoBoxVisible-->
    <el-dialog
      :append-to-body="true"
      title="添加店铺"
      :visible.sync="shopInfoBoxVisible"
      width="40%"
      @close="resetShopInfoForm"
      center>
      <el-form :model="shoInfoForm" :rules="shopInfoFormRules" ref="shoInfoFormRef" label-width="100px">
        <el-form-item label="店铺名称" prop="name">
          <el-input v-model="shoInfoForm.name"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">确认</el-button>
          <el-button @click="shopInfoBoxVisible=false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 添加场地框 goodsTagBoxVisible-->
    <el-dialog
      :append-to-body="true"
      title="添加场地"
      :visible.sync="roomBoxVisible"
      width="40%"
      @close="resetRoomForm"
      center>
      <el-form :model="roomForm" :rules="roomFormRules" ref="roomFormRef" label-width="100px">
        <el-form-item label="场地名称" prop="name">
          <el-input v-model="roomForm.name"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitRoom">确认</el-button>
          <el-button @click="roomBoxVisible=false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {getPage, getObj, addObj, putObj, delObj as delShop, getShopAndRoomTree} from '@/api/wxmp/shopinfo'
import {addObj as addRoom,delObj as delRoom} from '@/api/wxmp/auctionroom'
import {mapGetters} from 'vuex'

export default {
  name: 'calendarmanager',
  data() {
    return {
      roomBoxVisible: false,//房间信息
      roomForm: {name: ''},
      roomFormRules: {
        name: [{required: true, message: '请输入场地名称', trigger: 'blur'},],
      },
      shopInfoBoxVisible: false,
      shoInfoForm: {name: ''},
      shopInfoFormRules: {
        name: [{required: true, message: '请输入店铺名称', trigger: 'blur'},],
      },
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: '',
      treeData: [], //
      defaultProps: {
        children: 'roomList',
        label: 'name'
      },
    }
  },
  created() {
    this.getShopAndRoomTree();
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.permissions['weixin:auctioncalendar:add'] ? true : false,
        delBtn: this.permissions['weixin:auctioncalendar:del'] ? true : false,
        editBtn: this.permissions['weixin:auctioncalendar:edit'] ? true : false,
        viewBtn: this.permissions['weixin:auctioncalendar:get'] ? true : false
      };
    }
  },
  methods: {
    //打开新增店铺
    openShopInfoBox() {
      this.shopInfoBoxVisible = true;
    },
    onSubmit() {
      this.$refs["shoInfoFormRef"].validate((valid) => {
        if (valid) {
          addObj(this.shoInfoForm).then(res => {
            this.$message({
              showClose: true,
              message: '添加成功',
              type: 'success'
            })
            this.getShopAndRoomTree()
            this.shopInfoBoxVisible = false;
          }).catch(() => {
          })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    resetShopInfoForm() {
      this.$refs["shoInfoFormRef"].resetFields();
    },
    getShopAndRoomTree() {
      getShopAndRoomTree().then(res => {
        console.log("treeData", res);
        this.treeData = res.data.data;
      }).catch(() => {
      })
    },
    openRoomBox(val) {
      console.log("有没有", val)
      this.roomForm.shopId = val.id
      this.roomBoxVisible = true;
      return
    },
    submitRoom() {
      this.$refs["roomFormRef"].validate((valid) => {
        if (valid) {
          addRoom(this.roomForm).then(res => {
            this.$message({
              showClose: true,
              message: '添加成功',
              type: 'success'
            })
            this.getShopAndRoomTree()
            this.roomBoxVisible = false;
          }).catch(() => {

          })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    resetRoomForm() {
      this.$refs["roomFormRef"].resetFields();
    },
    remove(node) {
      let msg = "";
      if(node.data.hasOwnProperty('roomList')){
        msg = "此操作将永久删除该门店，及其相关的场地，是否继续？";
      }else{
        msg = "此操作将永久删除该场地，是否继续？";
      }
      this.$confirm(msg, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        if(node.data.hasOwnProperty('roomList')){
          delShop(node.data.id).then(res => {
            this.getShopAndRoomTree();
            this.$message({
              type: "success",
              message: "删除成功!"
            });
          }).catch(() => {

          })
        }else{
          delRoom(node.data.id).then(res => {
            this.getShopAndRoomTree();
            this.$message({
              type: "success",
              message: "删除成功!"
            });
          }).catch(() => {

          })
        }
      }).catch(() => {

      });
    },
    treeClick() {

    },
  }
}
</script>
<style lang="scss" scoped>
.shop_room_tree /deep/ {
  .el-tree-node__content {
    padding: 5px;
    margin: 2px;
    .el-tree-node__expand-icon{
      justify-content: start;
    }
  }
}
.tree_content{
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
</style>
