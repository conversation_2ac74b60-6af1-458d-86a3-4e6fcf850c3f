<!--商品选择
应用场景：
1. 商品排序
-->
<template>
  <div class="execution">
    <basic-container>
      <avue-crud ref="crud"
                 :page="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 v-model="form"
                 @on-load="getPage"
                 @refresh-change="refreshChange"
                 @row-update="handleUpdate"
                 @row-del="handleDel"
                 @sort-change="sortChange"
                 @selection-change="selectChange"
                 @search-change="searchChange">
        <template slot-scope="scope" slot="menuLeft">
          <el-button type="primary"
                     icon="el-icon-plus"
                     size="small"
                     @click.stop="confirm">确认
          </el-button>
        </template>
      </avue-crud>

    </basic-container>

  </div>
</template>

<script>
import {getPage, getObj, addObj as addGoods, putObj as putGoods, delObj, getSortPage} from '@/api/viewgen/wxgoods'
import {tableOption2} from '@/const/crud/viewgen/wxgoods.js'
import {mapGetters} from 'vuex'
import draggable from "vuedraggable";
import store from "@/store";
import {getSomeoneTags} from "@/api/viewgen/wxgoodstaglink";
import goodsTagSelect from "@/views/viewgen/goodslist/goodsTagSelect";

export default {
  name: 'wxGoodsSelect',
  components: {
    draggable,
    goodsTagSelect,
  },
  props: {
    sortId:{ //排序id  需要去剔除商品
      type:String
    },

  },
  data() {
    return {
      selectGoodsList:[],//所选goods
      selectTagList: [],//选中的标签
      goodsTagBoxVisible: false,//标签分类表
      serverUrl: '/upms/file/upload?fileType=image&dir=weixin/goods/', // 这里写你要上传的图片服务器地址
      header: {Authorization: 'Bearer ' + store.getters.access_token}, // 有的图片服务器要求请求头需要有token
      goodsBoxVisible: false,
      imagePreviewUrl: '',
      form: {},


      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption2,
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      goodsRules: {
        name: [
          {required: true, message: '请输入作品名称', trigger: 'blur'},
          {max: 25, message: '长度在25个字符内', trigger: 'blur'}
        ],
      },
    }
  },
  created() {
  },
  mounted: function () {
    this.getPage();
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.permissions['weixin:wxgoods:add'] ? true : false,
        delBtn: this.permissions['weixin:wxgoods:del'] ? true : false,
        editBtn: this.permissions['weixin:wxgoods:edit'] ? true : false,
        viewBtn: this.permissions['weixin:wxgoods:get'] ? true : false
      };
    }
  },
  methods: {
    searchChange(params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    sortChange(val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage(page, params) {
      this.tableLoading = true
      getSortPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
        sortId:this.sortId
      }, params, this.paramsSearch)).then(response => {
        this.tableData = response.data.data.records
        this.page.total = response.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this
      this.$confirm('是否确认删除此数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        return delObj(row.id)
      }).then(data => {
        _this.$message({
          showClose: true,
          message: '删除成功',
          type: 'success'
        })
        this.getPage(this.page)
      }).catch(function (err) {
      })
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page)
    },
    getFontColor(val) {
      if (!val) {
        return;
      }
      return "color:" + val;
    },
    selectChange(list){
      this.selectGoodsList = list;
      console.log("改了 ",list)
    },
    //确认
    confirm() {
      this.$emit("confirm", this.selectGoodsList);
      console.log("确认",this.selectGoodsList);
    },
  }
}
</script>

<style lang="scss" scoped>

.goods_form_tag {
  margin-right: 10px;
}

.userTagBox {
  height: 400px;
  overflow: scroll
}

.userTagBox_type {
  overflow: auto;
}

.userTagBox_tag {
  display: block;
  float: left;
  padding: 10px;
}

.img_dialog {
  position: absolute;
  left: 80%;
  top: 0;
  width: 30px;
  opacity: 0;
}

.image_preview {
  position: relative;
  float: left;
  display: inline;
  margin: 0px 15px 10px 0px;

  .image_preview_image {
    border: 1px solid transparent;
    width: 150px;
    height: 150px;
  }

  &:hover .img_dialog {
    text-align: center;
    cursor: default;
    text-align: center;
    color: #fff;
    opacity: 1;
    font-size: 20px;
    background-color: rgba(0, 0, 0, .5);
    transition: opacity .3s;
  }
}

.content_image_preview {
  position: relative;
  float: left;
  display: inline;
  margin-right: 15px;

  &:hover .img_dialog {
    text-align: center;
    cursor: default;
    text-align: center;
    color: #fff;
    opacity: 1;
    font-size: 20px;
    background-color: rgba(0, 0, 0, .5);
    transition: opacity .3s;
  }
}

.waite_upload_img {
  border: 1px #8c939d dashed;
  border-radius: 6px;
  width: 150px;
  height: 150px
}
</style>
