<template>
  <MaterialLibrary
    :value="value"
    :type="type"
    :divStyle="divStyle"
    :num="num"
    :width="width"
    :height="height"
    :selected-app-id="[]"
    :shop-id="shopId"
    default-library-type="system"
    @sureSuccess="$emit('sureSuccess', $event)"
    @deleteMaterial="$emit('deleteMaterial', $event)"
  />
</template>

<script>
  import MaterialLibrary from './materialLibrary.vue'

  export default {
    name: "materialList",
    components: {
      MaterialLibrary
    },
    props: {
      //店铺ID
      shopId:{
        type: String,
      },
      //素材数据
      value:{
        type: Array,
        default() {
          return []
        },
      },
      //素材类型
      type:{
        type: String,
      },
      //自定义图片style
      divStyle:{
        type: String
      },
      num:{
        type: Number,
        default() {
          return 5
        },
      },
      //宽度
      width: {
        type: Number,
        default() {
          return 150
        }
      },
      //宽度
      height: {
        type: Number,
        default() {
          return 150
        }
      }
    }
  };
</script>
