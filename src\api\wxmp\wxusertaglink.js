import request from '@/router/axios'


export function editTagLink(obj) {
  return request({
    url: '/weixin/wxusertaglink/edittaglink',
    method: 'post',
    data: obj
  })
}
export function delTagLink(obj) {
  return request({
    url: '/weixin/wxusertaglink/del',
    method: 'delete',
    data: obj
  })
}
export function delAllTagLink(obj) {
  return request({
    url: '/weixin/wxusertaglink/delall',
    method: 'delete',
    data: obj
  })
}

export function getSomeoneTags(query) {
  return request({
    url: '/weixin/wxusertaglink/getSomeoneTags',
    method: 'get',
    params: query
  })
}
