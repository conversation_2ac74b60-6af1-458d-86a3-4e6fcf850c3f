<template>
  <div class="popupSetting">
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">弹窗广告设置</p>
      <div slot="hint">
      </div>

      <div slot="mainContent">
        <el-form ref="form" label-width="90px" :model="formData">
          <el-form-item label="图片">
            <MaterialList :value="formData.imageUrl?[formData.imageUrl]:[]" @sureSuccess="handleImageChange" @deleteMaterial="handleImageDelete"
                          type="image" :divStyle="'width:100%;margin-bottom:0px;height:120px;line-height: 120px;'" :num=1 ></MaterialList>
          </el-form-item>
          
          <el-form-item label="图片地址">
            <el-input v-model="formData.imageUrl" placeholder="图片URL" size="mini"></el-input>
          </el-form-item>
          
          <el-form-item label="背景尺寸">
            <el-select v-model="formData.backgroundSize" placeholder="请选择背景尺寸" style="width: 100%" size="mini">
              <el-option label="原始大小" value="auto"></el-option>
              <el-option label="包含" value="contain"></el-option>
              <el-option label="覆盖" value="cover"></el-option>
              <el-option label="100%" value="100% 100%"></el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="背景位置">
            <el-select v-model="formData.backgroundPosition" placeholder="请选择背景位置" style="width: 100%" size="mini">
              <el-option label="居中" value="center"></el-option>
              <el-option label="左上" value="left top"></el-option>
              <el-option label="左中" value="left center"></el-option>
              <el-option label="左下" value="left bottom"></el-option>
              <el-option label="右上" value="right top"></el-option>
              <el-option label="右中" value="right center"></el-option>
              <el-option label="右下" value="right bottom"></el-option>
              <el-option label="顶部居中" value="center top"></el-option>
              <el-option label="底部居中" value="center bottom"></el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="背景重复">
            <el-select v-model="formData.backgroundRepeat" placeholder="请选择背景重复方式" style="width: 100%" size="mini">
              <el-option label="不重复" value="no-repeat"></el-option>
              <el-option label="水平重复" value="repeat-x"></el-option>
              <el-option label="垂直重复" value="repeat-y"></el-option>
              <el-option label="重复" value="repeat"></el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="尺寸设置">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-input-number v-model="formData.width" :min="100" :step="10" placeholder="宽度" size="mini"></el-input-number>
              </el-col>
              <el-col :span="12">
                <el-input-number v-model="formData.height" :min="100" :step="10" placeholder="高度" size="mini"></el-input-number>
              </el-col>
            </el-row>
          </el-form-item>
          
          <el-form-item label="跳转页面">
            <!-- 使用系统页面选择器 -->
            <wx-page-select 
              :isSystemUrl="formData.isSystemUrl"  
              @switchChange="handleSwitchChange" 
              :page="formData.linkUrl" 
              @change="handlePageChange"
              :appId="appId"
              style="width: 100%">
            </wx-page-select>
          </el-form-item>
          
          <el-form-item label="显示延迟">
            <el-input-number v-model="formData.showDelay" :min="0" :max="10" :step="1" placeholder="显示延迟(秒)" size="mini"></el-input-number>
          </el-form-item>
          
          <el-form-item label="自动关闭">
            <el-switch v-model="formData.autoClose"></el-switch>
          </el-form-item>
          
          <el-form-item label="关闭延迟" v-if="formData.autoClose">
            <el-input-number v-model="formData.closeDelay" :min="1" :max="60" :step="1" placeholder="关闭延迟(秒)" size="mini"></el-input-number>
          </el-form-item>
        </el-form>
      </div>
    </settingSlot>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import WxPageSelect from '@/components/wx-page-select/Index.vue'
import MaterialList from '@/components/material/wxlist.vue'
import settingSlot from '../settingSlot';

export default {
  name: 'popupSetting',
  components: {
    WxPageSelect,
    MaterialList,
    settingSlot
  },
  props: {
    thememobile: { type: Object | Array },
    showData: {
      type: Object,
      default: () => {}
    },
    appId: {
      type: String,
      default: ''
    },
    clientType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      formDataCopy: {
        imageUrl: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
        linkUrl: '',
        isSystemUrl: true,
        width: 300,
        height: 400,
        autoClose: false,
        closeDelay: 3,
        showDelay: 1,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      },
      formData: {}
    }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpageWx.componentsList,
      clickComIndex: state => state.divpageWx.clickComIndex,
    })
  },
  mounted() {
    let that = this;
    if (that.IsEmptyObj(that.showData)) {
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy));
    } else {
      that.formData = JSON.parse(JSON.stringify(that.showData));
      // 确保isSystemUrl存在
      if (typeof that.formData.isSystemUrl === 'undefined') {
        that.formData.isSystemUrl = true;
      }
    }
    that.$set(that.componentsList[that.clickComIndex], 'data', this.formData);
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    cancel() {
      this.$emit('cancel');
    },
    reset() {
      let that = this;
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy));
      that.componentsList[that.clickComIndex]['data'] = this.formData;
    },
    confirm() {
      this.$emit('confirm', this.formData);
    },
    delete() {
      this.$emit('delete');
    },
    IsEmptyObj(obj) {
      return Object.keys(obj).length === 0;
    },
    // 处理图片选择
    handleImageChange(imageUrls) {
      if (imageUrls && imageUrls.length > 0) {
        this.formData.imageUrl = imageUrls[0];
      }
    },
    // 处理图片删除
    handleImageDelete() {
      this.formData.imageUrl = '';
    },
    // 处理页面选择变化
    handlePageChange(value) {
      this.formData.linkUrl = value;
      // 实时更新到父组件
      this.$emit('update', this.formData);
    },
    // 处理系统链接切换
    handleSwitchChange(value) {
      this.formData.isSystemUrl = value;
      // 实时更新到父组件
      this.$emit('update', this.formData);
    }
  },
  watch: {
    showData: {
      handler(newVal, oldVal) {
        this.formData = newVal ? JSON.parse(JSON.stringify(newVal)) : this.formData;
        // 确保isSystemUrl存在
        if (this.formData && typeof this.formData.isSystemUrl === 'undefined') {
          this.$set(this.formData, 'isSystemUrl', true);
        }
      },
      deep: true
    },
    formData: {
      handler(newVal) {
        // 当formData变化时实时更新到父组件
        this.$emit('update', newVal);
      },
      deep: true
    },
    thememobile() {}
  }
}
</script>

<style lang='less' scoped>
.popupSetting {
  /deep/ .el-form .el-form-item {
    margin-bottom: 8px;
  }

  .el-form-item {
    margin-bottom: 0;
  }
}
</style> 