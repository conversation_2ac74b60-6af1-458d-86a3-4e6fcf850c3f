export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  excelBtn: false,
  printBtn: false,
  viewBtn: false,
  menuWidth: 300,
  columnBtn: false,//列的显隐按钮
  addBtn: true,
  delBtn: true,
  // searchMenuSpan: 6,
  column: [
    {
      label: '主键',
      prop: 'id',
    },
    {
      label: '页面名称',
      prop: 'name',
    },
    {
      label: '主体颜色',
      prop: 'theme',
      hide: true,
    },
    {
      label: '页面头图',
      prop: 'imgurl',
      hide: true,
    },
    {
      label: '提示信息',
      prop: 'remind',
      hide: true,
    },
    {
      label: '邮箱规则',
      prop: 'emailFlag',
      hide: true,
    },
    {
      label: '登记邮箱',
      prop: 'email',
      hide: true,
    },
    {
      label: '用户绑定规则',
      prop: 'rules',
      hide: true,
    },
    {
      label: '通知id',
      prop: 'robotsId',
      hide: true,
    },
    {
      label: '协议文本',
      prop: 'content',
      hide: true,
    },
    {
      label: '短信签名',
      prop: 'signName',
      hide: true,
    },
    {
      label: '短信模版',
      prop: 'templateCode',
      hide: true,
    },
  ]
}
