// 约档规则
import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/appointrules/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/weixin/appointrules',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/weixin/appointrules/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/appointrules/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/appointrules',
    method: 'put',
    data: obj
  })
}
