/**
 * 配置参考:
 * https://cli.vuejs.org/zh/config/
 */
//后台网关地址
// const url = 'http://localhost:9999/'
//   const url = 'https://gongjunqi.com:9999'
//   const url = 'https://gocreateone.com:9999'
const url = 'http://drone.gocreateone.com'
const fs = require('fs')
module.exports = {
  lintOnSave: true,
  productionSourceMap: false,
  chainWebpack: config => {
    // 忽略的打包文件
    config.externals({
      'axios': 'axios'
    })
    const entry = config.entry('app')
    entry
      .add('babel-polyfill')
      .end()
    entry
      .add('classlist-polyfill')
      .end()
  },
  // 本地开发环境配置
  devServer: {
    //https相关配置
    // https: {
    //   key: fs.readFileSync('/Users/<USER>/IdeaProjects/gocreateone/gocreateone-plus-ui/cert/5766143_gongjunqi.com.key'),
    //   cert: fs.readFileSync('/Users/<USER>/IdeaProjects/gocreateone/gocreateone-plus-ui/cert/5766143_gongjunqi.com.pem')
    // },
    disableHostCheck: true,
    port:8082,
    //转发代理
    proxy: {
      '/auth': {
        target: url,
        ws: true,
        pathRewrite: {
          '^/auth': '/auth'
        }
      },
      '/upms': {
        target: url,
        ws: true,
        pathRewrite: {
          '^/upms': '/upms'
        }
      },
      '/code': {
        target: url,
        ws: true,
        pathRewrite: {
          '^/code': '/code'
        }
      },
      '/gen': {
        target: url,
        ws: true,
        pathRewrite: {
          '^/gen': '/gen'
        }
      },
      '/doc': {
        target: url,
        ws: true,
        pathRewrite: {
          '^/doc': '/doc'
        }
      },
      '/webjars': {
        target: url,
        ws: true,
        pathRewrite: {
          '^/webjars': '/webjars'
        }
      },
      '/swagger-resources': {
        target: url,
        ws: true,
        pathRewrite: {
          '^/swagger-resources': '/swagger-resources'
        }
      },
      '/weixin': {
        target: url,
        ws: true,
        pathRewrite: {
          '^/weixin': '/weixin'
        }
      },
      '/weixinapi': {
        target: url,
        ws: true,
        changeOrigin: true,
        pathRewrite: {
          '^/weixinapi': '/weixinapi'
        }
      },
      '/wxma': {
        target: url,
        ws: true,
        pathRewrite: {
          '^/wxma': '/wxma'
        }
      },
      '/mall': {
        target: url,
        ws: true,
        pathRewrite: {
          '^/mall': '/mall'
        }
      },
      '/payapi': {
        target: url,
        ws: true,
        pathRewrite: {
          '^/payapi': '/payapi'
        }
      },
    }
  }
}
