<!-- 商品分类组件 -->
<template>
  <div class="cuttingLineComponent" :style="{paddingTop:`${setData.paddingTop}px`,paddingBottom:`${setData.paddingBottom}px`}"  >
    <div v-show="setData.type==0" :class="setData.background && setData.background.indexOf('bg-') != -1 ? setData.background : ''" :style="{backgroundColor: `${setData.background.indexOf('bg-') != -1 ?'':setData.background}`,height:`${setData.height}px`,width:setData.width+'%'}">
    </div>
    <div v-show="setData.type==1" :style="{borderColor:setData.background}" style="border:1px solid;width: 100%">
    </div>
    <div v-show="setData.type==2" :style="{borderColor:setData.background}" style="border:1px dashed;width: 100%">
    </div>
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';

export default {
  data() {
    return {

    };
  },
  components: {},
  props: {
    thememobile: {type: Object | Array},
    setData: {type: Object | Array},
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {

  },
  mounted() {
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
  },
  watch: {
    componentsList(newVal, oldVal) {          //添加的时候触发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    }
  }
};
</script>
<style lang='less' scoped>

@import '../../colorui/main.css';
@import '../../colorui/icon.css';

.cuttingLineComponent {
  display: flex;
  justify-content: center;

}
</style>
