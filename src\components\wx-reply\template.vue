<template>
  <div>
    <span slot="label"><i class="el-icon-picture"></i> 模版消息</span>
    <div  class="template_from_box" v-if="objData.content">
      <div style="text-align: center">
        <el-button  type="success" @click="refresh">重新选择 <i class=" el-icon-refresh-left"></i></el-button>
      </div>
<!--      {{ objData.content }}-->
      <h3>{{selectedTemplate.title}}</h3>
      <el-form :rules="formRules" :ref="templateForm"  label-width="auto" size="small"
               :destroy-on-close="true">
        <el-form-item label="跳转链接" prop="url">
          <wx-page-select
            :isSystemUrl="objData.content.isSystemUrl"
            @switchChange="switchChange"
            :page="objData.content.url"
            @change="ensurePage"></wx-page-select>
        </el-form-item>
        <el-form-item label="小程序Id" prop="miniAppId">
          <el-input v-model="objData.content.miniprogram.appId" placeHolder="【可选】请输入需要点击跳转的小程序Id" :maxlength="25" style="width:450px" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="小程序地址" prop="miniUrl">
          <el-input v-model="objData.content.miniprogram.pagepath" placeHolder="【可选】请输入需要点击跳转的小程序地址" :maxlength="25" style="width:450px" show-word-limit></el-input>
        </el-form-item>
        <div v-for="(item,index) in objData.content.data" :key="index">
          <el-form-item :label="item.label" prop="name">
              <el-input v-model="item.value" :maxlength="25" show-word-limit></el-input>
          </el-form-item>
          <el-form-item :label="'字体颜色'" prop="name">
            <el-input v-model="item.color"  style="width:450px" >
              <template slot="append">
                <el-color-picker  size="mini" v-model="item.color"></el-color-picker>
              </template>
            </el-input>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div v-if="!objData.content">
      <el-row style="text-align: center">
        <el-col :span="24" class="col-select">
          <el-button type="success" @click="openTemplate">模版选择<i class="el-icon-circle-check el-icon--right"></i>
          </el-button>
        </el-col>
      </el-row>
    </div>
    <!--      模版预览框-->
    <el-dialog
      title="模版选择"
      :visible.sync="dialogTemplateVisible"
      :show-close="false"
      :append-to-body="true"
      width="40%"
      center>
      <div style="overflow: auto;height: 60vh">
        <div v-for="(item,index) in tableData" :key="index" style="padding-bottom: 3px" @click="selectTemplate(item)">
          <el-card shadow="hover" class="template_example">
            <div class="template_title ">{{ item.title }}</div>
            <div class="example_value ">
              <el-input type="textarea" resize="none" :readonly="true" v-model="item.example" autosize></el-input>
            </div>
          </el-card>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {getPage} from '@/api/wxmp/wxmptempmsg'
import WxPageSelect from '@/components/wx-page-select/Index.vue'
import {mapGetters} from 'vuex'
import store from "@/store"
import {h5HostMobile} from "@/config/env";

export default {
  name: "wxReplyTemplate",
  components: {WxPageSelect},
  props: {
    objData: {
      type: Object
    },
  },
  data() {
    return {
      h5HostMobile: h5HostMobile,
      tempPlayerObj: {
        type: '2'
      },
      formRules: {
        appId: [
          {required: true, message: '请选择公众号', trigger: 'submit'}
        ],
        name: [
          {required: true, message: '请输入二维码名称', trigger: 'submit'},
        ],
      },
      templateForm: [],
      selectedTemplate: "",
      tableData: [],//模版数据
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 30, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },

      tableLoading: false,
      dialogTemplateVisible: false,
      getExample: '',
      tempObj: new Map().set(this.objData.repType, Object.assign({}, this.objData)),
      fileList: [],
      uploadData: {
        "appId": this.objData.appId,
        "mediaType": this.objData.repType,
        "title": '',
        "introduction": ''
      },
      actionUrl: '/weixin/wxmaterial/materialFileUpload',
      headers: {
        Authorization: 'Bearer ' + store.getters.access_token
      }
    }
  },
  computed: {
    ...mapGetters(['permissions'])
  },
  methods: {
    deleteObj() {
      this.$delete(this.objData, 'repName')
      this.$delete(this.objData, 'repUrl')
      this.$delete(this.objData, 'content')
    },
    openTemplate() {
      if (this.objData.repType == 'template') {
        this.dialogTemplateVisible = true
        this.getPage(this.page, this.objData.appId)
      }
    },
    getPage(page, params) {
      this.tableLoading = true
      getPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
      }, params, this.paramsSearch)).then(res => {
        console.log(res.data)
        this.tableData = res.data.data.records
        this.page.total = res.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    //选中
    selectTemplate(val) {
      if(this.objData.content){
        this.$delete(this.objData, "content");
      }
      console.log("点击", val)
      this.selectedTemplate = val;
      val.content = JSON.parse(val.content);
      console.log("要的的值所属", val.content)
      let date = [];
      for (let i = 0; i <val.content.length; i++) {
        let obj = {label:val.content[i].keyName, name: val.content[i].keyValue, value: "", color: "#000000"};
        date.push(obj);
      }
      let params ={
        template_id: val.priTmplId,
        url: '',
        miniprogram: {},
        data: date
      }
      //构造传输值
      // this.templateForm
      this.$set(this.objData,"content",params)
      this.dialogTemplateVisible = false;
    },
    refresh(){
      this.openTemplate();
    },
    switchChange(val){
      console.log("传入值变了",val)
      this.objData.content.isSystemUrl = val;
      this.objData.content.url = "";
    },
    ensurePage(val){
      if(val && val.indexOf('page_id') != -1){
        this.objData.content.url = this.h5HostMobile +val;
      }
    },
  }
};
</script>

<style lang="scss" scoped>

.template_from_box{
  margin: 0 30px 0 10px;
}


.template_example :hover {
  cursor: pointer;
  color: red;
}

.template_title {
  padding: 0 0 0 15px;
  font-weight: bold;
}

.example_value /deep/ .el-textarea__inner {
  border: 0;
}


</style>
