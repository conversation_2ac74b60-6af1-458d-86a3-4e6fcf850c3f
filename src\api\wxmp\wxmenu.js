import request from '@/router/axios'

export function save(obj) {
  return request({
    url: '/weixin/wxmenu',
    method: 'post',
    data: obj
  })
}

export function saveAndRelease(obj) {
  return request({
    url: '/weixin/wxmenu/release',
    method: 'post',
    data: obj
  })
}

export function getList(query) {
  return request({
    url: '/weixin/wxmenu/list',
    method: 'get',
    params: query
  })
}

export function delByRuleId(ruleId) {
  return request({
    url: '/weixin/wxmenu/' + ruleId,
    method: 'delete'
  })
}
