<template>
  <div class="rateControlComponent" :style="{background: `${setData.backgroundColor}`, marginBottom: `${setData.pageSpacing}px`}">
    <div :style="{color: `${setData.titleColor}`, fontSize: `${setData.titleSize}px`,fontWeight:`${setData.titleWeight?'bold':'normal'}`}">{{setData.title}}<i v-show="setData.required" style="color: #FF0000">*</i></div>
    <div :style="{color: `${setData.describeColor}`, fontSize: `${setData.describeSize}px`,fontWeight:`${setData.describeWeight?'bold':'normal'}`}">{{setData.describe}}</div>
    <div class="rate">
      <el-rate
        :style="'!important'+{color:`${setData.selectColor}`}"
        v-model="initValue"
        :show-text="setData.rateType==1?true:false"
        :show-score="setData.rateType==2?true:false"
        :text-color="setData.selectColor"
        :disabled="true"
        :colors="setData.rateColors"
        :texts="setData.textList"
        :allow-half="setData.isHalf"
        :void-color="setData.voidColor"
        >
      </el-rate>
    </div>
  </div>
</template>

<script>
import { mapState , mapGetters , mapMutations , mapActions } from 'vuex';
export default {
  data() {
    return {initValue:0};
  },
  components: { },
  props: {
    theme : { type: Object | Array },
    setData : { type: Object | Array },
    cId     : { type: Number },
    noEditor: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {
  },
  mounted() {
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
  },
  watch:{
    setData:{
      handler(newVal, oldVal) {
        this.initValue = newVal.initRate;
      },
      deep:true
    },
    componentsList(newVal, oldVal){          //添加的时候出发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    }
  },
  beforeDestroy(){
    // this.$root.Bus.$off('addHotSpot')
  }
};
</script>
<style lang='less' scoped>
.rateControlComponent {
  position: relative;
  display: block;
  width: 100%;
  padding: 5px;
  background: #ffffff;
}

</style>
