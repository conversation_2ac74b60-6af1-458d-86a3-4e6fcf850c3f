/*
  ColorUi for uniApp  v2.1.6 | by 文晓港 2019-05-31 10:44:24
  仅供学习交流，如作它用所承受的法律责任一概与作者无关

  *使用ColorUi开发扩展与插件时，请注明基于ColorUi开发

  （QQ交流群：240787041）
*/

/* ==================
        初始化
 ==================== */
body {
	background-color: #f1f1f1;
	font-size: 14px;
	color: #333333;
	font-family: Helvetica Neue, Helvetica, sans-serif;
}

div,
scroll-div,
swiper,
button,
input,
textarea,
label,
navigator,
image {
	box-sizing: border-box;
}

.round {
	border-radius: 5000px;
}

.radius {
	border-radius: 3px;
}

/* ==================
          图片
 ==================== */

image {
	max-width: 100%;
	display: inline-block;
	position: relative;
	z-index: 0;
}

image.loading::before {
	content: "";
	background-color: #f5f5f5;
	display: block;
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: -2;
}

image.loading::after {
	content: "\e7f1";
	font-family: "cuIcon";
	position: absolute;
	top: 0;
	left: 0;
	width: 16px;
	height: 16px;
	line-height: 16px;
	right: 0;
	bottom: 0;
	z-index: -1;
	font-size: 16px;
	margin: auto;
	color: #ccc;
	-webkit-animation: cuIcon-spin 2s infinite linear;
	animation: cuIcon-spin 2s infinite linear;
	display: block;
}

.response {
	width: 100%;
}

/* ==================
         开关
 ==================== */

switch,
checkbox,
radio {
	position: relative;
}

switch::after,
switch::before {
	font-family: "cuIcon";
	content: "\e645";
	position: absolute;
	color: #ffffff !important;
	top: 0%;
	left: 0px;
	font-size: 14px;
	line-height: 26px;
	width: 50%;
	text-align: center;
	pointer-events: none;
	transform: scale(0, 0);
	transition: all 0.3s ease-in-out 0s;
	z-index: 9;
	bottom: 0;
	height: 26px;
	margin: auto;
}

switch::before {
	content: "\e646";
	right: 0;
	transform: scale(1, 1);
	left: auto;
}

switch[checked]::after,
switch.checked::after {
	transform: scale(1, 1);
}

switch[checked]::before,
switch.checked::before {
	transform: scale(0, 0);
}

/* #ifndef MP-ALIPAY */
radio::before,
checkbox::before {
	font-family: "cuIcon";
	content: "\e645";
	position: absolute;
	color: #ffffff !important;
	top: 50%;
	margin-top: -8px;
	right: 5px;
	font-size: 16px;
	line-height: 16px;
	pointer-events: none;
	transform: scale(1, 1);
	transition: all 0.3s ease-in-out 0s;
	z-index: 9;
}

radio .wx-radio-input,
checkbox .wx-checkbox-input,
radio .uni-radio-input,
checkbox .uni-checkbox-input {
	margin: 0;
	width: 24px;
	height: 24px;
}

checkbox.round .wx-checkbox-input,
checkbox.round .uni-checkbox-input {
	border-radius: 100px;
}

/* #endif */

switch[checked]::before {
	transform: scale(0, 0);
}

switch .wx-switch-input,
switch .uni-switch-input {
	border: none;
	padding: 0 24px;
	width: 48px;
	height: 26px;
	margin: 0;
	border-radius: 100px;
}

switch .wx-switch-input:not([class*="bg-"]),
switch .uni-switch-input:not([class*="bg-"]) {
	background: #8799a3 !important;
}

switch .wx-switch-input::after,
switch .uni-switch-input::after {
	margin: auto;
	width: 26px;
	height: 26px;
	border-radius: 100px;
	left: 0px;
	top: 0px;
	bottom: 0px;
	position: absolute;
	transform: scale(0.9, 0.9);
	transition: all 0.1s ease-in-out 0s;
}

switch .wx-switch-input.wx-switch-input-checked::after,
switch .uni-switch-input.uni-switch-input-checked::after {
	margin: auto;
	left: 22px;
	box-shadow: none;
	transform: scale(0.9, 0.9);
}

radio-group {
	display: inline-block;
}



switch.radius .wx-switch-input::after,
switch.radius .wx-switch-input,
switch.radius .wx-switch-input::before,
switch.radius .uni-switch-input::after,
switch.radius .uni-switch-input,
switch.radius .uni-switch-input::before {
	border-radius: 10px;
}

switch .wx-switch-input::before,
radio.radio::before,
checkbox .wx-checkbox-input::before,
radio .wx-radio-input::before,
switch .uni-switch-input::before,
radio.radio::before,
checkbox .uni-checkbox-input::before,
radio .uni-radio-input::before {
	display: none;
}

radio.radio[checked]::after,
radio.radio .uni-radio-input-checked::after {
	content: "";
	background-color: transparent;
	display: block;
	position: absolute;
	width: 8px;
	height: 8px;
	z-index: 999;
	top: 0px;
	left: 0px;
	right: 0;
	bottom: 0;
	margin: auto;
	border-radius: 200px;
	/* #ifndef MP */
	border: 7px solid #ffffff !important;
	/* #endif */

	/* #ifdef MP */
	border: 8px solid #ffffff !important;
	/* #endif */
}

.switch-sex::after {
	content: "\e71c";
}

.switch-sex::before {
	content: "\e71a";
}

.switch-sex .wx-switch-input,
.switch-sex .uni-switch-input {
	background: #e54d42 !important;
	border-color: #e54d42 !important;
}

.switch-sex[checked] .wx-switch-input,
.switch-sex.checked .uni-switch-input {
	background: #0081ff !important;
	border-color: #0081ff !important;
}

switch.red[checked] .wx-switch-input.wx-switch-input-checked,
checkbox.red[checked] .wx-checkbox-input,
radio.red[checked] .wx-radio-input,
switch.red.checked .uni-switch-input.uni-switch-input-checked,
checkbox.red.checked .uni-checkbox-input,
radio.red.checked .uni-radio-input {
	background-color: #e54d42 !important;
	border-color: #e54d42 !important;
	color: #ffffff !important;
}

switch.orange[checked] .wx-switch-input,
checkbox.orange[checked] .wx-checkbox-input,
radio.orange[checked] .wx-radio-input,
switch.orange.checked .uni-switch-input,
checkbox.orange.checked .uni-checkbox-input,
radio.orange.checked .uni-radio-input {
	background-color: #f37b1d !important;
	border-color: #f37b1d !important;
	color: #ffffff !important;
}

switch.yellow[checked] .wx-switch-input,
checkbox.yellow[checked] .wx-checkbox-input,
radio.yellow[checked] .wx-radio-input,
switch.yellow.checked .uni-switch-input,
checkbox.yellow.checked .uni-checkbox-input,
radio.yellow.checked .uni-radio-input {
	background-color: #fbbd08 !important;
	border-color: #fbbd08 !important;
	color: #333333 !important;
}

switch.olive[checked] .wx-switch-input,
checkbox.olive[checked] .wx-checkbox-input,
radio.olive[checked] .wx-radio-input,
switch.olive.checked .uni-switch-input,
checkbox.olive.checked .uni-checkbox-input,
radio.olive.checked .uni-radio-input {
	background-color: #8dc63f !important;
	border-color: #8dc63f !important;
	color: #ffffff !important;
}

switch.green[checked] .wx-switch-input,
switch[checked] .wx-switch-input,
checkbox.green[checked] .wx-checkbox-input,
checkbox[checked] .wx-checkbox-input,
radio.green[checked] .wx-radio-input,
radio[checked] .wx-radio-input,
switch.green.checked .uni-switch-input,
switch.checked .uni-switch-input,
checkbox.green.checked .uni-checkbox-input,
checkbox.checked .uni-checkbox-input,
radio.green.checked .uni-radio-input,
radio.checked .uni-radio-input {
	background-color: #39b54a !important;
	border-color: #39b54a !important;
	color: #ffffff !important;
	border-color: #39B54A !important;
}

switch.cyan[checked] .wx-switch-input,
checkbox.cyan[checked] .wx-checkbox-input,
radio.cyan[checked] .wx-radio-input,
switch.cyan.checked .uni-switch-input,
checkbox.cyan.checked .uni-checkbox-input,
radio.cyan.checked .uni-radio-input {
	background-color: #1cbbb4 !important;
	border-color: #1cbbb4 !important;
	color: #ffffff !important;
}

switch.blue[checked] .wx-switch-input,
checkbox.blue[checked] .wx-checkbox-input,
radio.blue[checked] .wx-radio-input,
switch.blue.checked .uni-switch-input,
checkbox.blue.checked .uni-checkbox-input,
radio.blue.checked .uni-radio-input {
	background-color: #0081ff !important;
	border-color: #0081ff !important;
	color: #ffffff !important;
}

switch.purple[checked] .wx-switch-input,
checkbox.purple[checked] .wx-checkbox-input,
radio.purple[checked] .wx-radio-input,
switch.purple.checked .uni-switch-input,
checkbox.purple.checked .uni-checkbox-input,
radio.purple.checked .uni-radio-input {
	background-color: #6739b6 !important;
	border-color: #6739b6 !important;
	color: #ffffff !important;
}

switch.mauve[checked] .wx-switch-input,
checkbox.mauve[checked] .wx-checkbox-input,
radio.mauve[checked] .wx-radio-input,
switch.mauve.checked .uni-switch-input,
checkbox.mauve.checked .uni-checkbox-input,
radio.mauve.checked .uni-radio-input {
	background-color: #9c26b0 !important;
	border-color: #9c26b0 !important;
	color: #ffffff !important;
}

switch.pink[checked] .wx-switch-input,
checkbox.pink[checked] .wx-checkbox-input,
radio.pink[checked] .wx-radio-input,
switch.pink.checked .uni-switch-input,
checkbox.pink.checked .uni-checkbox-input,
radio.pink.checked .uni-radio-input {
	background-color: #e03997 !important;
	border-color: #e03997 !important;
	color: #ffffff !important;
}

switch.brown[checked] .wx-switch-input,
checkbox.brown[checked] .wx-checkbox-input,
radio.brown[checked] .wx-radio-input,
switch.brown.checked .uni-switch-input,
checkbox.brown.checked .uni-checkbox-input,
radio.brown.checked .uni-radio-input {
	background-color: #a5673f !important;
	border-color: #a5673f !important;
	color: #ffffff !important;
}

switch.grey[checked] .wx-switch-input,
checkbox.grey[checked] .wx-checkbox-input,
radio.grey[checked] .wx-radio-input,
switch.grey.checked .uni-switch-input,
checkbox.grey.checked .uni-checkbox-input,
radio.grey.checked .uni-radio-input {
	background-color: #8799a3 !important;
	border-color: #8799a3 !important;
	color: #ffffff !important;
}

switch.gray[checked] .wx-switch-input,
checkbox.gray[checked] .wx-checkbox-input,
radio.gray[checked] .wx-radio-input,
switch.gray.checked .uni-switch-input,
checkbox.gray.checked .uni-checkbox-input,
radio.gray.checked .uni-radio-input {
	background-color: #f0f0f0 !important;
	border-color: #f0f0f0 !important;
	color: #333333 !important;
}

switch.black[checked] .wx-switch-input,
checkbox.black[checked] .wx-checkbox-input,
radio.black[checked] .wx-radio-input,
switch.black.checked .uni-switch-input,
checkbox.black.checked .uni-checkbox-input,
radio.black.checked .uni-radio-input {
	background-color: #333333 !important;
	border-color: #333333 !important;
	color: #ffffff !important;
}

switch.white[checked] .wx-switch-input,
checkbox.white[checked] .wx-checkbox-input,
radio.white[checked] .wx-radio-input,
switch.white.checked .uni-switch-input,
checkbox.white.checked .uni-checkbox-input,
radio.white.checked .uni-radio-input {
	background-color: #ffffff !important;
	border-color: #ffffff !important;
	color: #333333 !important;
}

/* ==================
          边框
 ==================== */

/* -- 实线 -- */

.solid,
.solid-top,
.solid-right,
.solid-bottom,
.solid-left,
.solids,
.solids-top,
.solids-right,
.solids-bottom,
.solids-left,
.dashed,
.dashed-top,
.dashed-right,
.dashed-bottom,
.dashed-left {
	position: relative;
}

.solid::after,
.solid-top::after,
.solid-right::after,
.solid-bottom::after,
.solid-left::after,
.solids::after,
.solids-top::after,
.solids-right::after,
.solids-bottom::after,
.solids-left::after,
.dashed::after,
.dashed-top::after,
.dashed-right::after,
.dashed-bottom::after,
.dashed-left::after {
	content: " ";
	width: 200%;
	height: 200%;
	position: absolute;
	top: 0;
	left: 0;
	border-radius: inherit;
	transform: scale(0.5);
	transform-origin: 0 0;
	pointer-events: none;
	box-sizing: border-box;
}

.solid::after {
	border: 1px solid rgba(0, 0, 0, 0.1);
}

.solid-top::after {
	border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.solid-right::after {
	border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.solid-bottom::after {
	border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.solid-left::after {
	border-left: 1px solid rgba(0, 0, 0, 0.1);
}

.solids::after {
	border: 4px solid #eee;
}

.solids-top::after {
	border-top: 4px solid #eee;
}

.solids-right::after {
	border-right: 4px solid #eee;
}

.solids-bottom::after {
	border-bottom: 4px solid #eee;
}

.solids-left::after {
	border-left: 4px solid #eee;
}

/* -- 虚线 -- */

.dashed::after {
	border: 1px dashed #ddd;
}

.dashed-top::after {
	border-top: 1px dashed #ddd;
}

.dashed-right::after {
	border-right: 1px dashed #ddd;
}

.dashed-bottom::after {
	border-bottom: 1px dashed #ddd;
}

.dashed-left::after {
	border-left: 1px dashed #ddd;
}

/* -- 阴影 -- */

.shadow[class*='white'] {
	--ShadowSize: 0 1px 3px;
}

.shadow-lg {
	--ShadowSize: 0px 40px 100px 0px;
}

.shadow-warp {
	position: relative;
	box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.shadow-warp:before,
.shadow-warp:after {
	position: absolute;
	content: "";
	top: 20px;
	bottom: 30px;
	left: 20px;
	width: 50%;
	box-shadow: 0 30px 20px rgba(0, 0, 0, 0.2);
	transform: rotate(-3deg);
	z-index: -1;
}

.shadow-warp:after {
	right: 20px;
	left: auto;
	transform: rotate(3deg);
}

.shadow-blur {
	position: relative;
}

.shadow-blur::before {
	content: "";
	display: block;
	background: inherit;
	filter: blur(10px);
	position: absolute;
	width: 100%;
	height: 100%;
	top: 10px;
	left: 10px;
	z-index: -1;
	opacity: 0.4;
	transform-origin: 0 0;
	border-radius: inherit;
	transform: scale(1, 1);
}

/* ==================
          按钮
 ==================== */

.cu-btn {
	position: relative;
	border: 0px;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
	padding: 0 30px;
	font-size: 24px;
	height: 48px;
	line-height: 1;
	text-align: center;
	text-decoration: none;
	overflow: visible;
	margin-left: initial;
	transform: translate(0px, 0px);
	margin-right: initial;
}

.cu-btn::after {
	display: none;
}

.cu-btn:not([class*="bg-"]) {
	background-color: #f0f0f0;
}

.cu-btn[class*="line"] {
	background-color: transparent;
}

.cu-btn[class*="line"]::after {
	content: " ";
	display: block;
	width: 200%;
	height: 200%;
	position: absolute;
	top: 0;
	left: 0;
	border: 1px solid currentColor;
	transform: scale(0.5);
	transform-origin: 0 0;
	box-sizing: border-box;
	border-radius: 12px;
	z-index: 1;
	pointer-events: none;
}

.cu-btn.round[class*="line"]::after {
	border-radius: 1000px;
}

.cu-btn[class*="lines"]::after {
	border: 3px solid currentColor;
}

.cu-btn[class*="bg-"]::after {
	display: none;
}

.cu-btn.sm {
	padding: 0 10px;
	font-size: 10px;
	height: 24px;
}

.cu-btn.lg {
	padding: 0 20px;
	font-size: 16px;
	height: 40px;
}

.cu-btn.cuIcon.sm {
	width: 24px;
	height: 24px;
}

.cu-btn.cuIcon {
	width: 32px;
	height: 32px;
	border-radius: 250px;
	padding: 0;
}

button.cuIcon.lg {
	width: 40px;
	height: 40px;
}

.cu-btn.shadow-blur::before {
	top: 2px;
	left: 2px;
	filter: blur(3px);
	opacity: 0.6;
}

.cu-btn.button-hover {
	transform: translate(1px, 1px);
}

.block {
	display: block;
}

.cu-btn.block {
	display: flex;
}

.cu-btn[disabled] {
	opacity: 0.6;
	color: #ffffff;
}

/* ==================
          徽章
 ==================== */

.cu-tag {
	font-size: 12px;
	vertical-align: middle;
	position: relative;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
	padding: 0px 8px;
	height: 24px;
	font-family: Helvetica Neue, Helvetica, sans-serif;
	white-space: nowrap;
}

.cu-tag:not([class*="bg"]):not([class*="line"]) {
	background-color: #f1f1f1;
}

.cu-tag[class*="line-"]::after {
	content: " ";
	width: 200%;
	height: 200%;
	position: absolute;
	top: 0;
	left: 0;
	border: 1px solid currentColor;
	transform: scale(0.5);
	transform-origin: 0 0;
	box-sizing: border-box;
	border-radius: inherit;
	z-index: 1;
	pointer-events: none;
}

.cu-tag.radius[class*="line"]::after {
	border-radius: 12px;
}

.cu-tag.round[class*="line"]::after {
	border-radius: 1000px;
}

.cu-tag[class*="line-"]::after {
	border-radius: 0;
}

.cu-tag+.cu-tag {
	margin-left: 10px;
}

.cu-tag.sm {
	font-size: 10px;
	padding: 0px 6px;
	height: 16px;
}

.cu-capsule {
	display: inline-flex;
	vertical-align: middle;
}

.cu-capsule+.cu-capsule {
	margin-left: 10px;
}

.cu-capsule .cu-tag {
	margin: 0;
}

.cu-capsule .cu-tag[class*="line-"]:last-child::after {
	border-left: 0px solid transparent;
}

.cu-capsule .cu-tag[class*="line-"]:first-child::after {
	border-right: 0px solid transparent;
}

.cu-capsule.radius .cu-tag:first-child {
	border-top-left-radius: 4px;
	border-bottom-left-radius: 4px;
}

.cu-capsule.radius .cu-tag:last-child::after,
.cu-capsule.radius .cu-tag[class*="line-"] {
	border-top-right-radius: 6px;
	border-bottom-right-radius: 6px;
}

.cu-capsule.round .cu-tag:first-child {
	border-top-left-radius: 200px;
	border-bottom-left-radius: 200px;
	text-indent: 2px;
}

.cu-capsule.round .cu-tag:last-child::after,
.cu-capsule.round .cu-tag:last-child {
	border-top-right-radius: 200px;
	border-bottom-right-radius: 200px;
	text-indent: -2px;
}

.cu-tag.badge {
	border-radius: 200px;
	position: absolute;
	top: -10px;
	right: -10px;
	font-size: 20px;
	padding: 0px 10px;
	height: 24px;
	color: #ffffff;
}

.cu-tag.badge:not([class*="bg-"]) {
	background-color: #dd514c;
}

.cu-tag:empty:not([class*="cuIcon-"]) {
	padding: 0px;
	width: 10px;
	height: 10px;
	top: -2px;
	right: -2px;
}

.cu-tag[class*="cuIcon-"] {
	width: 16px;
	height: 16px;
	top: -2px;
	right: -2px;
}

/* ==================
          头像
 ==================== */

.cu-avatar {
	font-variant: small-caps;
	margin: 0;
	padding: 0;
	display: inline-flex;
	text-align: center;
	justify-content: center;
	align-items: center;
	background-color: #ccc;
	color: #ffffff;
	white-space: nowrap;
	position: relative;
	width: 48px;
	height: 48px;
	background-size: cover;
	background-position: center;
	vertical-align: middle;
	font-size: 1.5em;
}

.cu-avatar.sm {
	width: 44px;
	height: 44px;
	font-size: 1em;
}

.cu-avatar.lg {
	width: 58px;
	height: 58px;
	font-size: 2em;
}

.cu-avatar.xl {
	width: 124px;
	height: 124px;
	font-size: 2.5em;
}

.cu-avatar .avatar-text {
	font-size: 0.4em;
}

.cu-avatar-group {
	direction: rtl;
	unicode-bidi: bidi-override;
	padding: 0 10px 0 40px;
	display: inline-block;
}

.cu-avatar-group .cu-avatar {
	margin-left: -30px;
	border: 2px solid #f1f1f1;
	vertical-align: middle;
}

.cu-avatar-group .cu-avatar.sm {
	margin-left: -20px;
	border: 1px solid #f1f1f1;
}

/* ==================
         进度条
 ==================== */

.cu-progress {
	overflow: hidden;
	height: 24px;
	background-color: #ebeef5;
	display: inline-flex;
	align-items: center;
	width: 100%;
}

.cu-progress+div,
.cu-progress+text {
	line-height: 1;
}

.cu-progress.xs {
	height: 10px;
}

.cu-progress.sm {
	height: 20px;
}

.cu-progress div {
	width: 0;
	height: 100%;
	align-items: center;
	display: flex;
	justify-items: flex-end;
	justify-content: space-around;
	font-size: 20px;
	color: #ffffff;
	transition: width 0.6s ease;
}

.cu-progress text {
	align-items: center;
	display: flex;
	font-size: 20px;
	color: #333333;
	text-indent: 10px;
}

.cu-progress.text-progress {
	padding-right: 60px;
}

.cu-progress.striped div {
	background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
	background-size: 36px 36px;
}

.cu-progress.active div {
	animation: progress-stripes 2s linear infinite;
}

@keyframes progress-stripes {
	from {
		background-position: 36px 0;
	}

	to {
		background-position: 0 0;
	}
}

/* ==================
          加载
 ==================== */

.cu-load {
	display: block;
	line-height: 3em;
	text-align: center;
}

.cu-load::before {
	font-family: "cuIcon";
	display: inline-block;
	margin-right: 3px;
}

.cu-load.loading::before {
	content: "\e67a";
	animation: cuIcon-spin 2s infinite linear;
}

.cu-load.loading::after {
	content: "加载中...";
}

.cu-load.over::before {
	content: "\e64a";
}

.cu-load.over::after {
	content: "没有更多了";
}

.cu-load.erro::before {
	content: "\e658";
}

.cu-load.erro::after {
	content: "加载失败";
}

.cu-load.load-cuIcon::before {
	font-size: 16px;
}

.cu-load.load-cuIcon::after {
	display: none;
}

.cu-load.load-cuIcon.over {
	display: none;
}

.cu-load.load-modal {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 140px;
	left: 0;
	margin: auto;
	width: 260px;
	height: 260px;
	background-color: #ffffff;
	border-radius: 10px;
	box-shadow: 0 0 0px 2000px rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	flex-direction: column;
	justify-content: center;
	font-size: 24px;
	z-index: 9999;
	line-height: 2.4em;
}

.cu-load.load-modal [class*="cuIcon-"] {
	font-size: 60px;
}

.cu-load.load-modal image {
	width: 70px;
	height: 70px;
}

.cu-load.load-modal::after {
	content: "";
	position: absolute;
	background-color: #ffffff;
	border-radius: 50%;
	width: 200px;
	height: 200px;
	font-size: 10px;
	border-top: 3px solid rgba(0, 0, 0, 0.05);
	border-right: 3px solid rgba(0, 0, 0, 0.05);
	border-bottom: 3px solid rgba(0, 0, 0, 0.05);
	border-left: 3px solid #f37b1d;
	animation: cuIcon-spin 1s infinite linear;
	z-index: -1;
}

.load-progress {
	pointer-events: none;
	top: 0;
	position: fixed;
	width: 100%;
	left: 0;
	z-index: 2000;
}

.load-progress.hide {
	display: none;
}

.load-progress .load-progress-bar {
	position: relative;
	width: 100%;
	height: 2px;
	overflow: hidden;
	transition: all 200ms ease 0s;
}

.load-progress .load-progress-spinner {
	position: absolute;
	top: 10px;
	right: 10px;
	z-index: 2000;
	display: block;
}

.load-progress .load-progress-spinner::after {
	content: "";
	display: block;
	width:12px;
	height:12px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	border: solid 2px transparent;
	border-top-color: inherit;
	border-left-color: inherit;
	border-radius: 50%;
	-webkit-animation: load-progress-spinner 0.4s linear infinite;
	animation: load-progress-spinner 0.4s linear infinite;
}

@-webkit-keyframes load-progress-spinner {
	0% {
		-webkit-transform: rotate(0);
		transform: rotate(0);
	}

	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

@keyframes load-progress-spinner {
	0% {
		-webkit-transform: rotate(0);
		transform: rotate(0);
	}

	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

/* ==================
          列表
 ==================== */
.grayscale {
	filter: grayscale(1);
}

.cu-list+.cu-list {
	margin-top: 30px
}

.cu-list>.cu-item {
	transition: all .6s ease-in-out 0s;
	transform: translateX(0px)
}

.cu-list>.cu-item.move-cur {
	transform: translateX(-260px)
}

.cu-list>.cu-item .move {
	position: absolute;
	right: 0;
	display: flex;
	width: 260px;
	height: 100%;
	transform: translateX(100%)
}

.cu-list>.cu-item .move div {
	display: flex;
	flex: 1;
	justify-content: center;
	align-items: center
}

.cu-list.menu-avatar {
	overflow: hidden;
}

.cu-list.menu-avatar>.cu-item {
	position: relative;
	display: flex;
	padding-right: 10px;
	height: 140px;
	background-color: #ffffff;
	justify-content: flex-end;
	align-items: center
}

.cu-list.menu-avatar>.cu-item>.cu-avatar {
	position: absolute;
	left: 30px
}

.cu-list.menu-avatar>.cu-item .flex .text-cut {
	max-width: 510px
}

.cu-list.menu-avatar>.cu-item .content {
	position: absolute;
	left: 143px;
	width: calc(100% - 58px - 60px - 120px - 20px);
	line-height: 1.6em;
}

.cu-list.menu-avatar>.cu-item .content.flex-sub {
	width: calc(100% - 58px - 60px - 20px);
}

.cu-list.menu-avatar>.cu-item .content>div:first-child {
	font-size: 30px;
	display: flex;
	align-items: center
}

.cu-list.menu-avatar>.cu-item .content .cu-tag.sm {
	display: inline-block;
	margin-left: 10px;
	height: 24px;
	font-size: 10px;
	line-height: 16px
}

.cu-list.menu-avatar>.cu-item .action {
	width: 100px;
	text-align: center
}

.cu-list.menu-avatar>.cu-item .action div+div {
	margin-top: 10px
}

.cu-list.menu-avatar.comment>.cu-item .content {
	position: relative;
	left: 0;
	width: auto;
	flex: 1;
}

.cu-list.menu-avatar.comment>.cu-item {
	padding: 30px 30px 30px 120px;
	height: auto
}

.cu-list.menu-avatar.comment .cu-avatar {
	align-self: flex-start
}

.cu-list.menu>.cu-item {
	position: relative;
	display: flex;
	padding: 0 30px;
	min-height: 100px;
	background-color: #ffffff;
	justify-content: space-between;
	align-items: center
}

.cu-list.menu>.cu-item:last-child:after {
	border: none
}

.cu-list.menu-avatar>.cu-item:after,
.cu-list.menu>.cu-item:after {
	position: absolute;
	top: 0;
	left: 0;
	box-sizing: border-box;
	width: 200%;
	height: 200%;
	border-bottom: 1px solid #ddd;
	border-radius: inherit;
	content: " ";
	transform: scale(.5);
	transform-origin: 0 0;
	pointer-events: none
}

.cu-list.menu>.cu-item.grayscale {
	background-color: #f5f5f5
}

.cu-list.menu>.cu-item.cur {
	background-color: #fcf7e9
}

.cu-list.menu>.cu-item.arrow {
	padding-right: 90px
}

.cu-list.menu>.cu-item.arrow:before {
	position: absolute;
	top: 0;
	right: 30px;
	bottom: 0;
	display: block;
	margin: auto;
	width: 30px;
	height: 30px;
	color: #8799a3;
	content: "\e6a3";
	text-align: center;
	font-size: 17px;
	font-family: cuIcon;
	line-height: 30px
}

.cu-list.menu>.cu-item button.content {
	padding: 0;
	background-color: transparent;
	justify-content: flex-start
}

.cu-list.menu>.cu-item button.content:after {
	display: none
}

.cu-list.menu>.cu-item .cu-avatar-group .cu-avatar {
	border-color: #ffffff
}

.cu-list.menu>.cu-item .content>div:first-child {
	display: flex;
	align-items: center
}

.cu-list.menu>.cu-item .content>text[class*=cuIcon] {
	display: inline-block;
	margin-right: 10px;
	width: 1.6em;
	text-align: center
}

.cu-list.menu>.cu-item .content>image {
	display: inline-block;
	margin-right: 10px;
	width: 1.6em;
	height: 1.6em;
	vertical-align: middle
}

.cu-list.menu>.cu-item .content {
	font-size: 30px;
	line-height: 1.6em;
	flex: 1
}

.cu-list.menu>.cu-item .content .cu-tag.sm {
	display: inline-block;
	margin-left: 10px;
	height: 24px;
	font-size: 10px;
	line-height: 16px
}

.cu-list.menu>.cu-item .action .cu-tag:empty {
	right: 10px
}

.cu-list.menu {
	display: block;
	overflow: hidden
}

.cu-list.menu.sm-border>.cu-item:after {
	left: 30px;
	width: calc(200% - 120px)
}

.cu-list.grid>.cu-item {
	position: relative;
	display: flex;
	/*padding: 10px 0 15px;*/
	transition-duration: 0s;
	flex-direction: column
}

.cu-list.grid>.cu-item:after {
	position: absolute;
	top: 0;
	left: 0;
	box-sizing: border-box;
	width: 200%;
	height: 200%;
	border-right: 1px solid rgba(0, 0, 0, .1);
	border-bottom: 1px solid rgba(0, 0, 0, .1);
	border-radius: inherit;
	content: " ";
	transform: scale(.5);
	transform-origin: 0 0;
	pointer-events: none
}

.cu-list.grid>.cu-item div {
	display: block;
	margin-top: 5px;
	color: #888;
	font-size: 14px;
	line-height: 20px
}

.cu-list.grid>.cu-item [class*=cuIcon] {
	position: relative;
	display: block;
	margin-top: 10px;
	width: 100%;
	font-size: 24px
}

.cu-list.grid>.cu-item .cu-tag {
	right: auto;
	left: 50%;
	margin-left: 20px
}

.cu-list.grid {
	background-color: #ffffff;
	text-align: center
}

.cu-list.grid.no-border>.cu-item {
	/*padding-top: 5px;*/
	/*padding-bottom: 10px*/
}

.cu-list.grid.no-border>.cu-item:after {
	border: none
}

.cu-list.grid.no-border {
	/*padding: 10px 5px*/
}

.cu-list.grid.col-3>.cu-item:nth-child(3n):after,
.cu-list.grid.col-4>.cu-item:nth-child(4n):after,
.cu-list.grid.col-5>.cu-item:nth-child(5n):after {
	border-right-width: 0
}

.cu-list.card-menu {
	overflow: hidden;
	margin-right: 15px;
	margin-left: 15px;
	border-radius: 10px
}


/* ==================
          操作条
 ==================== */

.cu-bar {
	display: flex;
	position: relative;
	align-items: center;
	min-height: 50px;
	justify-content: space-between;
}

.cu-bar .action {
	display: flex;
	align-items: center;
	height: 100%;
	justify-content: center;
	max-width: 100%;
}

.cu-bar .action.border-title {
	position: relative;
	top: -5px;
}

.cu-bar .action.border-title text[class*="bg-"]:last-child {
	position: absolute;
	bottom: -0.5rem;
	min-width: 2rem;
	height: 3px;
	left: 0;
}

.cu-bar .action.sub-title {
	position: relative;
	top: -0.2rem;
}

.cu-bar .action.sub-title text {
	position: relative;
	z-index: 1;
}

.cu-bar .action.sub-title text[class*="bg-"]:last-child {
	position: absolute;
	display: inline-block;
	bottom: -0.2rem;
	border-radius: 4px;
	width: 100%;
	height: 0.6rem;
	left: 0.6rem;
	opacity: 0.3;
	z-index: 0;
}

.cu-bar .action.sub-title text[class*="text-"]:last-child {
	position: absolute;
	display: inline-block;
	bottom: -0.7rem;
	left: 0.5rem;
	opacity: 0.2;
	z-index: 0;
	text-align: right;
	font-weight: 900;
	font-size: 33px;
}

.cu-bar.justify-center .action.border-title text:last-child,
.cu-bar.justify-center .action.sub-title text:last-child {
	left: 0;
	right: 0;
	margin: auto;
	text-align: center;
}

.cu-bar .action:first-child {
	margin-left: 15px;
	font-size: 15px;
}

.cu-bar .action text.text-cut {
	text-align: left;
	width: 100%;
}

.cu-bar .cu-avatar:first-child {
	margin-left: 20px;
}

.cu-bar .action:first-child>text[class*="cuIcon-"] {
	margin-left: -0.3em;
	margin-right: 0.3em;
}

.cu-bar .action:last-child {
	margin-right: 15px;
}

.cu-bar .action>div[class*="cuIcon-"],
.cu-bar .action>div[class*="cuIcon-"] {
	font-size: 18px;
}

.cu-bar .action>div[class*="cuIcon-"]+div[class*="cuIcon-"] {
	margin-left: 0.5em;
}

.cu-bar .content {
	position: absolute;
	text-align: center;
	width: calc(100% - 170px);
	left: 0;
	right: 0;
	bottom: 0;
	top: 0;
	margin: auto;
	height: 30px;
	font-size: 16px;
	line-height: 30px;
	cursor: none;
	pointer-events: none;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
}

.cu-bar.ios .content {
	bottom: 7px;
	height: 30px;
	font-size: 16px;
	line-height: 30px;
}

.cu-bar.btn-group {
	justify-content: space-around;
}

.cu-bar.btn-group button {
	padding: 20px 16px;
}

.cu-bar.btn-group button {
	flex: 1;
	margin: 0 20px;
	max-width: 50%;
}

.cu-bar .search-form {
	background-color: #f5f5f5;
	line-height: 48px;
	height: 48px;
	font-size: 12px;
	color: #333333;
	flex: 1;
	display: flex;
	align-items: center;
	margin: 0 30px;
}

.cu-bar .search-form+.action {
	margin-right: 30px;
}

.cu-bar .search-form input {
	flex: 1;
	padding-right: 30px;
	height: 48px;
	line-height: 48px;
	font-size: 14px;
	background-color: transparent;
}

.cu-bar .search-form [class*="cuIcon-"] {
	margin: 0 0.5em 0 0.8em;
}

.cu-bar .search-form [class*="cuIcon-"]::before {
	top: 0px;
}

.cu-bar.fixed,
.nav.fixed {
	position: fixed;
	width: 100%;
	top: 0;
	z-index: 1024;

}

.cu-bar.foot {
	position: fixed;
	width: 100%;
	bottom: 0;
	z-index: 1024;
	box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
}

.cu-bar.tabbar {
	padding: 0;
	height: calc(100px + env(safe-area-inset-bottom) / 2);
	padding-bottom: calc(env(safe-area-inset-bottom) / 2);
}

.cu-tabbar-height {
	min-height: 100px;
	height: calc(100px + env(safe-area-inset-bottom) / 2);
}

.cu-bar.tabbar.shadow {
	box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
}

.cu-bar.tabbar .action {
	font-size: 11px;
	position: relative;
	flex: 1;
	text-align: center;
	padding: 0;
	display: block;
	height: auto;
	line-height: 1;
	margin: 0;
	background-color: inherit;
	overflow: initial;
}

.cu-bar.tabbar.shop .action {
	width: 140px;
	flex: initial;
}

.cu-bar.tabbar .action.add-action {
	position: relative;
	z-index: 2;
	padding-top: 50px;
}

.cu-bar.tabbar .action.add-action [class*="cuIcon-"] {
	position: absolute;
	width: 70px;
	z-index: 2;
	height: 70px;
	border-radius: 50%;
	line-height: 70px;
	font-size: 50px;
	top: -17px;
	left: 0;
	right: 0;
	margin: auto;
	padding: 0;
}

.cu-bar.tabbar .action.add-action::after {
	content: "";
	position: absolute;
	width: 100px;
	height: 100px;
	top: -50px;
	left: 0;
	right: 0;
	margin: auto;
	box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.08);
	border-radius: 50px;
	background-color: inherit;
	z-index: 0;
}

.cu-bar.tabbar .action.add-action::before {
	content: "";
	position: absolute;
	width: 100px;
	height: 30px;
	bottom: 30px;
	left: 0;
	right: 0;
	margin: auto;
	background-color: inherit;
	z-index: 1;
}

.cu-bar.tabbar .btn-group {
	flex: 1;
	display: flex;
	justify-content: space-around;
	align-items: center;
	padding: 0 10px;
}

.cu-bar.tabbar button.action::after {
	border: 0;
}

.cu-bar.tabbar .action [class*="cuIcon-"] {
	width: 100px;
	position: relative;
	display: block;
	height: auto;
	margin: 0 auto 10px;
	text-align: center;
	font-size: 40px;
}

.cu-bar.tabbar .action .cuIcon-cu-image {
	margin: 0 auto;
}

.cu-bar.tabbar .action .cuIcon-cu-image image {
	width: 50px;
	height: 50px;
	display: inline-block;
}

.cu-bar.tabbar .submit {
	align-items: center;
	display: flex;
	justify-content: center;
	text-align: center;
	position: relative;
	flex: 2;
	align-self: stretch;
}

.cu-bar.tabbar .submit:last-child {
	flex: 2.6;
}

.cu-bar.tabbar .submit+.submit {
	flex: 2;
}

.cu-bar.tabbar.border .action::before {
	content: " ";
	width: 200%;
	height: 200%;
	position: absolute;
	top: 0;
	left: 0;
	transform: scale(0.5);
	transform-origin: 0 0;
	border-right: 1px solid rgba(0, 0, 0, 0.1);
	z-index: 3;
}

.cu-bar.tabbar.border .action:last-child:before {
	display: none;
}

.cu-bar.input {
	padding-right: 20px;
	background-color: #ffffff;
}

.cu-bar.input input {
	overflow: initial;
	line-height: 48px;
	height: 48px;
	min-height: 48px;
	flex: 1;
	font-size: 30px;
	margin: 0 20px;
}

.cu-bar.input .action {
	margin-left: 20px;
}

.cu-bar.input .action [class*="cuIcon-"] {
	font-size: 44px;
}

.cu-bar.input input+.action {
	margin-right: 20px;
	margin-left: 0px;
}

.cu-bar.input .action:first-child [class*="cuIcon-"] {
	margin-left: 0px;
}

.cu-custom {
	display: block;
	position: relative;
}

.cu-custom .cu-bar .content {
	width: calc(100% - 440px);
}

/* #ifdef MP-ALIPAY */
.cu-custom .cu-bar .action .cuIcon-back {
	opacity: 0;
}

/* #endif */

.cu-custom .cu-bar .content image {
	height: 60px;
	width: 240px;
}

.cu-custom .cu-bar {
	min-height: 0px;
	/* #ifdef MP-WEIXIN */
	padding-right: 220px;
	/* #endif */
	/* #ifdef MP-ALIPAY */
	padding-right: 150px;
	/* #endif */
	box-shadow: 0px 0px 0px;
	z-index: 9999;
}

.cu-custom .cu-bar .border-custom {
	position: relative;
	background: rgba(0, 0, 0, 0.15);
	border-radius: 1000px;
	height: 30px;
}

.cu-custom .cu-bar .border-custom::after {
	content: " ";
	width: 200%;
	height: 200%;
	position: absolute;
	top: 0;
	left: 0;
	border-radius: inherit;
	transform: scale(0.5);
	transform-origin: 0 0;
	pointer-events: none;
	box-sizing: border-box;
	border: 1px solid #ffffff;
	opacity: 0.5;
}

.cu-custom .cu-bar .border-custom::before {
	content: " ";
	width: 1px;
	height: 110%;
	position: absolute;
	top: 22.5%;
	left: 0;
	right: 0;
	margin: auto;
	transform: scale(0.5);
	transform-origin: 0 0;
	pointer-events: none;
	box-sizing: border-box;
	opacity: 0.6;
	background-color: #ffffff;
}

.cu-custom .cu-bar .border-custom text {
	display: block;
	flex: 1;
	margin: auto !important;
	text-align: center;
	font-size: 17px;
}

/* ==================
         导航栏
 ==================== */

.nav {
	white-space: nowrap;
}

/*::-webkit-scrollbar {*/
/*	display: none;*/
/*}*/

.nav .cu-item {
	height: 45px;
	display: inline-block;
	line-height: 45px;
	margin: 0 5px;
	padding: 0 10px;
}

.nav .cu-item.cur {
	border-bottom: 2px solid;
}

/* ==================
         时间轴
 ==================== */

.cu-timeline {
	display: block;
	background-color: #ffffff;
}

.cu-timeline .cu-time {
	width: 120px;
	text-align: center;
	padding: 20px 0;
	font-size: 14px;
	color: #888;
	display: block;
}

.cu-timeline>.cu-item {
	padding: 30px 30px 30px 120px;
	position: relative;
	display: block;
	z-index: 0;
}

.cu-timeline>.cu-item:not([class*="text-"]) {
	color: #ccc;
}

.cu-timeline>.cu-item::after {
	content: "";
	display: block;
	position: absolute;
	width: 1px;
	background-color: #ddd;
	left: 60px;
	height: 100%;
	top: 0;
	z-index: 8;
}

.cu-timeline>.cu-item::before {
	font-family: "cuIcon";
	display: block;
	position: absolute;
	top: 33px;
	z-index: 9;
	background-color: #ffffff;
	width: 50px;
	height: 50px;
	text-align: center;
	border: none;
	line-height: 50px;
	left: 33px;
}

.cu-timeline>.cu-item:not([class*="cuIcon-"])::before {
	content: "\e763";
}

.cu-timeline>.cu-item[class*="cuIcon-"]::before {
	background-color: #ffffff;
	width: 50px;
	height: 50px;
	text-align: center;
	border: none;
	line-height: 50px;
	left: 33px;
}

.cu-timeline>.cu-item>.content {
	padding: 30px;
	border-radius: 4px;
	display: block;
	line-height: 1.6;
}

.cu-timeline>.cu-item>.content:not([class*="bg-"]) {
	background-color: #f1f1f1;
	color: #333333;
}

.cu-timeline>.cu-item>.content+.content {
	margin-top: 20px;
}

/* ==================
         聊天
 ==================== */

.cu-chat {
	display: flex;
	flex-direction: column;
}

.cu-chat .cu-item {
	display: flex;
	padding: 30px 30px 70px;
	position: relative;
}

.cu-chat .cu-item>.cu-avatar {
	width: 80px;
	height: 80px;
}

.cu-chat .cu-item>.main {
	max-width: calc(100% - 260px);
	margin: 0 40px;
	display: flex;
	align-items: center;
}

.cu-chat .cu-item>image {
	height: 320px;
}

.cu-chat .cu-item>.main .content {
	padding: 20px;
	border-radius: 4px;
	display: inline-flex;
	max-width: 100%;
	align-items: center;
	font-size: 30px;
	position: relative;
	min-height: 80px;
	line-height: 40px;
	text-align: left;
}

.cu-chat .cu-item>.main .content:not([class*="bg-"]) {
	background-color: #ffffff;
	color: #333333;
}

.cu-chat .cu-item .date {
	position: absolute;
	font-size: 12px;
	color: #8799a3;
	width: calc(100% - 320px);
	bottom: 20px;
	left: 160px;
}

.cu-chat .cu-item .action {
	padding: 0 30px;
	display: flex;
	align-items: center;
}

.cu-chat .cu-item>.main .content::after {
	content: "";
	top: 13px;
	transform: rotate(45deg);
	position: absolute;
	z-index: 100;
	display: inline-block;
	overflow: hidden;
	width:12px;
	height:12px;
	left:  -6px;
	right: initial;
	background-color: inherit;
}

.cu-chat .cu-item.self>.main .content::after {
	left: auto;
	right:  -6px;
}

.cu-chat .cu-item>.main .content::before {
	content: "";
	top: 30px;
	transform: rotate(45deg);
	position: absolute;
	z-index: -1;
	display: inline-block;
	overflow: hidden;
	width:12px;
	height:12px;
	left:  -6px;
	right: initial;
	background-color: inherit;
	filter: blur(5upx);
	opacity: 0.3;
}

.cu-chat .cu-item>.main .content:not([class*="bg-"])::before {
	background-color: #333333;
	opacity: 0.1;
}

.cu-chat .cu-item.self>.main .content::before {
	left: auto;
	right:  -6px;
}

.cu-chat .cu-item.self {
	justify-content: flex-end;
	text-align: right;
}

.cu-chat .cu-info {
	display: inline-block;
	margin: 20px auto;
	font-size: 12px;
	padding: 4px 6px;
	background-color: rgba(0, 0, 0, 0.2);
	border-radius: 4px;
	color: #ffffff;
	max-width: 400px;
	line-height: 1.4;
}

/* ==================
         卡片
 ==================== */

.cu-card {
	display: block;
	overflow: hidden;
}

.cu-card>.cu-item {
	display: block;
	background-color: #ffffff;
	overflow: hidden;
	border-radius: 5px;
	margin: 15px;
}

.cu-card>.cu-item.shadow-blur {
	overflow: initial;
}

.cu-card.no-card>.cu-item {
	margin: 0px;
	border-radius: 0px;
}

.cu-card .grid.grid-square {
	margin-bottom: -20px;
}

.cu-card.case .image {
	position: relative;
}

.cu-card.case .image image {
	width: 100%;
}

.cu-card.case .image .cu-tag {
	position: absolute;
	right: 0;
	top: 0;
}

.cu-card.case .image .cu-bar {
	position: absolute;
	bottom: 0;
	width: 100%;
	background-color: transparent;
	padding: 0px 15px;
}

.cu-card.case.no-card .image {
	margin: 30px 30px 0;
	overflow: hidden;
	border-radius: 10px;
}

.cu-card.dynamic {
	display: block;
}

.cu-card.dynamic>.cu-item {
	display: block;
	background-color: #ffffff;
	overflow: hidden;
}

.cu-card.dynamic>.cu-item>.text-content {
	padding: 0 30px 0;
	max-height: 6.4em;
	overflow: hidden;
	font-size: 30px;
	margin-bottom: 20px;
}

.cu-card.dynamic>.cu-item .square-img {
	width: 100%;
	height: 200px;
	border-radius: 4px;
}

.cu-card.dynamic>.cu-item .only-img {
	width: 100%;
	height: 320px;
	border-radius: 4px;
}

/* card.dynamic>.cu-item .comment {
  padding: 20px;
  background-color: #f1f1f1;
  margin: 0 30px 30px;
  border-radius: 4px;
} */

.cu-card.article {
	display: block;
}

.cu-card.article>.cu-item {
	padding-bottom: 30px;
}

.cu-card.article>.cu-item .title {
	font-size: 30px;
	font-weight: 900;
	color: #333333;
	line-height: 100px;
	padding: 0 30px;
}

.cu-card.article>.cu-item .content {
	display: flex;
	padding: 0 30px;
}

.cu-card.article>.cu-item .content>image {
	width: 240px;
	height: 6.4em;
	margin-right: 20px;
	border-radius: 4px;
}

.cu-card.article>.cu-item .content .desc {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.cu-card.article>.cu-item .content .text-content {
	font-size: 24px;
	color: #888;
	height: 4.8em;
	overflow: hidden;
}

/* ==================
         表单
 ==================== */

.cu-form-group {
	background-color: #ffffff;
	padding: 1px 30px;
	display: flex;
	align-items: center;
	min-height: 100px;
	justify-content: space-between;
}

.cu-form-group+.cu-form-group {
	border-top: 1px solid #eee;
}

.cu-form-group .title {
	text-align: justify;
	padding-right: 30px;
	font-size: 30px;
	position: relative;
	height: 60px;
	line-height: 60px;
}

.cu-form-group input {
	flex: 1;
	font-size: 30px;
	color: #555;
	padding-right: 20px;
}

.cu-form-group>text[class*="cuIcon-"] {
	font-size: 33px;
	padding: 0;
	box-sizing: border-box;
}

.cu-form-group textarea {
	margin: 16px 0 30px;
	height: 4.6em;
	width: 100%;
	line-height: 1.2em;
	flex: 1;
	font-size: 24px;
	padding: 0;
}

.cu-form-group.align-start .title {
	height: 1em;
	margin-top: 16px;
	line-height: 1em;
}

.cu-form-group picker {
	flex: 1;
	padding-right: 40px;
	overflow: hidden;
	position: relative;
}

.cu-form-group picker .picker {
	line-height: 100px;
	font-size: 24px;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
	width: 100%;
	text-align: right;
}

.cu-form-group picker::after {
	font-family: cuIcon;
	display: block;
	content: "\e6a3";
	position: absolute;
	font-size: 17px;
	color: #8799a3;
	line-height: 100px;
	width: 60px;
	text-align: center;
	top: 0;
	bottom: 0;
	right: -20px;
	margin: auto;
}

.cu-form-group textarea[disabled],
.cu-form-group textarea[disabled] .placeholder {
	color: transparent;
}

/* ==================
         模态窗口
 ==================== */

.cu-modal {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 1110;
	opacity: 0;
	outline: 0;
	text-align: center;
	-ms-transform: scale(1.185);
	transform: scale(1.185);
	backface-visibility: hidden;
	perspective: 2000px;
	background: rgba(0, 0, 0, 0.6);
	transition: all 0.3s ease-in-out 0s;
	pointer-events: none;
}

.cu-modal::before {
	content: "\200B";
	display: inline-block;
	height: 100%;
	vertical-align: middle;
}

.cu-modal.show {
	opacity: 1;
	transition-duration: 0.3s;
	-ms-transform: scale(1);
	transform: scale(1);
	overflow-x: hidden;
	overflow-y: auto;
	pointer-events: auto;
}

.cu-dialog {
	position: relative;
	display: inline-block;
	vertical-align: middle;
	margin-left: auto;
	margin-right: auto;
	width: 680px;
	max-width: 100%;
	background-color: #f8f8f8;
	border-radius: 10px;
	overflow: hidden;
}

.cu-modal.bottom-modal::before {
	vertical-align: bottom;
}

.cu-modal.bottom-modal .cu-dialog {
	width: 100%;
	border-radius: 0;
}

.cu-modal.bottom-modal {
	margin-bottom: -1000px;
}

.cu-modal.bottom-modal.show {
	margin-bottom: 0;
}

.cu-modal.drawer-modal {
	transform: scale(1);
	display: flex;
}

.cu-modal.drawer-modal .cu-dialog {
	height: 100%;
	min-width: 200px;
	border-radius: 0;
	margin: initial;
	transition-duration: 0.3s;
}

.cu-modal.drawer-modal.justify-start .cu-dialog {
	transform: translateX(-100%);
}

.cu-modal.drawer-modal.justify-end .cu-dialog {
	transform: translateX(100%);
}

.cu-modal.drawer-modal.show .cu-dialog {
	transform: translateX(0%);
}
.cu-modal .cu-dialog>.cu-bar:first-child .action{
  min-width:  50px;
  margin-right: 0;
  min-height:  50px;
}
/* ==================
         轮播
 ==================== */
swiper .a-swiper-dot {
	display: inline-block;
	width: 10px;
	height: 10px;
	background: rgba(0, 0, 0, .3);
	border-radius: 50%;
	vertical-align: middle;
}

swiper[class*="-dot"] .wx-swiper-dots,
swiper[class*="-dot"] .a-swiper-dots,
swiper[class*="-dot"] .uni-swiper-dots {
	display: flex;
	align-items: center;
	width: 100%;
	justify-content: center;
}

swiper.square-dot .wx-swiper-dot,
swiper.square-dot .a-swiper-dot,
swiper.square-dot .uni-swiper-dot {
	background-color: #ffffff;
	opacity: 0.4;
	width: 10px;
	height: 10px;
	border-radius: 20px;
	margin: 0 4px !important;
}

swiper.square-dot .wx-swiper-dot.wx-swiper-dot-active,
swiper.square-dot .a-swiper-dot.a-swiper-dot-active,
swiper.square-dot .uni-swiper-dot.uni-swiper-dot-active {
	opacity: 1;
	width: 30px;
}

swiper.round-dot .wx-swiper-dot,
swiper.round-dot .a-swiper-dot,
swiper.round-dot .uni-swiper-dot {
	width: 10px;
	height: 10px;
	position: relative;
	margin: 2px 4px !important;
}

swiper.round-dot .wx-swiper-dot.wx-swiper-dot-active::after,
swiper.round-dot .a-swiper-dot.a-swiper-dot-active::after,
swiper.round-dot .uni-swiper-dot.uni-swiper-dot-active::after {
	content: "";
	position: absolute;
	width: 10px;
	height: 10px;
	top: 0px;
	left: 0px;
	right: 0;
	bottom: 0;
	margin: auto;
	background-color: #ffffff;
	border-radius: 20px;
}

swiper.round-dot .wx-swiper-dot.wx-swiper-dot-active,
swiper.round-dot .a-swiper-dot.a-swiper-dot-active,
swiper.round-dot .uni-swiper-dot.uni-swiper-dot-active {
	width: 14px;
	height: 14px;
}

.screen-swiper {
	min-height:  190px;
}

.screen-swiper image,
.screen-swiper video,
.swiper-item image,
.swiper-item video {
	width: 100%;
	display: block;
	height: 100%;
	margin: 0;
	pointer-events: none;
}

.card-swiper {
	height: 420px !important;
}

.card-swiper swiper-item {
	width: 610px !important;
	left: 70px;
	box-sizing: border-box;
	padding: 40px 0px 70px;
	overflow: initial;
}

.card-swiper swiper-item .swiper-item {
	width: 100%;
	display: block;
	height: 100%;
	border-radius: 10px;
	transform: scale(0.9);
	transition: all 0.2s ease-in 0s;
	overflow: hidden;
}

.card-swiper swiper-item.cur .swiper-item {
	transform: none;
	transition: all 0.2s ease-in 0s;
}


.tower-swiper {
	height: 420px;
	position: relative;
	max-width: 750px;
	overflow: hidden;
}

.tower-swiper .tower-item {
	position: absolute;
	width: 300px;
	height: 380px;
	top: 0;
	bottom: 0;
	left: 50%;
	margin: auto;
	transition: all 0.2s ease-in 0s;
	opacity: 1;
}

.tower-swiper .tower-item.none {
	opacity: 0;
}

.tower-swiper .tower-item .swiper-item {
	width: 100%;
	height: 100%;
	border-radius: 4px;
	overflow: hidden;
}

/* ==================
          步骤条
 ==================== */

.cu-steps {
	display: flex;
}

scroll-div.cu-steps {
	display: block;
	white-space: nowrap;
}

scroll-div.cu-steps .cu-item {
	display: inline-block;
}

.cu-steps .cu-item {
	flex: 1;
	text-align: center;
	position: relative;
	min-width: 100px;
}

.cu-steps .cu-item:not([class*="text-"]) {
	color: #8799a3;
}

.cu-steps .cu-item [class*="cuIcon-"],
.cu-steps .cu-item .num {
	display: block;
	font-size: 40px;
	line-height: 80px;
}

.cu-steps .cu-item::before,
.cu-steps .cu-item::after,
.cu-steps.steps-arrow .cu-item::before,
.cu-steps.steps-arrow .cu-item::after {
	content: "";
	display: block;
	position: absolute;
	height: 0px;
	width: calc(100% - 80px);
	border-bottom: 1px solid #ccc;
	left: calc(0px - (100% - 80px) / 2);
	top: 40px;
	z-index: 0;
}

.cu-steps.steps-arrow .cu-item::before,
.cu-steps.steps-arrow .cu-item::after {
	content: "\e6a3";
	font-family: 'cuIcon';
	height: 30px;
	border-bottom-width: 0px;
	line-height: 30px;
	top: 0;
	bottom: 0;
	margin: auto;
	color: #ccc;
}

.cu-steps.steps-bottom .cu-item::before,
.cu-steps.steps-bottom .cu-item::after {
	bottom: 40px;
	top: initial;
}

.cu-steps .cu-item::after {
	border-bottom: 1px solid currentColor;
	width: 0px;
	transition: all 0.3s ease-in-out 0s;
}

.cu-steps .cu-item[class*="text-"]::after {
	width: calc(100% - 80px);
	color: currentColor;
}

.cu-steps .cu-item:first-child::before,
.cu-steps .cu-item:first-child::after {
	display: none;
}

.cu-steps .cu-item .num {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	line-height: 40px;
	margin: 20px auto;
	font-size: 12px;
	border: 1px solid currentColor;
	position: relative;
	overflow: hidden;
}

.cu-steps .cu-item[class*="text-"] .num {
	background-color: currentColor;
}

.cu-steps .cu-item .num::before,
.cu-steps .cu-item .num::after {
	content: attr(data-index);
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	margin: auto;
	transition: all 0.3s ease-in-out 0s;
	transform: translateY(0px);
}

.cu-steps .cu-item[class*="text-"] .num::before {
	transform: translateY(-40px);
	color: #ffffff;
}

.cu-steps .cu-item .num::after {
	transform: translateY(40px);
	color: #ffffff;
	transition: all 0.3s ease-in-out 0s;
}

.cu-steps .cu-item[class*="text-"] .num::after {
	content: "\e645";
	font-family: 'cuIcon';
	color: #ffffff;
	transform: translateY(0px);
}

.cu-steps .cu-item[class*="text-"] .num.err::after {
	content: "\e646";
}

/* ==================
          布局
 ==================== */

/*  -- flex弹性布局 -- */

.flex {
	display: flex;
}

.basis-xs {
	flex-basis: 20%;
}

.basis-sm {
	flex-basis: 40%;
}

.basis-df {
	flex-basis: 50%;
}

.basis-lg {
	flex-basis: 60%;
}

.basis-xl {
	flex-basis: 80%;
}

.flex-sub {
	flex: 1;
}

.flex-twice {
	flex: 2;
}

.flex-treble {
	flex: 3;
}

.flex-direction {
	flex-direction: column;
}

.flex-wrap {
	flex-wrap: wrap;
}

.align-start {
	align-items: flex-start;
}

.align-end {
	align-items: flex-end;
}

.align-center {
	align-items: center;
}

.align-stretch {
	align-items: stretch;
}

.self-start {
	align-self: flex-start;
}

.self-center {
	align-self: flex-center;
}

.self-end {
	align-self: flex-end;
}

.self-stretch {
	align-self: stretch;
}

.align-stretch {
	align-items: stretch;
}

.justify-start {
	justify-content: flex-start;
}

.justify-end {
	justify-content: flex-end;
}

.justify-center {
	justify-content: center;
}

.justify-between {
	justify-content: space-between;
}

.justify-around {
	justify-content: space-around;
}

/* grid布局 */

.grid {
	display: flex;
	flex-wrap: wrap;
}

.grid.grid-square {
	overflow: hidden;
}

.grid.grid-square .cu-tag {
	position: absolute;
	right: 0;
	top: 0;
	border-bottom-left-radius: 4px;
	padding: 3px 6px;
	height: auto;
	background-color: rgba(0, 0, 0, 0.5);
}

.grid.grid-square>div>text[class*="cuIcon-"] {
	font-size: 26px;
	position: absolute;
	color: #8799a3;
	margin: auto;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
}

.grid.grid-square>div {
	margin-right: 20px;
	margin-bottom: 20px;
	border-radius: 4px;
	position: relative;
	overflow: hidden;
}
.grid.grid-square>div.bg-img image {
	width: 100%;
	height: 100%;
	position: absolute;
}
.grid.col-1.grid-square>div {
	padding-bottom: 100%;
	height: 0;
	margin-right: 0;
}

.grid.col-2.grid-square>div {
	padding-bottom: calc((100% - 10px)/2);
	height: 0;
	width: calc((100% - 10px)/2);
}

.grid.col-3.grid-square>div {
	padding-bottom: calc((100% - 20px)/3);
	height: 0;
	width: calc((100% - 20px)/3);
}

.grid.col-4.grid-square>div {
	padding-bottom: calc((100% - 30px)/4);
	height: 0;
	width: calc((100% - 30px)/4);
}

.grid.col-5.grid-square>div {
	padding-bottom: calc((100% - 40px)/5);
	height: 0;
	width: calc((100% - 40px)/5);
}

.grid.col-2.grid-square>div:nth-child(2n),
.grid.col-3.grid-square>div:nth-child(3n),
.grid.col-4.grid-square>div:nth-child(4n),
.grid.col-5.grid-square>div:nth-child(5n) {
	margin-right: 0;
}

.grid.col-1>div {
	width: 100%;
}

.grid.col-2>div {
	width: 50%;
}

.grid.col-3>div {
	width: 33.33%;
}

.grid.col-4>div {
	width: 25%;
}

.grid.col-5>div {
	width: 20%;
}

/*  -- 内外边距 -- */

.margin-0 {
	margin: 0;
}

.margin-xs {
	margin: 10px;
}

.margin-sm {
	margin: 20px;
}

.margin {
	margin: 30px;
}

.margin-lg {
	margin: 40px;
}

.margin-xl {
	margin:25px;
}

.margin-top-xs {
	margin-top: 5px;
}

.margin-top-sm {
	margin-top: 10px;
}

.margin-top {
	margin-top: 15px;
}

.margin-top-lg {
	margin-top: 20px;
}

.margin-top-xl {
	margin-top: 25px;
}

.margin-right-xs {
	margin-right: 5px;
}

.margin-right-sm {
	margin-right: 10px;
}

.margin-right {
	margin-right: 15px;
}

.margin-right-lg {
	margin-right: 20px;
}

.margin-right-xl {
	margin-right: 25px;
}

.margin-bottom-xs {
	margin-bottom: 5px;
}

.margin-bottom-sm {
	margin-bottom: 10px;
}

.margin-bottom {
	margin-bottom: 15px;
}

.margin-bottom-lg {
	margin-bottom: 20px;
}

.margin-bottom-xl {
	margin-bottom: 25px;
}

.margin-left-xs {
	margin-left: 5px;
}

.margin-left-sm {
	margin-left: 10px;
}

.margin-left {
	margin-left: 15px;
}

.margin-left-lg {
	margin-left: 20px;
}

.margin-left-xl {
	margin-left: 25px;
}

.margin-lr-xs {
	margin-left: 5px;
	margin-right: 5px;
}

.margin-lr-sm {
	margin-left: 10px;
	margin-right: 10px;
}

.margin-lr {
	margin-left: 15px;
	margin-right: 15px;
}

.margin-lr-lg {
	margin-left: 20px;
	margin-right: 20px;
}

.margin-lr-xl {
	margin-left: 25px;
	margin-right: 25px;
}

.margin-tb-xs {
	margin-top: 5px;
	margin-bottom: 5px;
}

.margin-tb-sm {
	margin-top: 10px;
	margin-bottom: 10px;
}

.margin-tb {
	margin-top: 15px;
	margin-bottom: 15px;
}

.margin-tb-lg {
	margin-top: 20px;
	margin-bottom: 20px;
}

.margin-tb-xl {
	margin-top: 25px;
	margin-bottom: 25px;
}

.padding-0 {
	padding: 0;
}

.padding-xs {
	padding: 5px;
}

.padding-sm {
	padding: 10px;
}

.padding {
	padding: 15px;
}

.padding-lg {
	padding: 20px;
}

.padding-xl {
	padding: 25px;
}

.padding-top-xs {
	padding-top: 10px;
}

.padding-top-sm {
	padding-top: 20px;
}

.padding-top {
	padding-top: 30px;
}

.padding-top-lg {
	padding-top: 40px;
}

.padding-top-xl {
	padding-top: 50px;
}

.padding-right-xs {
	padding-right: 10px;
}

.padding-right-sm {
	padding-right: 20px;
}

.padding-right {
	padding-right: 30px;
}

.padding-right-lg {
	padding-right: 40px;
}

.padding-right-xl {
	padding-right: 50px;
}

.padding-bottom-xs {
	padding-bottom: 10px;
}

.padding-bottom-sm {
	padding-bottom: 20px;
}

.padding-bottom {
	padding-bottom: 30px;
}

.padding-bottom-lg {
	padding-bottom: 40px;
}

.padding-bottom-xl {
	padding-bottom: 50px;
}

.padding-left-xs {
	padding-left: 10px;
}

.padding-left-sm {
	padding-left: 20px;
}

.padding-left {
	padding-left: 30px;
}

.padding-left-lg {
	padding-left: 40px;
}

.padding-left-xl {
	padding-left: 50px;
}

.padding-lr-xs {
	padding-left: 10px;
	padding-right: 10px;
}

.padding-lr-sm {
	padding-left: 20px;
	padding-right: 20px;
}

.padding-lr {
	padding-left: 30px;
	padding-right: 30px;
}

.padding-lr-lg {
	padding-left: 40px;
	padding-right: 40px;
}

.padding-lr-xl {
	padding-left: 50px;
	padding-right: 50px;
}

.padding-tb-xs {
	padding-top: 10px;
	padding-bottom: 10px;
}

.padding-tb-sm {
	padding-top: 20px;
	padding-bottom: 20px;
}

.padding-tb {
	padding-top: 30px;
	padding-bottom: 30px;
}

.padding-tb-lg {
	padding-top: 40px;
	padding-bottom: 40px;
}

.padding-tb-xl {
	padding-top: 50px;
	padding-bottom: 50px;
}

/* -- 浮动 --  */

.cf::after,
.cf::before {
	content: " ";
	display: table;
}

.cf::after {
	clear: both;
}

.fl {
	float: left;
}

.fr {
	float: right;
}

/* ==================
          背景
 ==================== */

.line-red::after,
.lines-red::after {
	border-color: #e54d42;
}

.line-orange::after,
.lines-orange::after {
	border-color: #f37b1d;
}

.line-yellow::after,
.lines-yellow::after {
	border-color: #fbbd08;
}

.line-olive::after,
.lines-olive::after {
	border-color: #8dc63f;
}

.line-green::after,
.lines-green::after {
	border-color: #39b54a;
}

.line-cyan::after,
.lines-cyan::after {
	border-color: #1cbbb4;
}

.line-blue::after,
.lines-blue::after {
	border-color: #0081ff;
}

.line-purple::after,
.lines-purple::after {
	border-color: #6739b6;
}

.line-mauve::after,
.lines-mauve::after {
	border-color: #9c26b0;
}

.line-pink::after,
.lines-pink::after {
	border-color: #e03997;
}

.line-brown::after,
.lines-brown::after {
	border-color: #a5673f;
}

.line-grey::after,
.lines-grey::after {
	border-color: #8799a3;
}

.line-gray::after,
.lines-gray::after {
	border-color: #aaaaaa;
}

.line-black::after,
.lines-black::after {
	border-color: #333333;
}

.line-white::after,
.lines-white::after {
	border-color: #ffffff;
}

.bg-red {
	background-color: #e54d42;
	color: #ffffff;
}

.bg-orange {
	background-color: #f37b1d;
	color: #ffffff;
}

.bg-yellow {
	background-color: #fbbd08;
	color: #333333;
}

.bg-olive {
	background-color: #8dc63f;
	color: #ffffff;
}

.bg-green {
	background-color: #39b54a;
	color: #ffffff;
}

.bg-cyan {
	background-color: #1cbbb4;
	color: #ffffff;
}

.bg-blue {
	background-color: #0081ff;
	color: #ffffff;
}

.bg-purple {
	background-color: #6739b6;
	color: #ffffff;
}

.bg-mauve {
	background-color: #9c26b0;
	color: #ffffff;
}

.bg-pink {
	background-color: #e03997;
	color: #ffffff;
}

.bg-brown {
	background-color: #a5673f;
	color: #ffffff;
}

.bg-grey {
	background-color: #8799a3;
	color: #ffffff;
}

.bg-gray {
	background-color: #f0f0f0;
	color: #333333;
}

.bg-black {
	background-color: #333333;
	color: #ffffff;
}

.bg-white {
	background-color: #ffffff;
	color: #666666;
}

.bg-shadeTop {
	background-image: linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 0.01));
	color: #ffffff;
}

.bg-shadeBottom {
	background-image: linear-gradient(rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 1));
	color: #ffffff;
}

.bg-red.light {
	color: #e54d42;
	background-color: #fadbd9;
}

.bg-orange.light {
	color: #f37b1d;
	background-color: #fde6d2;
}

.bg-yellow.light {
	color: #fbbd08;
	background-color: #fef2ced2;
}

.bg-olive.light {
	color: #8dc63f;
	background-color: #e8f4d9;
}

.bg-green.light {
	color: #39b54a;
	background-color: #d7f0dbff;
}

.bg-cyan.light {
	color: #1cbbb4;
	background-color: #d2f1f0;
}

.bg-blue.light {
	color: #0081ff;
	background-color: #cce6ff;
}

.bg-purple.light {
	color: #6739b6;
	background-color: #e1d7f0;
}

.bg-mauve.light {
	color: #9c26b0;
	background-color: #ebd4ef;
}

.bg-pink.light {
	color: #e03997;
	background-color: #f9d7ea;
}

.bg-brown.light {
	color: #a5673f;
	background-color: #ede1d9;
}

.bg-grey.light {
	color: #8799a3;
	background-color: #e7ebed;
}

.bg-gradual-gray {
	background-image: linear-gradient(45deg, #99a6c3, #444c5e);
	color: #ffffff;
}

.bg-gradual-darkblue {
	background-image: linear-gradient(45deg, #339eec, #1b7bde);
	color: #ffffff;
}

.bg-gradual-red {
	background-image: linear-gradient(45deg, #f43f3b, #ec008c);
	color: #ffffff;
}

.bg-gradual-orange {
	background-image: linear-gradient(45deg, #ff9700, #ed1c24);
	color: #ffffff;
}

.bg-gradual-green {
	background-image: linear-gradient(45deg, #39b54a, #8dc63f);
	color: #ffffff;
}

.bg-gradual-purple {
	background-image: linear-gradient(45deg, #9000ff, #5e00ff);
	color: #ffffff;
}

.bg-gradual-pink {
	background-image: linear-gradient(45deg, #ec008c, #6739b6);
	color: #ffffff;
}

.bg-gradual-blue {
	background-image: linear-gradient(45deg, #0081ff, #1cbbb4);
	color: #ffffff;
}

.shadow[class*="-red"] {
	box-shadow: 3px 3px 4px rgba(204, 69, 59, 0.2);
}

.shadow[class*="-orange"] {
	box-shadow: 3px 3px 4px rgba(217, 109, 26, 0.2);
}

.shadow[class*="-yellow"] {
	box-shadow: 3px 3px 4px rgba(224, 170, 7, 0.2);
}

.shadow[class*="-olive"] {
	box-shadow: 3px 3px 4px rgba(124, 173, 55, 0.2);
}

.shadow[class*="-green"] {
	box-shadow: 3px 3px 4px rgba(48, 156, 63, 0.2);
}

.shadow[class*="-cyan"] {
	box-shadow: 3px 3px 4px rgba(28, 187, 180, 0.2);
}

.shadow[class*="-blue"] {
	box-shadow: 3px 3px 4px rgba(0, 102, 204, 0.2);
}

.shadow[class*="-purple"] {
	box-shadow: 3px 3px 4px rgba(88, 48, 156, 0.2);
}

.shadow[class*="-mauve"] {
	box-shadow: 3px 3px 4px rgba(133, 33, 150, 0.2);
}

.shadow[class*="-pink"] {
	box-shadow: 3px 3px 4px rgba(199, 50, 134, 0.2);
}

.shadow[class*="-brown"] {
	box-shadow: 3px 3px 4px rgba(140, 88, 53, 0.2);
}

.shadow[class*="-grey"] {
	box-shadow: 3px 3px 4px rgba(114, 130, 138, 0.2);
}

.shadow[class*="-gray"] {
	box-shadow: 3px 3px 4px rgba(114, 130, 138, 0.2);
}

.shadow[class*="-black"] {
	box-shadow: 3px 3px 4px rgba(26, 26, 26, 0.2);
}

.shadow[class*="-white"] {
	box-shadow: 3px 3px 4px rgba(26, 26, 26, 0.2);
}

.text-shadow[class*="-red"] {
	text-shadow: 3px 3px 4px rgba(204, 69, 59, 0.2);
}

.text-shadow[class*="-orange"] {
	text-shadow: 3px 3px 4px rgba(217, 109, 26, 0.2);
}

.text-shadow[class*="-yellow"] {
	text-shadow: 3px 3px 4px rgba(224, 170, 7, 0.2);
}

.text-shadow[class*="-olive"] {
	text-shadow: 3px 3px 4px rgba(124, 173, 55, 0.2);
}

.text-shadow[class*="-green"] {
	text-shadow: 3px 3px 4px rgba(48, 156, 63, 0.2);
}

.text-shadow[class*="-cyan"] {
	text-shadow: 3px 3px 4px rgba(28, 187, 180, 0.2);
}

.text-shadow[class*="-blue"] {
	text-shadow: 3px 3px 4px rgba(0, 102, 204, 0.2);
}

.text-shadow[class*="-purple"] {
	text-shadow: 3px 3px 4px rgba(88, 48, 156, 0.2);
}

.text-shadow[class*="-mauve"] {
	text-shadow: 3px 3px 4px rgba(133, 33, 150, 0.2);
}

.text-shadow[class*="-pink"] {
	text-shadow: 3px 3px 4px rgba(199, 50, 134, 0.2);
}

.text-shadow[class*="-brown"] {
	text-shadow: 3px 3px 4px rgba(140, 88, 53, 0.2);
}

.text-shadow[class*="-grey"] {
	text-shadow: 3px 3px 4px rgba(114, 130, 138, 0.2);
}

.text-shadow[class*="-gray"] {
	text-shadow: 3px 3px 4px rgba(114, 130, 138, 0.2);
}

.text-shadow[class*="-black"] {
	text-shadow: 3px 3px 4px rgba(26, 26, 26, 0.2);
}

.bg-img {
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
}

.bg-mask {
	background-color: #333333;
	position: relative;
}

.bg-mask::after {
	content: "";
	border-radius: inherit;
	width: 100%;
	height: 100%;
	display: block;
	background-color: rgba(0, 0, 0, 0.4);
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
	top: 0;
}

.bg-mask div,
.bg-mask cover-div {
	z-index: 5;
	position: relative;
}

.bg-video {
	position: relative;
}

.bg-video video {
	display: block;
	height: 100%;
	width: 100%;
	-o-object-fit: cover;
	object-fit: cover;
	position: absolute;
	top: 0;
	z-index: 0;
	pointer-events: none;
}

/* ==================
          文本
 ==================== */

.text-xs {
	font-size: 10px;
}

.text-sm {
	font-size: 12px;
}

.text-df {
	font-size: 14px;
}

.text-lg {
	font-size: 16px;
}

.text-xl {
	font-size: 18px;
}

.text-xxl {
	font-size: 22px;
}

.text-sl {
	font-size: 40px;
}

.text-xsl {
	font-size: 60px;
}

.text-Abc {
	text-transform: Capitalize;
}

.text-ABC {
	text-transform: Uppercase;
}

.text-abc {
	text-transform: Lowercase;
}

.text-price::before {
	content: "¥";
	font-size: 80%;
	margin-right: 2px;
}

.text-cut {
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
}

.text-bold {
	font-weight: bold;
}

.text-center {
	text-align: center;
}

.text-content {
	line-height: 1.6;
}

.text-left {
	text-align: left;
}

.text-right {
	text-align: right;
}

.text-purple-grey,
.line-purple-grey,
.lines-purple-grey {
	color: #b2b7cd;
}

.text-darkgrey,
.line-darkgrey,
.lines-darkgrey {
	color: #43496a;
}

.text-red,
.line-red,
.lines-red {
	color: #e54d42;
}

.text-orange,
.line-orange,
.lines-orange {
	color: #f37b1d;
}

.text-yellow,
.line-yellow,
.lines-yellow {
	color: #fbbd08;
}

.text-olive,
.line-olive,
.lines-olive {
	color: #8dc63f;
}

.text-green,
.line-green,
.lines-green {
	color: #39b54a;
}

.text-cyan,
.line-cyan,
.lines-cyan {
	color: #1cbbb4;
}

.text-blue,
.line-blue,
.lines-blue {
	color: #0081ff;
}

.text-purple,
.line-purple,
.lines-purple {
	color: #6739b6;
}

.text-mauve,
.line-mauve,
.lines-mauve {
	color: #9c26b0;
}

.text-pink,
.line-pink,
.lines-pink {
	color: #e03997;
}

.text-brown,
.line-brown,
.lines-brown {
	color: #a5673f;
}

.text-grey,
.line-grey,
.lines-grey {
	color: #8799a3;
}

.text-gray,
.line-gray,
.lines-gray {
	color: #aaaaaa;
}

.text-black,
.line-black,
.lines-black {
	color: #333333;
}

.text-white,
.line-white,
.lines-white {
	color: #ffffff;
}
/*JOOLUN新加的css */

.nav_bt_img{
  width: 50px !important;
  height: 50px !important;
  margin: 10px 0;
}
.draggable-focus{
  font-size: 30px;
}
.draggable-focus:hover{
  cursor: move;
}
.del-focus{
  font-size: 20px;
  color: red;
  margin-top: 10px
}
.del-focus:hover{
  cursor: pointer;
}

/* 店铺 */
.store-swiper {
  margin-top: -15px;
  height: 183px;
  padding-bottom: 32px;
}

.shop-selection{
  margin-left: 0px !important;
  color: #666666;
}

.shop-more{
  margin-right: 0px !important;
  color: #666666;
}

.shop-detail{
  margin-top: -15px !important;
}

.shop-image{
  width: 100px;
  height: 100px !important;
}

.shop-image image{
  height: 100px !important;
}

.shop-box{
  height: 100px !important;
  width: 100px !important;
  margin-right: 0px !important;
}

.shop-information{
  position: absolute;
  top: 70px;
  left: 25px;
}

.enter-bg{
  width: 50px;
  height: 20px;
  opacity: 0.3;
}

.shop-name{
  position: absolute;
  top: 0;
  line-height: 100px;
  padding: 0 5px 0 2px;
  width: 100px;
}

.enter-shop{
  position: absolute;
  left:10px;
}

.wrapper-list {
  white-space: nowrap;
  padding: 10px 15px;
}

.wrapper-list .item {
  display: inline-block;
  width: 190px;
  height: 260px;
  padding: 0px 0;
  margin-bottom: 10px;
  border: 1px solid #eee;
  background-color: #fff;
}

.bg-red {
  background-color: #e54d42;
  color: #ffffff;
}

.bg-orange {
  background-color: #f37b1d;
  color: #ffffff;
}

.bg-yellow {
  background-color: #fbbd08;
  color: #333333;
}

.bg-olive {
  background-color: #8dc63f;
  color: #ffffff;
}

.bg-green {
  background-color: #39b54a;
  color: #ffffff;
}

.bg-cyan {
  background-color: #1cbbb4;
  color: #ffffff;
}

.bg-darkblue {
  background-color: #0055ff;
  color: #ffffff;
}

.bg-blue {
  background-color: #0081ff;
  color: #ffffff;
}

.bg-purple {
  background-color: #6739b6;
  color: #ffffff;
}

.bg-mauve {
  background-color: #9c26b0;
  color: #ffffff;
}

.bg-pink {
  background-color: #e03997;
  color: #ffffff;
}

.bg-brown {
  background-color: #a5673f;
  color: #ffffff;
}

.bg-grey {
  background-color: #8799a3;
  color: #ffffff;
}

.bg-gray {
  background-color: #f0f0f0;
  color: #333333;
}

.bg-black {
  background-color: #333333;
  color: #ffffff;
}

.bg-white {
  background-color: #ffffff;
  color: #666666;
}

.bg-shadeTop {
  background-image: linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 0.01));
  color: #ffffff;
}

.bg-shadeBottom {
  background-image: linear-gradient(rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 1));
  color: #ffffff;
}

.bg-red.light {
  color: #e54d42;
  background-color: #fadbd9;
}

.bg-orange.light {
  color: #f37b1d;
  background-color: #fde6d2;
}

.bg-yellow.light {
  color: #fbbd08;
  background-color: #fef2ced2;
}

.bg-olive.light {
  color: #8dc63f;
  background-color: #e8f4d9;
}

.bg-green.light {
  color: #39b54a;
  background-color: #d7f0dbff;
}

.bg-cyan.light {
  color: #1cbbb4;
  background-color: #d2f1f0;
}

.bg-blue.light {
  color: #0081ff;
  background-color: #cce6ff;
}

.bg-purple.light {
  color: #6739b6;
  background-color: #e1d7f0;
}

.bg-mauve.light {
  color: #9c26b0;
  background-color: #ebd4ef;
}

.bg-pink.light {
  color: #e03997;
  background-color: #f9d7ea;
}

.bg-brown.light {
  color: #a5673f;
  background-color: #ede1d9;
}

.bg-grey.light {
  color: #8799a3;
  background-color: #e7ebed;
}
.bg-scarlet {
  background-color: #e53c43;
  color: #ffffff;
}
.bg-gradual-scarlet {
  background-image: linear-gradient(45deg, #e5432e, #e53c43);
  color: #ffffff;
}
.bg-gradual-red {
  background-image: linear-gradient(45deg, #f43f3b, #ec008c);
  color: #ffffff;
}

.bg-gradual-orange {
  background-image: linear-gradient(45deg, #ff9700, #ed1c24);
  color: #ffffff;
}

.bg-gradual-green {
  background-image: linear-gradient(45deg, #39b54a, #8dc63f);
  color: #ffffff;
}

.bg-gradual-purple {
  background-image: linear-gradient(45deg, #9000ff, #5e00ff);
  color: #ffffff;
}

.bg-gradual-pink {
  background-image: linear-gradient(45deg, #ec008c, #6739b6);
  color: #ffffff;
}

.bg-gradual-blue {
  background-image: linear-gradient(45deg, #0081ff, #1cbbb4);
  color: #ffffff;
}


.bg-gradual-gray {
  background-image: linear-gradient(45deg, #99a6c3, #444c5e);
  color: #ffffff;
}

.bg-gradual-darkblue {
  background-image: linear-gradient(45deg, #339eec, #1b7bde);
  color: #ffffff;
}
/*建议提示的class*/
.tips-class{
  color: #7f7f7f;
  padding: 20px 0 10px 20px;
}
.tm-select-bg {
  text-align: center;
  cursor: pointer;
  padding: 10px 0;
}

.tm-select-bg:hover {
  background: #efefef;
}
