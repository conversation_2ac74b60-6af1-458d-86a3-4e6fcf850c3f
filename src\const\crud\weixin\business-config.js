/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.gocreateone.com
 * 注意：
 * 本软件为www.gocreateone.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  viewBtn: true,
  searchMenuSpan: 6,
  labelWidth: 200,
  column: [
    {
      label: '超时档期退款处理设置',
      prop: 'timeoutRefundHandle',
      type: 'radio',
      dicData: [
        {
          label: '联系客服',
          value: 'contact_service'
        },
        {
          label: '强制删除',
          value: 'force_delete'
        }
      ],
      rules: [{
        required: true,
        message: '请选择超时档期退款处理方式',
        trigger: 'change'
      }]
    }
  ]
}
