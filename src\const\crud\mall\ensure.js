export const tableOption = {
    dialogDrag: true,
    border: true,
    indexLabel: '序号',
    stripe: true,
    menuAlign: 'center',
    align: 'center',
    menuType: 'text',
    searchShow: false,
    excelBtn: true,
    printBtn: true,
    viewBtn: true,
    searchMenuSpan: 6,
    column: [
        {
            label: '保障名',
            prop: 'name',
            rules: [{
                required: true,
                message: '请输入保障名',
                trigger: 'blur'
            },{
              max: 50,
              message: '长度在不能超过50个字符'
            }],
        },
        {
            label: '保障详情',
            prop: 'detail',
            type: 'textarea',
            rules: [{
              max: 200,
              message: '长度在不能超过200个字符'
            }],
        },
    ]
}
