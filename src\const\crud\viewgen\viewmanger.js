export const tableOption = {
  dialogDrag: true,
  border: true,
  menuAlign: 'center',
  align: 'center',
  stripe: true,
  index: true,
  menuWidth: 300,
  menuType: 'text',
  searchShow: false,
  // excelBtn: true,//过滤按钮
  addBtn: false,
  printBtn: true,
  viewBtn: false,
  editBtn: false,
  delBtn: false,
  searchMenuSpan: 6,
  menu: true,
  column: [
    {
      prop: 'id',
      label: 'id',
      addDisplay: false
    },
    {
      label: '页面名字',
      prop: 'pageName',
    },
    {
      label: '公众号',
      prop: 'appId',
      filterMethod:function(value, row, column) {
        return row.appId === value;
      },
      dicData:[],
    },
    {
      label: '创建时间',
      prop: 'createTime',
      sortable: true,
      addDisplay: false
    },
    {
      label: '最后更新时间',
      prop: 'updateTime',
      sortable: true,
      addDisplay: false
    },
    {
      label: '页面类型',
      prop: 'pageType',
      dicData:[{ label: '首页', value: '1' },
        { label: '拼团活动', value: '2' },
        { label: '作品详情', value: '3' },
        { label: '图文表单', value: '4' },
        { label: '客照分享', value: '5' },
        { label: '档期选择', value: '6' },
      ],
      // filters:true,//过滤
      filterMethod:function(value, row, column) {
        return row.pageType === value;
      }
    },
    {
      label: '是否启用',
      prop: 'enable',
      addDisplay: false,

    },
    {
      label: '应用类型',
      prop: 'clientType',
      addDisplay: false
    },
  ]
}
