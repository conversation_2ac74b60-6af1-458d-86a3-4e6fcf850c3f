<!-- 商品分类组件 -->
<template>
  <div class="categoryComponent">
    <!-- 横向模式 -->
    <div v-if="!setData.navStyle || setData.navStyle == 0" >
      <!--    导航显示-->
      <div class="cu-bar" :style="{ backgroundColor: setData.background }" style="min-height: 30px;"
           :class="setData.background && setData.background.indexOf('bg-') != -1 ? setData.background : '' ">
        <div v-if="setData.showType==0" style="width: 90%;">
          <div class="nav text-white text-sm" :style="{fontSize:`${setData.fontSize}px`,color:setData.fontColor}"
               style="overflow-x:scroll;position: relative;display:flex;">
            <div class="cu-item"
                 :style="[
                   index==TabCur ? {fontSize:`${setData.selectedFontSize}px`,color:setData.selectedFontColor} : '',
                   (index==TabCur && item.selectedImgUrl) ? {
                     backgroundImage: `url(${item.selectedImgUrl})`,
                     backgroundSize: item.backgroundSize || 'cover',
                     backgroundPosition: item.backgroundPosition || 'center',
                     backgroundRepeat: item.backgroundRepeat || 'no-repeat'
                   } : (item.imgUrl ? {
                     backgroundImage: `url(${item.imgUrl})`,
                     backgroundSize: item.backgroundSize || 'cover',
                     backgroundPosition: item.backgroundPosition || 'center',
                     backgroundRepeat: item.backgroundRepeat || 'no-repeat'
                   } : {})
                 ]"
                 :class="index==TabCur ? 'cur  text-bold  text-white text-lg' : ''"
                 v-for="(item,index) in setData.initCategoryList" :key="index"
                 @click="tabSelect(index)"
                 :data-index="index">
              {{ item.name }}
            </div>
          </div>
        </div>
        <div v-if="setData.showType==1" style="width: 90%;">
          <div class="nav text-white text-sm" :style="{fontSize:`${setData.fontSize}px`,color:setData.fontColor}"
               style="overflow-x:scroll;position: relative;display:flex;">
            <div class="cu-item" 
                 :class="index==TabCur ? 'cur text-bold text-white text-lg' : ''"
                 :style="[
                   index==TabCur ? {fontSize:`${setData.selectedFontSize}px`,color:setData.selectedFontColor} : '',
                   (index==TabCur && item.selectedImgUrl) ? {
                     backgroundImage: `url(${item.selectedImgUrl})`,
                     backgroundSize: item.backgroundSize || 'cover',
                     backgroundPosition: item.backgroundPosition || 'center',
                     backgroundRepeat: item.backgroundRepeat || 'no-repeat'
                   } : (item.imgUrl ? {
                     backgroundImage: `url(${item.imgUrl})`,
                     backgroundSize: item.backgroundSize || 'cover',
                     backgroundPosition: item.backgroundPosition || 'center',
                     backgroundRepeat: item.backgroundRepeat || 'no-repeat'
                   } : {})
                 ]"
                 v-for="(item,index) in setData.categoryList" :key="index"
                 @click="tabSelect(index)"
                 :data-index="index">
              {{ item.name }}
            </div>
          </div>
        </div>
        <div class="action">
          <div :class="setData.titleIcon" :style="{fontSize:`${setData.fontSize}px`,color:setData.fontColor}"></div>
        </div>
      </div>

      <!--    显示图片-->
      <div>
        <div v-if="setData.rowRules==0" >
          <div
            :style="{ paddingTop: `${setData.topSpacing}px`,paddingBottom: `${setData.bottomSpacing}px`, paddingLeft: `${setData.leftSpacing}px`, paddingRight: `${setData.rightSpacing}px`, }">
            <el-image class="img" :fit="'fill'"
                      :src="'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg'"
                      style="width: 100%"></el-image>
            <div v-show="setData.nameFlag" class="text-center text-cut">作品名称</div>
          </div>
        </div>
        <div v-if="setData.rowRules==1">
          <div class="flex" :style="{ paddingTop: `${setData.topSpacing}px`,paddingBottom: `${setData.bottomSpacing}px`, paddingLeft: `${setData.leftSpacing}px`, paddingRight: `${setData.rightSpacing}px`, }">
            <div class="flex-sub" :style="{ paddingRight: `${setData.interval}px`}" >
              <img src="https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg" style="width: 100%">
              <div v-show="setData.nameFlag"  class="text-center text-cut">作品名称</div>
            </div>
            <div class="flex-sub" :style="{ paddingLeft: `${setData.interval}px`}"  >
              <img src="https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg" style="width: 100%">
              <div v-show="setData.nameFlag"  class="text-center text-cut">作品名称</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 竖向模式 -->
    <div v-else-if="setData.navStyle == 1" class="category-vertical-layout"
         :style="{
           backgroundColor: setData.background,
           height: setData.containerHeight ? `${setData.containerHeight}px` : '300px',
           overflow: 'auto'
         }">
      <!-- 竖向导航 -->
      <div class="vertical-nav" :style="setData.containerHeight ? {height: '100%'} : {}">
        <div v-if="setData.showType==0" class="vertical-menu">
          <div class="vertical-item"
               :style="[
                 index==TabCur ? 
                   {fontSize:`${setData.selectedFontSize}px`,color:setData.selectedFontColor, backgroundColor: '#f2f2f2'} : 
                   {fontSize:`${setData.fontSize}px`,color:setData.fontColor},
                 (index==TabCur && item.selectedImgUrl) ? {
                   backgroundImage: `url(${item.selectedImgUrl})`,
                   backgroundSize: item.backgroundSize || 'cover',
                   backgroundPosition: item.backgroundPosition || 'center',
                   backgroundRepeat: item.backgroundRepeat || 'no-repeat'
                 } : (item.imgUrl ? {
                   backgroundImage: `url(${item.imgUrl})`,
                   backgroundSize: item.backgroundSize || 'cover',
                   backgroundPosition: item.backgroundPosition || 'center',
                   backgroundRepeat: item.backgroundRepeat || 'no-repeat'
                 } : {})
               ]"
               :class="index==TabCur ? 'vertical-active' : ''"
               v-for="(item,index) in setData.initCategoryList" :key="index"
               @click="tabSelect(index)"
               :data-index="index">
            {{ item.name }}
          </div>
          <div v-if="!setData.initCategoryList || setData.initCategoryList.length === 0" class="vertical-item" style="color: #999;">
            暂无分类
          </div>
        </div>
        <div v-if="setData.showType==1" class="vertical-menu">
          <div class="vertical-item"
               :style="[
                 index==TabCur ? 
                   {fontSize:`${setData.selectedFontSize}px`,color:setData.selectedFontColor, backgroundColor: '#f2f2f2'} : 
                   {fontSize:`${setData.fontSize}px`,color:setData.fontColor},
                 (index==TabCur && item.selectedImgUrl) ? {
                   backgroundImage: `url(${item.selectedImgUrl})`,
                   backgroundSize: item.backgroundSize || 'cover',
                   backgroundPosition: item.backgroundPosition || 'center',
                   backgroundRepeat: item.backgroundRepeat || 'no-repeat'
                 } : (item.imgUrl ? {
                   backgroundImage: `url(${item.imgUrl})`,
                   backgroundSize: item.backgroundSize || 'cover',
                   backgroundPosition: item.backgroundPosition || 'center',
                   backgroundRepeat: item.backgroundRepeat || 'no-repeat'
                 } : {})
               ]"
               :class="index==TabCur ? 'vertical-active' : ''"
               v-for="(item,index) in setData.categoryList" :key="index"
               @click="tabSelect(index)"
               :data-index="index">
            {{ item.name }}
          </div>
          <div v-if="!setData.categoryList || setData.categoryList.length === 0" class="vertical-item" style="color: #999;">
            暂无分类
          </div>
        </div>
      </div>

      <!-- 竖向内容 -->
      <div class="vertical-content" :style="{
          height: setData.containerHeight ? '100%' : 'auto',
          paddingTop: `${setData.topSpacing}px`,
          paddingBottom: `${setData.bottomSpacing}px`,
          paddingLeft: `${setData.leftSpacing}px`,
          paddingRight: `${setData.rightSpacing}px`
        }">

        <div v-if="setData.rowRules==0" >
          <div
            :style="{ paddingTop: `${setData.topSpacing}px`,paddingBottom: `${setData.bottomSpacing}px`, paddingLeft: `${setData.leftSpacing}px`, paddingRight: `${setData.rightSpacing}px`, }">
            <el-image class="img" :fit="'fill'"
                      :src="'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg'"
                      style="width: 100%"></el-image>
            <div v-show="setData.nameFlag" class="text-center text-cut">作品名称</div>
          </div>
        </div>
        <div v-if="setData.rowRules==1">
          <div class="flex" :style="{ paddingTop: `${setData.topSpacing}px`,paddingBottom: `${setData.bottomSpacing}px`, paddingLeft: `${setData.leftSpacing}px`, paddingRight: `${setData.rightSpacing}px`, }">
            <div class="flex-sub" :style="{ paddingRight: `${setData.interval}px`}" >
              <img src="https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg" style="width: 100%">
              <div v-show="setData.nameFlag"  class="text-center text-cut">作品名称</div>
            </div>
            <div class="flex-sub" :style="{ paddingLeft: `${setData.interval}px`}"  >
              <img src="https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg" style="width: 100%">
              <div v-show="setData.nameFlag"  class="text-center text-cut">作品名称</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {getList} from '@/api/viewgen/wxgoodstagtype'
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';

export default {
  data() {
    return {
      TabCur: 0,
    };
  },
  components: {},
  props: {
    thememobile: {type: Object | Array},
    setData: {type: Object | Array},
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {
    
    // 确保setData中有navStyle属性，兼容老数据
    if(this.setData && this.setData.navStyle === undefined) {
      this.$set(this.setData, 'navStyle', 0); // 默认使用横向导航
    }
    // 确保setData中有containerHeight属性
    if(this.setData && this.setData.containerHeight === undefined) {
      this.$set(this.setData, 'containerHeight', 300); // 默认容器高度
    }
    
    // 检查分类列表数据
    this.validateCategoryData();
  },
  mounted() {
    
    // 再次检查数据，确保视图渲染时数据完整
    this.validateCategoryData();
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    // 切换分类选项卡
    tabSelect(index) {
      this.TabCur = index;
    },
    // 验证分类数据完整性
    validateCategoryData() {
      if(!this.setData) return;
      
      // 检查并记录分类列表数据
      if(this.setData.showType === 0 && this.setData.initCategoryList) {
        this.setData.initCategoryList.forEach((item, index) => {
          if(!item.backgroundSize) this.$set(item, 'backgroundSize', 'cover');
          if(!item.backgroundPosition) this.$set(item, 'backgroundPosition', 'center');
          if(!item.backgroundRepeat) this.$set(item, 'backgroundRepeat', 'no-repeat');
          // 保护imgUrl，确保它不会被清空
          if(item.imgUrl === undefined) this.$set(item, 'imgUrl', '');
          // 保护selectedImgUrl，确保它不会被清空
          if(item.selectedImgUrl === undefined) this.$set(item, 'selectedImgUrl', '');
        });
      }
      
      if(this.setData.showType === 1 && this.setData.categoryList) {
        this.setData.categoryList.forEach((item, index) => {
          if(!item.backgroundSize) this.$set(item, 'backgroundSize', 'cover');
          if(!item.backgroundPosition) this.$set(item, 'backgroundPosition', 'center');
          if(!item.backgroundRepeat) this.$set(item, 'backgroundRepeat', 'no-repeat');
          // 保护imgUrl，确保它不会被清空
          if(item.imgUrl === undefined) this.$set(item, 'imgUrl', '');
          // 保护selectedImgUrl，确保它不会被清空
          if(item.selectedImgUrl === undefined) this.$set(item, 'selectedImgUrl', '');
        });
      }
    }
  },
  watch: {
    componentsList(newVal, oldVal) {          //添加的时候触发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    },
    setData: {
      handler(newVal) {
        this.validateCategoryData();
      },
      deep: true
    }
  }
};
</script>
<style lang='less' scoped>

@import '../../colorui/main.css';
@import '../../colorui/icon.css';

.categoryComponent {
  .img-category {
    width: 50px;
    height: 50px;
  }

  .more {
    margin-top: 30px;
  }

  .img-category-banner {
    width: 100%;
    height: 90px;
  }
  
  /* 导航项样式增强 */
  .cu-item {
    padding: 0 12px;
    min-width: 60px;
    text-align: center;
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
  }

  /* 竖向布局样式 */
  .category-vertical-layout {
    display: flex;
    width: 100%;
    min-height: 200px; /* 最小高度，会被containerHeight覆盖 */
    padding: 5px;
    box-sizing: border-box;
  }

  /* 竖向导航样式 */
  .vertical-nav {
    width: 30%;
    max-width: 120px;
    min-width: 80px; /* 确保在小屏幕上不会太窄 */
    overflow-y: auto;
    height: auto;
    margin-right: 10px;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
  }

  .vertical-menu {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .vertical-item {
    padding: 10px 5px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    cursor: pointer;
    transition: all 0.3s;
    word-break: break-all; /* 确保文字不会溢出 */
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .vertical-active {
    border-left: 3px solid #0081ff;
    font-weight: bold;
  }

  /* 竖向内容区域样式 */
  .vertical-content {
    flex: 1;
    padding: 0 5px;
    overflow-y: auto;
  }

  .action-title {
    padding: 8px 0;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 10px;
  }
}
</style>
