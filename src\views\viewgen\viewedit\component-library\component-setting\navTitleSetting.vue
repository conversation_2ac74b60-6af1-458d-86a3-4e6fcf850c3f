<template>
  <div>
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">头部导航栏</p>
      <div slot="hint"></div>
      <div slot="mainContent">
        <el-form ref="form"  label-width="100px" :model="formData">
          <el-divider>基础属性</el-divider>
          <el-form-item label="固定悬浮">
            <el-switch
              v-model="formData.fixed"
              :active-value="true"
              :inactive-value="false"
              active-color="#13ce66"
              inactive-color="#ff4949">
            </el-switch>
          </el-form-item>
          <el-form-item label="导航文字">
            <el-input v-model="formData.title" size="mini" style="margin-top: 5px" placeholder="标题文字">
            </el-input>
          </el-form-item>
          <el-form-item label="文字大小">
            <el-input v-model="formData.fontSize" size="mini"  :min="12"  :max="35" type="number" style="margin-top: 5px" placeholder="文字大小">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="文字颜色">
            <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
              <el-input v-model="formData.color" size="small" style="margin-top: 5px">
                <template slot="append">
                  <el-color-picker size="mini" v-model="formData.color"></el-color-picker>
                </template>
              </el-input>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="背景颜色">
            <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
              <el-input v-model="formData.backgroundColor" size="small" style="margin-top: 5px">
                <template slot="append">
                  <el-color-picker size="mini" v-model="formData.backgroundColor"></el-color-picker>
                </template>
              </el-input>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="右侧图标选择">
            <icon-select style="float:left;margin-right: 10px;"  @onChangeIcon="formData.titleIcon = $event"></icon-select>
            <div :class="formData.titleIcon"></div>
          </el-form-item>
          <el-divider>菜单导航</el-divider>
          <el-form-item label="滑出方向">
            <el-radio-group v-model="formData.barMode">
              <el-radio :label="'left'">左边</el-radio>
              <el-radio :label="'right'">右边</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="滑出宽度">
            <el-input v-model="formData.barWidth" type="number" size="mini"  :min="100"  :max="800"  style="margin-top: 5px" placeholder="弹出宽度">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="顶部高度">
            <el-input v-model="formData.topHeight" type="number" size="mini"  :min="0"  :max="800"  style="margin-top: 5px" placeholder="顶部高度">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="背景颜色">
            <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
              <el-input v-model="formData.barGroundColor" size="small" style="margin-top: 5px">
                <template slot="append">
                  <el-color-picker size="mini" v-model="formData.barGroundColor"></el-color-picker>
                </template>
              </el-input>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="文字大小">
            <el-input v-model="formData.barFontSize" size="mini"  :min="12"  :max="35" type="number" style="margin-top: 5px" placeholder="文字大小">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="文字颜色">
            <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
              <el-input v-model="formData.barFontColor" size="small" style="margin-top: 5px">
                <template slot="append">
                  <el-color-picker size="mini" v-model="formData.barFontColor"></el-color-picker>
                </template>
              </el-input>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="菜单高度">
            <el-input v-model="formData.barItemHeight" size="mini"  :min="20"  :max="300" type="number" style="margin-top: 5px" placeholder="文字大小">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="间隔高度">
            <el-input v-model="formData.barItemPadding" size="mini"  :min="2"  :max="35" type="number" style="margin-top: 5px" placeholder="文字大小">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <draggable v-model="formData.menuList" @start="datadragStart" @update="datadragUpdate" @end="datadragEnd"
                      :move="datadragMove" :options="{filter:'.notDraggable', preventOnFilter: false,animation:500}">
            <transition-group>
              <div  v-for="(item,index) in formData.menuList" :key="'dragItem'+index"  class="drag-item" >
                <el-button class="menu_list_tag notDraggable" size="mini" icon="el-icon-delete-solid" @click="delMenu(index)" type="danger"></el-button>
                <p class="menu_list_title_name">菜单名称</p>
                <el-input class="menu_list_title notDraggable" :draggable="false" v-model="item.title" size="mini" style="margin-top: 5px"> </el-input>
                <div class="notDraggable">
                  <wx-page-select :isSystemUrl="item.isSystemUrl"   @switchChange="item.isSystemUrl=$event" :page="item.pageUrl" @change="item.pageUrl=$event"></wx-page-select>
                </div>
              </div>
            </transition-group>
          </draggable>
          <el-button style="margin-left: 20px" @click="addMenu" size="medium" >添加</el-button>
        </el-form>
      </div>
    </settingSlot>
  </div>
</template>

<script>

import { mapState , mapGetters , mapMutations , mapActions } from 'vuex';

import settingSlot from './settingSlot'
import MaterialList from '@/components/material/wxlist.vue'
import WxPageSelect from '@/components/wx-page-select/Index.vue'
import draggable from "vuedraggable";
import iconSelect from '../pages/page-components/iconSelect.vue'

export default {
  components: { iconSelect,settingSlot, MaterialList, WxPageSelect, draggable  },
  data() {
    return {
      formDataCopy : {
        fixed: false,
        fontSize: 16,
        color: '#000000',
        backgroundColor: '#ffffff',
        pageUrl: '',
        titleIcon: '', //背景图
        title: '首页',
        barMode: 'right', //背景图
        barWidth: 200, //背景图
        topHeight: 200, //顶部高度
        barGroundColor: '#FFFFFF', //抽屉背景图
        barFontSize: 16, //抽屉背景图
        barFontColor: '#000000', //抽屉字体颜色
        barItemHeight: 60, //菜单高度
        barItemPadding: 2, //菜单上下边距
        menuList: [{
          title:"标题1",
          isSystemUrl:false,
          pageUrl: ""
        }]
      },
      formData : {}
    };
  },
  props: {
    clientType: [String],
    showData:{
      type: Object,
      default: ()=> {}
    },
    config   : {
      type: Object,
      default: ()=> {}
    }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
      clickComIndex : state => state.divpage.clickComIndex,
    })
  },
  watch: {
    showData: {
      handler(newVal, oldVal) {
        this.formData = newVal ? newVal : this.formData;
      },
      deep: true
    },
  },
  mounted(){
    let that = this;
    if(that.IsEmptyObj(that.showData)){
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
    } else {
      that.formData = that.showData
    }
    that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
    // that.updateData({
    //   componentsList: that.componentsList
    // })
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),

    // 添加项目
    addItem(){
      let that = this;
      that.pushItem()
    },
    pushItem(){
      let that = this;
      if(that.formData.noticeList.length >=10){
        that.$message.error("项目不能超过10条")
        return false;
      }
      that.formData.noticeList.push({
        id       : Math.random(),
        imageUrl : '',
        imgWidth : 0,
        imgHeight: 0,
        pageUrl  : '',
        content  : '',
        tag: ''
      })
    },
    // 删除项目
    delItem(index){
      let that = this;
      if(that.formData.swiperList.length<=1){
        that.$message.error("请至少保留一条项目")
        return false;
      }
      that.$confirm('是否删除该项目?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText : '取消',
        type : 'warning'
      }).then(()=>{
        that.$delete(that.formData.noticeList, index)
      }).catch(()=>{})
    },
    // 删除按钮
    delBtn(index){
      let that = this;
      that.$confirm('是否删除该按钮?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText : '取消',
        type : 'warning'
      }).then(()=>{
        that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
        that.updateData({ componentsList: that.componentsList });
      }).catch(()=>{})
    },
    cancel(){
      this.$emit('cancel')
    },
    reset(){
      let that = this;
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      that.componentsList[that.clickComIndex]['data'] = this.formData;
    },
    confirm(){
      this.$emit('confirm', this.formData)
    },
    delete(){
      this.$emit('delete')
    },
    isCustomUrlChange(val){
      if(val){
        this.formData.pageUrl = "";
      }else{
        this.formData.pageUrl =this.$refs.pageSelect.page
      }
    },
    addMenu(){
      this.formData.menuList.push({
        title:"标题1",
        isCustomUrl:false,
        pageUrl: ""
      })
    },
    delMenu(index){
      this.formData.menuList.splice(index, 1);
    },
    // 拖动的事件等等=======================================================>
    datadragStart(e) {
      let that = this;
      that.updateData({clickComIndex: null})
      that.showSetBlock = false;
    },
    datadragUpdate(e) {
      e.preventDefault();
    },
    datadragEnd(e) {
      // console.log(e, "拖动结束");
    },
    datadragMove(e, originalEve) {
      return (e.draggedContext.element.text !== 'Gold（不可拖动元素）');
    },
  },

};
</script>
<style lang='less' scoped>

@import '../colorui/main.css';
.el-form-item{
  margin-bottom: 0;
}
.menu_list_title_name{
  display: inline;
}
.menu_list_title{
  display: block;
  width: 80%;
}
.menu_list_tag{
  display: none;
  float: right;
}
.drag-item {
  padding: 0px 0 5px 20px;
  margin-bottom: 15px;
  margin-top: 20px;
  border: 1px solid transparent;
  &:hover {
    cursor: move;
    border: 1px dashed #1fc421;
    .menu_list_tag {
      display: inline;
    }
  }
}
</style>
