
import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/wxtagtype/page',
    method: 'get',
    params: query
  })
}

export function getList(query) {
  return request({
    url: '/weixin/wxtagtype/list',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/weixin/wxtagtype',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/weixin/wxtagtype/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/wxtagtype/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/wxtagtype',
    method: 'put',
    data: obj
  })
}
