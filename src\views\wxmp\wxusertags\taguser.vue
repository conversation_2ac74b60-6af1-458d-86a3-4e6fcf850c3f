<template>
  <div class="execution">
    <basic-container>
      <!-- 标签筛选表单 -->
      <el-form label-width="100px" class="search_form" :model="searchForm">
        <el-row type="flex" justify="start">
          <!-- 统一级联选择器配置 -->
          <el-form-item label="标签筛选:">
            <el-cascader
              size="small"
              v-model="searchTagIds"
              :options="cascaderOptions"
              :props="cascaderProps"
              placeholder="请选择标签"
              clearable>
            </el-cascader>
          </el-form-item>
          <el-form-item label="排除标签:">
            <el-cascader
              size="small"
              v-model="notInTagIds"
              :options="cascaderOptions"
              :props="cascaderProps"
              placeholder="请选择要排除的标签"
              clearable>
            </el-cascader>
          </el-form-item>
          <el-form-item>
            <el-button size="small" type="primary" @click="searchByTags">确认搜索</el-button>
            <el-button size="small" @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-row>
      </el-form>

      <!-- 用户数据表格 -->
      <avue-crud ref="crud"
                 :page.sync="page"
                 :data="tableData"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 :permission="permissionList"
                 @on-load="getPageByTag"
                 @refresh-change="refreshChange"
                 @row-update="handleUpdate"
                 @row-save="handleSave"
                 @row-del="handleDel"
                 @sort-change="sortChange"
                 @search-change="searchChange"
                 @selection-change="selectionChange">
        <!-- 订阅状态标签 -->
        <template slot="subscribe" slot-scope="scope">
          <el-tag size="mini" effect="dark" :type="getSubscribeTagType(scope.row.subscribe)">
            {{scope.row.$subscribe}}
          </el-tag>
        </template>

        <!-- 性别标签 -->
        <template slot="sex" slot-scope="scope">
          <el-tag size="mini" effect="light" :type="getSexTagType(scope.row.sex)">
            {{scope.row.$sex}}
          </el-tag>
        </template>

        <!-- 用户标签列表 -->
        <template slot="tagList" slot-scope="scope">
          <div class="tag-list-container" style="display: flex; flex-wrap: wrap; gap: 5px;">
            <el-tag
              v-for="tag in scope.row.tagList"
              :key="tag.id"
              size="mini"
              effect="plain"
              :style="{backgroundColor: tag.backColor, color: tag.fontColor}">
              {{tag.name}}
            </el-tag>
            <span v-if="!scope.row.tagList || scope.row.tagList.length === 0">无标签</span>
          </div>
        </template>

        <!-- 地理位置链接 -->
        <template slot="latitude" slot-scope="scope">
          <el-link
            v-if="scope.row.longitude"
            type="primary"
            target="_blank"
            :href="getMapUrl(scope.row)">
            <i class="el-icon-map-location"></i>
          </el-link>
        </template>

        <!-- 表格顶部按钮组 -->
        <template slot="menuLeft">
          <el-cascader
            size="small"
            v-model="selectedTagsForAction"
            :options="cascaderOptions"
            :props="cascaderProps"
            placeholder="请选择标签"
            clearable
            style="margin-right: 10px; width: 110px;margin-bottom: 10px;">
          </el-cascader>
          <el-button type="success"
                     @click="dialogTagging = true; taggingType = 'tagging'"
                     size="small"
                     icon="el-icon-document"
                     v-if="permissions['wxmp:wxuser:tagging']">群发消息</el-button>
          <el-button type="primary"
                     @click="showTaggingConfirm"
                     size="small"
                     icon="el-icon-price-tag"
                     v-if="permissions['wxmp:wxuser:tagging']">打标签</el-button>
          <el-button type="warning"
                     @click="showDeleteTagsConfirm"
                     size="small"
                     icon="el-icon-price-tag"
                     v-if="permissions['wxmp:wxuser:tagging']">删除标签</el-button>
          <el-button type="danger"
                     @click="delAllTagLink"
                     size="small"
                     icon="el-icon-delete-solid"
                     v-if="permissions['wxmp:wxuser:tagging']">清除全部</el-button>
          <el-button @click="addBackList"
                     size="small"
                     style="background: black;color: white"
                     icon="el-icon-user-solid"
                     v-if="permissions['wxmp:wxuser:synchro']">全部拉黑</el-button>
          <el-button size="small"
                     @click="addByIdBoxVisible = true"
                     icon="el-icon-add">新增用户</el-button>
        </template>

        <!-- 行操作按钮 -->
        <template slot="menu" slot-scope="scope">
          <el-button type="text"
                     v-if="permissions['wxmp:wxuser:edit:remark']"
                     icon="el-icon-edit"
                     size="small"
                     plain
                     @click="updateRemark(scope.row, scope.index)">修改备注</el-button>
          <el-button type="text"
                     icon="el-icon-price-tag"
                     size="small"
                     plain
                     @click="delTagLink(scope.row, scope.index)">删除标记</el-button>
        </template>

        <!-- 标签选择器 -->
        <template slot="tagidListSearch" slot-scope="scope">
          <el-select v-model="scope.row.tagidList" placeholder="请选择">
            <el-option
              v-for="item in userTagsData"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </template>
      </avue-crud>
    </basic-container>

    <!-- 添加用户对话框 -->
    <el-dialog
      title="添加用户"
      :visible.sync="addByIdBoxVisible"
      width="50%"
      append-to-body
      :close-on-click-modal="false"
      center>
      <el-form ref="form" label-width="80px">
        <el-form-item label="openId">
          <el-input
            v-model="addById.openId"
            :autosize="{ minRows: 10}"
            type="textarea"
            placeholder="请输入openId，多个请换行分隔">
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="addByOpenId">确认</el-button>
          <el-button @click="addByIdBoxVisible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 打标签确认对话框 -->
    <el-dialog
      title="确认打标签"
      :visible.sync="taggingDialogVisible"
      width="50%"
      append-to-body
      :close-on-click-modal="false"
      center>
      <div v-if="selectionData.length > 0">
        <h4>选中的用户：</h4>
        <div style="max-height: 200px; overflow-y: auto; margin-bottom: 15px;">
          <el-tag
            v-for="(user, index) in selectionData"
            :key="user.id"
            size="small"
            style="margin: 3px;">
            {{ user.nickName || '无昵称用户' }}
          </el-tag>
        </div>

        <h4>选中的标签：</h4>
        <div style="margin-bottom: 15px;">
          <el-tag
            v-for="(tagId, index) in selectedTagsForAction"
            :key="tagId"
            size="small"
            style="margin: 3px;">
            {{ getTagNameById(tagId) }}
          </el-tag>
        </div>
      </div>
      <div v-else>
        <el-alert
          title="请先选择用户"
          type="warning"
          :closable="false">
        </el-alert>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="taggingDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitTagging" :disabled="selectionData.length === 0">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 删除标签确认对话框 -->
    <el-dialog
      title="确认删除标签"
      :visible.sync="deleteTagsDialogVisible"
      width="50%"
      append-to-body
      :close-on-click-modal="false"
      center>
      <div v-if="selectionData.length > 0">
        <h4>选中的用户：</h4>
        <div style="max-height: 200px; overflow-y: auto; margin-bottom: 15px;">
          <el-tag
            v-for="(user, index) in selectionData"
            :key="user.id"
            size="small"
            style="margin: 3px;">
            {{ user.nickName || '无昵称用户' }}
          </el-tag>
        </div>

        <h4>选中的标签：</h4>
        <div style="margin-bottom: 15px;">
          <el-tag
            v-for="(tagId, index) in selectedTagsForAction"
            :key="tagId"
            size="small"
            style="margin: 3px;">
            {{ getTagNameById(tagId) }}
          </el-tag>
        </div>
      </div>
      <div v-else>
        <el-alert
          title="请先选择用户"
          type="warning"
          :closable="false">
        </el-alert>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteTagsDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitDeleteTags" :disabled="selectionData.length === 0">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import { getPageByTag, addObj, putObj, delObj, updateRemark } from '@/api/wxmp/wxuser'
  import { addById, getList as listUserTags, getSysList, addUserTags, deleteUserTags } from '@/api/wxmp/wxusertags'
  import { delTagLink, delAllTagLink } from '@/api/wxmp/wxusertaglink'
  import { addByTag } from '@/api/wxmp/wxbacklist'
  import { tableOption } from '@/const/crud/wxmp/taguser'
  import { mapGetters } from 'vuex'
  import { getList as getWxAppList } from "@/api/wxmp/wxapp"

  export default {
    name: 'wxuser',
    components: {},
    props: {
      selectedTagId: { type: String }, // 选中的标签
      appId: { type: String },
    },
    data() {
      return {
        // 用户添加相关
        addByIdBoxVisible: false,
        addById: {},

        // 表格数据相关
        tableData: [],
        tableLoading: false,
        tableOption: tableOption,
        selectionData: [],

        // 分页相关
        page: {
          total: 0,
          currentPage: 1,
          pageSize: 20,
          ascs: [],
          descs: 'subscribe_time'
        },

        // 搜索相关
        paramsSearch: {},
        searchForm: {},

        // 标签相关
        dialogTagging: false,
        taggingType: '',
        checkedTags: [],
        userTagsData: [],
        wxAppList: [], // 公众号列表
        cascaderOptions: [], // 级联选择器的选项
        searchTagIds: [], // 搜索用的标签ID数组
        notInTagIds: [], // 排除用的标签ID数组
        selectedTagsForAction: [], // 用于群发消息和打标签的标签选择器
        taggingDialogVisible: false, // 打标签确认对话框
        deleteTagsDialogVisible: false, // 删除标签确认对话框
        cascaderProps: {
          checkStrictly: false,
          expandTrigger: 'hover',
          value: 'id',
          label: 'name',
          children: 'children',
          emitPath: false,
          showAllLevels: true,
          multiple: true
        }
      }
    },
    created() {
      this.initTagData()

      if (this.appId && this.selectedTagId) {
        this.listUserTags()
      }
    },
    mounted() {
      this.$nextTick(() => {
        if (this.$refs.crud) {
          this.$refs.crud.refreshChange()
        }
      })
    },
    computed: {
      ...mapGetters(['permissions']),
      permissionList() {
        return {
          addBtn: this.permissions['wxmp:wxuser:add'] || false,
          delBtn: this.permissions['wxmp:wxuser:del'] || false,
          editBtn: this.permissions['wxmp:wxuser:edit'] || false,
          viewBtn: this.permissions['wxmp:wxuser:get'] || false,
        }
      }
    },
    methods: {
      // 标签数据初始化
      initTagData() {
        if (this.appId) {
          this.loadTagsByAppId(this.appId)
        } else {
          this.loadAllAppTags()
        }
      },

      // 加载指定公众号的标签
      loadTagsByAppId(appId) {
        this.tableLoading = true
        getSysList({ appId }).then(res => {
          if (res.data && res.data.data) {
            this.buildCascaderOptions(res.data.data, appId)
          }
          this.tableLoading = false
        }).catch(err => {
          console.error('获取标签数据失败:', err)
          this.tableLoading = false
        })
      },

      // 加载所有公众号的标签
      loadAllAppTags() {
        getWxAppList({ appType: '2' }).then(res => {
          this.wxAppList = res.data
          if (this.wxAppList && this.wxAppList.length > 0) {
            this.fetchAllAppTags()
          }
        }).catch(() => {})
      },

      // 获取所有公众号的标签数据
      fetchAllAppTags() {
        const promises = []
        const appData = {}

        this.wxAppList.forEach(app => {
          const promise = getSysList({ appId: app.id }).then(res => {
            appData[app.id] = {
              appInfo: app,
              tagsData: res.data.data
            }
          })
          promises.push(promise)
        })

        Promise.all(promises).then(() => {
          this.buildCascaderOptionsFromAllApps(appData)
        }).catch(err => {
          console.error('获取标签数据失败:', err)
        })
      },

      // 构建单个公众号的级联选择器数据
      buildCascaderOptions(tagsData, appId) {
        const options = []

        // 处理公众号级别
        if (this.wxAppList && this.wxAppList.length > 0) {
          const app = this.wxAppList.find(app => app.id === appId)
          if (app) {
            const appOption = this.createAppOption(app, tagsData)
            if (appOption.children.length > 0) {
              options.push(appOption)
            }
          }
        } else {
          // 直接处理标签分类级别
          this.processTagTypes(options, tagsData)
        }

        this.cascaderOptions = options
      },

      // 从所有公众号数据构建级联选择器选项
      buildCascaderOptionsFromAllApps(appData) {
        const options = []

        Object.keys(appData).forEach(appId => {
          const { appInfo, tagsData } = appData[appId]
          const appOption = this.createAppOption(appInfo, tagsData)

          if (appOption.children.length > 0) {
            options.push(appOption)
          }
        })

        this.cascaderOptions = options
      },

      // 创建公众号选项
      createAppOption(app, tagsData) {
        const appOption = {
          id: app.id,
          name: app.name,
          children: []
        }

        this.processTagTypes(appOption.children, tagsData)
        return appOption
      },

      // 处理标签分类
      processTagTypes(targetArray, tagsData) {
        if (!tagsData || !tagsData.length) return

        tagsData.forEach(typeItem => {
          if (!typeItem.tagList || !typeItem.tagList.length) return

          const typeOption = {
            id: typeItem.typeId,
            name: typeItem.tagTypeName,
            children: []
          }

          typeItem.tagList.forEach(tag => {
            typeOption.children.push({
              id: tag.id,
              name: tag.name,
              backColor: tag.backColor,
              fontColor: tag.fontColor
            })
          })

          if (typeOption.children.length > 0) {
            targetArray.push(typeOption)
          }
        })
      },

      // 加载用户标签数据
      listUserTags() {
        this.tableLoading = true
        listUserTags({
          appId: this.appId
        }).then(res => {
          if (res.data.code === '0') {
            this.userTagsData = res.data.data
            this.$refs.crud.DIC.tagidList = this.userTagsData
          } else {
            this.$message.error('获取用户标签出错：' + res.data.msg)
          }
          this.tableLoading = false
          this.getPageByTag(this.page)
        }).catch(() => {
          this.tableLoading = false
          this.getPageByTag(this.page)
        })
      },

      // 根据标签搜索用户
      searchByTags() {
        this.page.currentPage = 1
        this.getPageByTag(this.page)
      },

      // 重置搜索条件
      resetSearch() {
        this.searchTagIds = []
        this.notInTagIds = []
        this.paramsSearch = {}
        this.searchForm = {}
        this.page.currentPage = 1
        this.getPageByTag(this.page)
      },

      // 表格选择变化
      selectionChange(list) {
        this.selectionData = list
      },

      // 搜索条件变化
      searchChange(params, done) {
        this.paramsSearch = params
        this.page.currentPage = 1
        this.getPageByTag(this.page, params)
        done()
      },

      // 排序变化
      sortChange(val) {
        let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''

        if (val.order === 'ascending') {
          this.page.descs = []
          this.page.ascs = prop
        } else if (val.order === 'descending') {
          this.page.ascs = []
          this.page.descs = prop
        } else {
          this.page.ascs = []
          this.page.descs = []
        }

        this.getPageByTag(this.page)
      },

      // 获取分页数据
      getPageByTag(page, params) {
        if (!this.appId) return

        this.tableLoading = true

        // 构建请求参数
        const requestParams = this.buildRequestParams(page, params)

        getPageByTag(requestParams).then(res => {
          this.tableData = res.data.data.records
          this.page.total = res.data.data.total
          this.page.currentPage = page.currentPage
          this.page.pageSize = page.pageSize
          this.tableLoading = false
        }).catch(() => {
          this.tableLoading = false
        })
      },

      // 构建请求参数
      buildRequestParams(page, params) {
        const requestParams = {
          current: page.currentPage,
          size: page.pageSize,
          descs: page.descs || this.page.descs,
          ascs: page.ascs || this.page.ascs,
          appType: '2',
          appId: this.appId,
          tagId: this.selectedTagId
        }

        // 添加多标签筛选
        if (this.searchTagIds && this.searchTagIds.length > 0) {
          requestParams.tagIds = this.searchTagIds.join(',')
        }

        // 添加排除标签
        if (this.notInTagIds && this.notInTagIds.length > 0) {
          requestParams.notInTagIds = this.notInTagIds.join(',')
        }

        // 合并额外参数
        if (params) {
          Object.assign(requestParams, params)
        }

        // 合并查询参数
        if (this.paramsSearch) {
          Object.assign(requestParams, this.paramsSearch)
        }

        return requestParams
      },

      // 修改用户备注
      updateRemark(row, index) {
        this.$prompt('请输入备注', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /\S/,
          inputErrorMessage: '输入不能为空'
        }).then(({ value }) => {
          this.tableLoading = true
          row.remark = value

          updateRemark(row).then(res => {
            this.tableLoading = false
            if (res.data.code === '0') {
              this.tableData.splice(index, 1, Object.assign({}, row))
              this.$message({
                showClose: true,
                message: '修改成功',
                type: 'success'
              })
              this.getPageByTag(this.page)
            } else {
              this.$message.error(res.data.msg)
            }
          }).catch(() => {
            this.tableLoading = false
          })
        })
      },

      // 删除用户
      handleDel(row, index) {
        this.$confirm('是否确认删除', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          return delObj(row.id)
        }).then(data => {
          this.$message({
            showClose: true,
            message: '删除成功',
            type: 'success'
          })
          this.refreshChange()
        }).catch(() => {})
      },

      // 更新用户数据
      handleUpdate(row, index, done, loading) {
        putObj(row).then(data => {
          this.$message({
            showClose: true,
            message: '修改成功',
            type: 'success'
          })
          done()
          this.getPageByTag(this.page)
        }).catch(() => {
          loading()
        })
      },

      // 添加用户数据
      handleSave(row, done, loading) {
        addObj(row).then(data => {
          this.$message({
            showClose: true,
            message: '添加成功',
            type: 'success'
          })
          done()
          this.getPageByTag(this.page)
        }).catch(() => {
          loading()
        })
      },

      // 刷新表格
      refreshChange() {
        this.$emit("reFresh")
        this.getPageByTag(this.page)
      },

      // 全部拉黑
      addBackList() {
        this.$confirm('确定要全部拉黑吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          addByTag({
            id: this.selectedTagId,
            appId: this.appId
          }).then(res => {
            this.$message({
              showClose: true,
              message: '拉黑成功',
              type: 'success'
            })
          }).catch(() => {})
        }).catch(() => {})
      },

      // 删除标签标记
      delTagLink(obj) {
        this.$confirm('确定要删除标记吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          delTagLink({
            userId: obj.id,
            tagId: this.selectedTagId,
            appId: this.appId
          }).then(res => {
            this.$message({
              showClose: true,
              message: '删除成功',
              type: 'success'
            })
            this.refreshChange()
          }).catch(() => {})
        }).catch(() => {})
      },

      // 删除标签全部内容
      delAllTagLink() {
        this.$confirm('确定要清空标记吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          delAllTagLink({
            tagId: this.selectedTagId,
            appId: this.appId
          }).then(res => {
            this.$message({
              showClose: true,
              message: '清空成功',
              type: 'success'
            })
            this.refreshChange()
          }).catch(() => {})
        }).catch(() => {})
      },

      // 通过OpenID添加用户
      addByOpenId() {
        this.addById.openIdList = this.addById.openId.split(/[(\r\n)\r\n]+/)
        this.addById.appId = this.appId
        this.addById.tagId = this.selectedTagId

        addById(this.addById).then(res => {
          this.refreshChange()
          this.addByIdBoxVisible = false
        }).catch(() => {})
      },

      // 辅助方法：获取订阅状态标签类型
      getSubscribeTagType(subscribe) {
        if (subscribe === '1') {
          return 'success'
        } else if (subscribe === '0') {
          return 'danger'
        } else {
          return 'warning'
        }
      },

      // 辅助方法：获取性别标签类型
      getSexTagType(sex) {
        if (sex === '1') {
          return ''
        } else if (sex === '2') {
          return 'danger'
        } else {
          return 'warning'
        }
      },

      // 辅助方法：获取地图URL
      getMapUrl(row) {
        if (row.longitude && row.latitude) {
          return `https://map.qq.com/?type=marker&isopeninfowin=1&markertype=1&pointx=${row.longitude}&pointy=${row.latitude}&name=${row.nickName}&ref=gocreateone`
        }
        return '#'
      },

      // 显示打标签确认对话框
      showTaggingConfirm() {
        if (!this.selectedTagsForAction || this.selectedTagsForAction.length === 0) {
          this.$message.warning('请选择要打标签的标签');
          return;
        }

        if (!this.selectionData || this.selectionData.length === 0) {
          this.$message.warning('请选择要打标签的用户');
          return;
        }

        this.taggingDialogVisible = true;
      },

      // 显示删除标签确认对话框
      showDeleteTagsConfirm() {
        if (!this.selectedTagsForAction || this.selectedTagsForAction.length === 0) {
          this.$message.warning('请选择要删除的标签');
          return;
        }

        if (!this.selectionData || this.selectionData.length === 0) {
          this.$message.warning('请选择要删除标签的用户');
          return;
        }

        this.deleteTagsDialogVisible = true;
      },

      // 获取标签名称
      getTagNameById(tagId) {
        // 在级联选择器中查找标签
        const findTagInOptions = (options) => {
          for (const option of options) {
            if (option.id === tagId) {
              return option.name;
            }
            if (option.children && option.children.length > 0) {
              const name = findTagInOptions(option.children);
              if (name) return name;
            }
          }
          return null;
        };

        const tagName = findTagInOptions(this.cascaderOptions);
        return tagName || tagId;
      },

      // 提交打标签请求
      submitTagging() {
        if (!this.selectedTagsForAction || this.selectedTagsForAction.length === 0) {
          this.$message.warning('请选择要打标签的标签');
          return;
        }

        if (!this.selectionData || this.selectionData.length === 0) {
          this.$message.warning('请选择要打标签的用户');
          return;
        }

        const userIds = this.selectionData.map(user => user.id);
        const tagIds = this.selectedTagsForAction;

        // 构建请求数据结构
        const requestData = {
          userIds: userIds,
          tagIds: tagIds,
          appId: this.appId
        };

        console.log('打标签请求数据:', requestData);

        // 这里调用API进行打标签操作
        this.tableLoading = true;

        addUserTags(requestData)
          .then(res => {
            this.$message({
              showClose: true,
              message: '打标签成功',
              type: 'success'
            });
            this.taggingDialogVisible = false;
            this.refreshChange();
          })
          .catch(err => {
            console.error('打标签失败:', err);
            this.$message.error('打标签失败');
          })
          .finally(() => {
            this.tableLoading = false;
          });
      },

      // 提交删除标签请求
      submitDeleteTags() {
        if (!this.selectedTagsForAction || this.selectedTagsForAction.length === 0) {
          this.$message.warning('请选择要删除的标签');
          return;
        }

        if (!this.selectionData || this.selectionData.length === 0) {
          this.$message.warning('请选择要删除标签的用户');
          return;
        }

        const userIds = this.selectionData.map(user => user.id);
        const tagIds = this.selectedTagsForAction;

        // 构建请求数据结构
        const requestData = {
          userIds: userIds,
          tagIds: tagIds,
          appId: this.appId
        };

        console.log('删除标签请求数据:', requestData);

        this.tableLoading = true;

        // 使用API方法进行删除标签操作
        deleteUserTags(requestData)
          .then(res => {
              this.$message({
                showClose: true,
                message: '删除标签成功',
                type: 'success'
              });
              this.deleteTagsDialogVisible = false;
              this.refreshChange();
          })
          .catch(err => {
            console.error('删除标签失败:', err);
            this.$message.error('删除标签失败');
          })
          .finally(() => {
            this.tableLoading = false;
          });
      },

      // 执行打标签操作
      taggingUsers() {
        const tagIds = this.selectedTagsForAction;
        const userIds = this.selectionData.map(user => user.id);

        if (!userIds || userIds.length === 0) {
          this.$message.warning('请选择要打标签的用户');
          return;
        }

        // 构建请求数据结构
        const requestData = {
          userIds: userIds,
          tagIds: tagIds,
          appId: this.appId
        };

        console.log('打标签请求数据:', requestData);

        this.tableLoading = true;
        addUserTags(requestData)
          .then(res => {
            this.$message({
              showClose: true,
              message: '打标签成功',
              type: 'success'
            });
            this.refreshChange();
          })
          .catch(err => {
            console.error('打标签失败:', err);
            this.$message.error('打标签失败');
          })
          .finally(() => {
            this.tableLoading = false;
          });
      }
    },
    watch: {
      // 监听标签ID变化
      selectedTagId(newVal, oldVal) {
        if (newVal !== oldVal && newVal) {
          this.page.currentPage = 1
          this.getPageByTag(this.page)
        }
      },
      // 监听公众号ID变化
      appId(newVal, oldVal) {
        if (newVal !== oldVal && newVal) {
          this.initTagData()
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
.execution {
  .search_form {
    margin-bottom: 15px;
  }

  .tag-list-container {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    max-width: 100%;
  }
}
</style>
