<template>
    <div name="page">
      <span slot="label"><i class="el-icon-tickets"></i>站内页面</span>
      <el-row>
        <div class="select-item" v-if="objData.content">
          <el-row class="ope-row">
            <div>
              <div>
                <h3 style="text-align: left">{{objData.content.articles[0].title}}</h3>
              </div>
              <el-row>
                <el-col :span="16">
                  <p style="text-align: left;font-size: 12px">{{objData.content.articles[0].digest}}</p>
                </el-col>
                <el-col :span="8">
                  <img style="float: left;width: 50px;height: 50px" :src="objData.content.articles[0].thumbUrl">
                </el-col>
              </el-row>
            </div>
            <el-button type="danger" icon="el-icon-delete" circle @click="deleteObj"></el-button>
          </el-row>
        </div>
        <div v-if="!objData.content">
          <el-row style="text-align: center">
            <el-col :span="24" class="col-select2">
              <el-button type="success" @click="openMaterial">页面选择<i
                class="el-icon-circle-check el-icon--right"></i></el-button>
            </el-col>
          </el-row>
        </div>
        <el-dialog title="选择页面" :visible.sync="pageVisible" width="80%" append-to-body @close="pageVisible = false">
          <el-row>
            <wx-page-select :showCustom="false" :isSystemUrl="true"   @change="ensurePage"></wx-page-select>
          </el-row>
          <el-row>
            <el-button class="page_select_btn" size="small" type="primary" @click="selectPage">确定</el-button>
          </el-row>
        </el-dialog>
      </el-row>
    </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import WxPageSelect from "@/components/wx-page-select/Index"
  import { h5HostMobile } from '@/config/env.js'
  export default {
    name: "wxReplyPage",
    components: {
      WxPageSelect,
    },
    props: {
      objData:{
        type: Object
      },
      oneNews:{
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        pageVisible:false,
        h5HostMobile:h5HostMobile,
        content:{},//选中的页面
      }
    },
    computed: {
      ...mapGetters(['permissions'])
    },
    methods:{
      deleteObj(){
        this.$delete(this.objData,'repName')
        this.$delete(this.objData,'repUrl')
        this.$delete(this.objData,'content')
      },
      openMaterial(){
        this.pageVisible = true
      },
      getPage(page, params) {

      },
      selectPage(){
        // this.$refs.wxPageSelect.clearValue();
        this.$set(this.objData,'content',this.content)
        this.pageVisible = false;
      },
      ensurePage(url,obj){
        let page = obj[2];
        console.log("确认了1111",obj)
        this.content = {
          articles: [{
            title: page.pageBase.shareTitle,
            digest: page.pageBase.describe,
            thumbUrl: page.pageBase.shareImgUrl,
            url: this.h5HostMobile+ url,
          }]
        };
        console.log("确定的页面",this.content)

      },
    }
  };
</script>

<style lang="scss" scoped>

.ope-row {
  padding-top: 10px;
  text-align: center;
}

.select-item {
  width: 280px;
  padding: 10px;
  margin: 0 auto 10px auto;
  border: 1px solid #eaeaea;
}

.page_select_btn {
  margin-top: 20px;
  float: right;
}
</style>
