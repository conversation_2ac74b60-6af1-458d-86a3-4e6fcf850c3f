<template>
  <div class="execution">
    <basic-container>
      <avue-crud ref="crud"
                 :page="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 v-model="form"
                 @on-load="getPage"
                 @refresh-change="refreshChange"
                 @row-update="handleUpdate"
                 @row-save="handleSave"
                 @row-del="handleDel"
                 @sort-change="sortChange"
                 @search-change="searchChange"
                 @row-click="rowClick">
        <template slot-scope="scope" slot="menuLeft">
          <el-button type="primary"
                     icon="el-icon-plus"
                     size="small"
                     @click.stop="openFromBox('add')">新增微信消息
          </el-button>
        </template>
        <template slot-scope="scope" slot="menu">
          <el-button icon="el-icon-edit" type="text" size="small" @click.stop="preView(scope.row)">预览
          </el-button>
          <el-button icon="el-icon-edit" type="text" size="small" @click.stop="openFromBox('put',scope.row)">编辑
          </el-button>
          <el-button icon="el-icon-delete" type="text" size="small" @click.stop="handleDel(scope.row)">删除</el-button>
        </template>
      </avue-crud>
      <!--      提交、修改弹出框-->
      <el-dialog
        :title="qrMessageBoxForm.title"
        :visible.sync="qrCodeBoxVisible"
        :close-on-click-modal="false"
        :append-to-body="true"
        :destroy-on-close="true"
        width="70%">
        <div style="overflow: auto; height: 60vh">
          <el-form :rules="formRules" :ref="qrMessageBoxForm" :model="qrMessageBoxForm" label-width="auto"
                   :destroy-on-close="true">
            <el-form-item label="公众号" prop="appId">
              <el-select v-model="qrMessageBoxForm.appId" placeholder="请选择" :disabled="checkSelect()">
                <el-option
                  v-for="item in wxAppList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="消息名称" prop="name">
              <el-input v-model="qrMessageBoxForm.name" :maxlength="25" style="width:450px" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="消息类型" >
              <el-button v-if="!(type=='1')" icon="el-icon-plus" type="primary" size="mini" @click="addMsg('text')">文本消息</el-button>
              <el-button v-if="!(type=='1')" icon="el-icon-plus" type="primary" size="mini" @click="addMsg('news')">图文消息</el-button>
              <el-button v-if="!(type=='1')"  icon="el-icon-plus" type="primary" size="mini" @click="addMsg('coupon')">卡劵</el-button>
              <el-button v-show="getTempLateBtn()" icon="el-icon-plus" type="primary" size="mini" @click="addMsg('template')">模版消息</el-button>
              <el-button v-if="!(type=='1')" icon="el-icon-plus" type="primary" size="mini" @click="addMsg('image')">图片</el-button>
              <el-button v-if="!(type=='1')" icon="el-icon-plus" type="primary" size="mini" @click="addMsg('video')">视频</el-button>
              <el-button v-if="!(type=='1')" icon="el-icon-plus" type="primary" size="mini" @click="addMsg('music')">语音</el-button>
              <el-button v-if="!(type=='1')" icon="el-icon-plus" type="primary" size="mini" @click="addMsg('page')">站内页面</el-button>
            </el-form-item>
            <el-form-item>
            </el-form-item>
            <div v-for="(item,index) in qrMessageBoxForm.content" :key="index">
              <el-form-item>
                <div class="msg_box">
                  <WxReplyText v-if="item.repType =='text'" :objData="item"></WxReplyText>
                  <WxReplyNews v-if="item.repType =='news'" :objData="item"></WxReplyNews>
                  <WxReplyTemplate v-if="item.repType =='template'" :objData="item"></WxReplyTemplate>
                  <WxReplyImage v-if="item.repType =='image'" :objData="item"></WxReplyImage>
                  <WxReplyVideo v-if="item.repType =='video'" :objData="item"></WxReplyVideo>
                  <WxReplyMusic v-if="item.repType =='music'" :objData="item"></WxReplyMusic>
                  <WxReplyPage v-if="item.repType =='page'" :objData="item"></WxReplyPage>
                  <div class="msg_del_icon">
                    <el-button type="danger" icon="el-icon-delete" size="mini" @click="delMsg(index)"></el-button>
                  </div>
                </div>
              </el-form-item>
            </div>
            <el-form-item>
              <el-button type="primary" @click="submit">{{ qrMessageBoxForm.submitButton }}</el-button>
              <el-button @click="qrCodeBoxVisible = false ">取消</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-dialog>
      <!--      图片预览框-->
      <el-dialog
        :visible.sync="preViewBoxVisible"
        :show-close="false"
        :append-to-body="true"
        width="30%"
        center>
        <div style="display: flex;align-items: center;justify-content: center;">
          <el-image :src="preViewUrl">
            <div slot="placeholder">
              加载中<span>...</span>
            </div>
          </el-image>
        </div>
        <span slot="title" class="pre_view_title">
          <span>预览二维码</span>
        </span>
      </el-dialog>

    </basic-container>
  </div>
</template>

<script>
import {getPage, getPreView, addObj, putObj, delObj, test} from '@/api/wxmp/wxmpqrcodemessage'
import {tableOption} from '@/const/crud/wxmp/wxmpqrcodemessage'
import {mapGetters} from 'vuex'
import {getList as getWxAppList} from '@/api/wxmp/wxapp'
import WxReplyText from '@/components/wx-reply/text.vue'
import WxReplyImage from '@/components/wx-reply/image.vue'
import WxReplyNews from '@/components/wx-reply/news.vue'
import WxReplyTemplate from '@/components/wx-reply/template.vue'
import WxReplyVideo from '@/components/wx-reply/video.vue'
import WxReplyMusic from '@/components/wx-reply/music.vue'
import WxReplyPage from '@/components/wx-reply/page.vue'

export default {
  name: 'wxmpqrcodemessage',
  props: {
    selectedAppId: { //选中的appId
      type: String
    },
    type: {//消息的类型 0为扫码消息  1为订阅消息
      type: String,
      default:'0'
    },
  },
  watch: {
    selectedAppId(newVal, oldVal) {
      // console.log("监听知道的改变", newVal);
      this.appId = newVal;
      this.getPage(this.page, {appId: this.appId,type:this.type});
    }
  },
  components: {
    WxReplyText,
    WxReplyImage,
    WxReplyNews,
    WxReplyTemplate,
    WxReplyVideo,
    WxReplyMusic,
    WxReplyPage,
  },
  data() {
    return {
      appId: "",//父级组件传入的Id
      wxAppList: [],
      qrMessageBoxForm: {},
      qrCodeBoxVisible: false,
      preViewBoxVisible: false,
      preViewUrl: '',
      formRules: {
        appId: [
          {required: true, message: '请选择公众号', trigger: 'submit'}
        ],
        name: [
          {required: true, message: '请输入二维码名称', trigger: 'submit'},
        ],
      },
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption
    }
  },
  created() {
    this.getApp();
    if (this.selectedAppId) {
      this.appId = this.selectedAppId;
      this.getPage(this.page);
    }
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.permissions['weixin:wxmpqrcodemessage:add'] ? true : false,
        delBtn: this.permissions['weixin:wxmpqrcodemessage:del'] ? true : false,
        editBtn: this.permissions['weixin:wxmpqrcodemessage:edit'] ? true : false,
        viewBtn: this.permissions['weixin:wxmpqrcodemessage:get'] ? true : false
      };
    }
  },
  methods: {
    getApp() {
      getWxAppList({
        appType: '2'
      }).then(res => {
        let data = res.data
        this.wxAppList = data;
        console.log("app", data)
        //默认加载第一个公众号的素材
        tableOption.column[4].dicData = [];
        for (let i = 0; i < data.length; i++) {
          tableOption.column[4].dicData.push({label: data[i].name, value: data[i].id});
        }
        //如果父组件传值
        if (this.appId) {
          tableOption.column[4].dicData = [];
          for (let i = 0; i < data.length; i++) {
            if (data[i].id == this.appId) {
              tableOption.column[4].dicData.push({label: data[i].name, value: data[i].id});
              break;
            }
          }
        }
      }).catch(() => {
      })
    },
    searchChange(params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    sortChange(val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage(page, params) {
      this.tableLoading = true
      //有父组件传值时
      if (this.appId) {
        this.paramsSearch.appId = this.appId;
      }
      if (this.type) {
        this.paramsSearch.type = this.type;
      }
      console.log("sadas",this.paramsSearch)
      getPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
      }, params, this.paramsSearch)).then(res => {
        this.tableData = res.data.data.records
        this.page.total = res.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this
      this.$confirm('是否确认删除此数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        return delObj(row.id)
      }).then(data => {
        _this.$message({
          showClose: true,
          message: '删除成功',
          type: 'success'
        })
        this.getPage(this.page)
      }).catch(function (err) {
      })
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      putObj(row).then(res => {
        this.$message({
          showClose: true,
          message: '修改成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      addObj(row).then(res => {
        this.$message({
          showClose: true,
          message: '添加成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      console.log("刷新了")
      this.getPage(this.page)
    },
    //预览
    preView(obj) {
      //请求二维码地址
      // this.preViewUrl = '        https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=gQGa7zwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyTWRvNHdLdmVjREgxY05mSWh4Y08AAgR9zixhAwS0AAAA';
      // this.preViewBoxVisible = true;
      // console.log(11)
      // test().then(res => {
      //   console.log(res)
      // }).catch(() => {
      // })
      // return
      getPreView(Object.assign(obj)).then(res => {
        console.log("请求二维码地址", res);
        this.preViewUrl = res.data.data;
        this.preViewBoxVisible = true;
      }).catch(() => {
      })
    },
    openFromBox(type, obj) {
      if (type == "add") {
        this.qrMessageBoxForm = {
          title: "新建消息",
          type: "add",
          submitButton: "立即创建",
          content: [],
        };
        if (this.appId) {
          this.qrMessageBoxForm.appId = this.appId;
        }
        this.qrCodeBoxVisible = true;
      } else if (type == "put") {
        let param = JSON.parse(JSON.stringify(obj));
        console.log("param", param)
        this.qrMessageBoxForm = param;
        this.qrMessageBoxForm.title = "修改消息";
        this.qrMessageBoxForm.type = "put";
        this.qrMessageBoxForm.submitButton = "立即修改";
        if (typeof param.content == 'string') {
          this.qrMessageBoxForm.content = JSON.parse(param.content);
        }
        this.qrCodeBoxVisible = true;
      }
    },
    submit() {
      console.log("提交", this.qrMessageBoxForm);
      this.$refs[this.qrMessageBoxForm].validate((valid) => {
        if (valid) {
          let obj = JSON.parse(JSON.stringify(this.qrMessageBoxForm));
          obj.content = JSON.stringify(obj.content);
          obj.type = this.type;
          console.log("提交obj", obj);
          if (!obj.id) {
            addObj(obj).then(res => {
              this.$message({
                showClose: true,
                message: '添加成功',
                type: 'success'
              })
              this.qrCodeBoxVisible = false;
              this.refreshChange()
            }).catch(() => {
            })
          } else {
            putObj(Object.assign(obj)).then(res => {
              this.goodsBoxVisible = false;
              this.$message({
                showClose: true,
                message: '修改成功',
                type: 'success'
              })
              this.qrCodeBoxVisible = false;
              this.refreshChange();
            }).catch(() => {
            })
          }
        }
      });
    },
    //提交信息
    addMsg(type) {
      console.log("添加消息")
      if (!this.qrMessageBoxForm.appId) {
        this.$message.error("请先选择公众号")
        return;
      }
      this.qrMessageBoxForm.content.push({repType: type, appId: this.qrMessageBoxForm.appId});
    },
    delMsg(index) {
      this.qrMessageBoxForm.content.splice(index, 1)
    },
    rowClick(row, event, column) {
      if (this.$listeners['ensureMsg']) {
        this.$emit("ensureMsg", row);
      }
    },
    checkSelect() {
      if (this.qrMessageBoxForm.type == 'put' || this.appId) {
        return true;
      }
      return false;
    },
    getTempLateBtn(){
      if(this.type == '0'){
        return true;
      }
      if(this.type == '1'){
        if(this.qrMessageBoxForm && this.qrMessageBoxForm.content && this.qrMessageBoxForm.content.length<1){
          return true;
        }else{
          return false;
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.msg_box {
  //background: #ECECEC;
  border: 1px solid #EBEEF5;
  border-radius: 5px;
  transition: .3s;
  padding: 5px;
}

.msg_del_icon {
  position: absolute;
  top: 2px;
  right: 10px;
}

.pre_view_title {
  text-align: center;
}
</style>
