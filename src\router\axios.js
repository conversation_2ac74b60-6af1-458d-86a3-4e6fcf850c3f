/**
 * 全站http配置
 *
 * header参数说明
 * serialize是否开启form表单提交
 * isToken是否需要token
 */
import axios from 'axios'
import store from '@/store'
import router from '@/router/router'
import { serialize, judgePlatformPage } from '@/util/util'
import NProgress from 'nprogress' // progress bar
import errorCode from '@/const/errorCode'
import { Message, MessageBox } from 'element-ui'
import 'nprogress/nprogress.css'
// import { switchDemon } from '@/config/env'
import qs from "qs";
import { getStore, setStore } from '@/util/store'

let requestList=[];//请求集合  用于防抖

axios.defaults.timeout = 30000
// 返回其他状态吗
axios.defaults.validateStatus = function (status) {
  return status // 默认的
}
// 跨域请求，允许保存cookie
axios.defaults.withCredentials = true
// NProgress Configuration  通过将加载微调器设置为false来关闭
NProgress.configure({
  showSpinner: false
})

// HTTPrequest拦截
axios.interceptors.request.use(config => {
  // if(switchDemon){
  //   if(config.url && config.url.indexOf("/auth/") == -1 &&
  //     config.url.indexOf("/register") == -1 &&
  //     config.url.indexOf("/generator/view") == -1 &&
  //     config.url.indexOf("/wxqrcode/unlimited") == -1 &&
  //       (store.getters.userInfo.id == '1287604554753417218' || store.getters.userInfo.id == '1287605593112092673'
  //         || store.getters.userInfo.id == '1287610097266106369' || store.getters.userInfo.id == '1287611668582408194') &&
  //     config.method !== 'get'){
  //     MessageBox.confirm('演示账号，不能操作。<a href="http://www.gocreateone.com" target="_blank" style="color: red">前去官网获取源码</a><br>客服QQ（1023530620）', '提示',{
  //       confirmButtonText: '确定',
  //       cancelButtonText: '取消',
  //       dangerouslyUseHTMLString: true,
  //       type: 'warning'
  //     }).then(() => {
  //       // store.dispatch("LogOut").then(() => {
  //       //   router.push({ path: '/register' })
  //       // })
  //     }).catch(() => {
  //
  //     })
  //     return Promise.reject('演示环境')
  //   }
  // }
  //以上可考虑删除
  //只针对get方式进行序列化
  if (config.method === 'get') {
    config.paramsSerializer = function(params) {
      return qs.stringify(params, { arrayFormat: 'repeat' })
    }
  }
  NProgress.start() // start progress bar
  const isToken = (config.headers || {}).isToken === false
  let token = store.getters.access_token
  if (token && !isToken) {
    config.headers['Authorization'] = 'Bearer ' + token// token
  }
  let switchTenantId = getStore({ name: 'switchTenantId' })
  if(switchTenantId && !judgePlatformPage()
    && config.url != "/upms/menu/tree"){
    //只有非平台系统页面传切换租户ID
    config.headers['switch-tenant-id'] = switchTenantId // 切换租户ID
  }

  //防抖
  if (config.method === 'post' || config.method === 'put' ) { //put或post 请求进行防抖处理

    let cancel
    // 设置cancelToken对象
    config.cancelToken = new axios.CancelToken(function(c) {
      cancel = c
    })
    // 阻止重复请求。当上个请求未完成时，相同的请求不会进行
    stopRepeatRequest(requestList, config.url, cancel, `${config.url} 请求被中断`)
    return config
  }

  return config
}, error => {
  return Promise.reject(error)
})


// HTTPresponse拦截
axios.interceptors.response.use(res => {
    // 增加延迟，相同请求不得在短时间内重复发送
    setTimeout(() => {
      allowRequest(requestList, res.config.url);
    }, 1000)


  NProgress.done()
  const status = Number(res.status) || 200
  const message = res.data.msg || errorCode[status] || errorCode['default']
  if (status === 401 && res.data.data === 'invalid_token') {
    Message({
      message: '登录过期，请重新登录',
      type: 'error'
    })
    store.dispatch('FedLogOut').then(() => {
      router.push({ path: '/login' })
    })
    return
  }

  if (status !== 200 || res.data.code === 1) {
    Message({
      message: message,
      type: 'error'
    })
    return Promise.reject(new Error(message))
  }

  return res
}, error => {
  if (axios.isCancel(error)) {
    // console.log(error.message);
  } else {
    // 增加延迟，相同请求不得在短时间内重复发送
    setTimeout(() => {
      allowRequest(requestList, error.config.url);
    }, 1000)
  }
  NProgress.done()
  return Promise.reject(new Error(error))
})

/**
 * 阻止重复请求
 * @param {array} reqList - 请求缓存列表
 * @param {string} url - 当前请求地址
 * @param {function} cancel - 请求中断函数
 * @param {string} errorMessage - 请求中断时需要显示的错误信息
 */
const stopRepeatRequest = function (reqList, url, cancel, errorMessage) {
  const errorMsg = errorMessage || ''
  for (let i = 0; i < reqList.length; i++) {
    if (reqList[i] === url) {
      cancel(errorMsg)
      return
    }
  }
  reqList.push(url)
}

/**
 * 允许某个请求可以继续进行
 * @param {array} reqList 全部请求列表
 * @param {string} url 请求地址
 */
const allowRequest = function (reqList, url) {
  for (let i = 0; i < reqList.length; i++) {
    if (reqList[i] === url) {
      reqList.splice(i, 1)
      break
    }
  }
}

export default axios
