import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/mall/pointsconfig/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/mall/pointsconfig',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/mall/pointsconfig/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/mall/pointsconfig/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/mall/pointsconfig',
        method: 'put',
        data: obj
    })
}

export function getObj2() {
    return request({
        url: '/mall/pointsconfig',
        method: 'get'
    })
}
