
.div-page-index{
    // position: relative;
    width: 100%;
    overflow-y: auto;

    .content{
        .left-class{
          border: 1px solid #d7dae2;
          padding: 20px;
          border-radius: 4px;
          min-width: 240px;
        }
        .focus-class {
          border: 2px dashed #409EFF!important;
        }
        height: 100%;
        .showContent{
            position: relative;
            margin-left: 10px;
            background-color: #FFFFFF;
            .pageContent{
                position: relative;
                margin: 0px 0 0 20px;
                padding: 1px 0 0 0 ;
                width: 375px;
                box-shadow:1px 2px 20px 2px rgba(0,0,0,0.1);
                border: #e1e1e1 solid 1px;
                background: white;
                .pageTopBlock{
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 51px;
                    position: absolute;
                    .pageTopImg{
                        width: 100%;
                        height: 49px;
                    }
                    p{
                        position: absolute;
                        left: 0;
                        bottom: 15px;
                        width: 100%;
                        text-align: center;
                    }
                }
                .componentsList{
                    margin-top: 50px;
                    height: 680px;
                    background: #f1f1f1;
                    &::-webkit-scrollbar {display:none};
                }
                .drag-item {
                    // padding: 10px;
                    // width: 355px;
                    margin: auto;
                    position: relative;
                    background: #ffffff;
                    &:hover{
                        cursor: move;
                      border: 1px dashed #409EFF;
                    }
                }
                .modal{
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0,0,0,.6);
                }
            }
          .funBlock{
            position: absolute;
            right: 0;
            top: 0;
            padding: 10px 5px;
            width: 30px;
            font-size: 20px;
            text-align: center;
            background: white;
            border-radius: 5px;
            box-shadow: 0 0 5px #a9a9a9;
            z-index: 120;
            .icon{
              &+.icon{
                margin-top: 10px;
              }
              &:hover{
                color: #409EFF;
                cursor: pointer;
              }
            }
          }
        }
        .btns{
            height: calc(~"100% - 10px");
            width: 300px;
            text-align: left;
            background: white;
            /deep/ .el-button{
                margin: 10px;
            }
            .funBtnItem{
                display: inline-block;
                padding: 20px 10px;
                width: calc(~"50% - 44px");
                text-align: center;
                cursor: pointer;
                .icon{
                    font-size: 20px;
                }
            }
        }
    }
    .settingBlock{
        position: absolute;
        background: #fff;
        top: 1px;
        z-index: 100;
        .pseudoRow{
            position: absolute;
            content: "";
            left: -5px;
            border: 5px solid #fff;
            transform: rotateZ(45deg);
        }
    }
}
