<!--商品分类标签-->
<template>
  <div class="pageSetting">
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">团主控件</p>
      <div slot="mainContent">
        <el-divider>基础属性</el-divider>
        <el-form ref="form" label-width="100px" :model="formData">
          <el-form-item label="页面上距">
            <el-input v-model="formData.pageMarginTop" size="mini" type="number" style="margin-top: 5px" placeholder="与上面元素的间隔">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="页面下距">
            <el-input v-model="formData.pageMarginBottom" size="mini" type="number" style="margin-top: 5px" placeholder="与下面元素的间隔">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="是否开启支付">
            <el-switch
              v-model="formData.enablePayment"
              active-value="1"
              inactive-value="0"
              active-text="开启"
              inactive-text="关闭">
            </el-switch>
          </el-form-item>
          <el-divider>状态设置</el-divider>
          <el-tabs v-model="formData.tabActiveName"  @tab-click="handleClick">
            <el-tab-pane label="无团主进入" name="1">
              <el-form-item label="是否显示">
                <el-switch
                  v-model="formData.startShowFlag"
                  active-value="0"
                  inactive-value="1"
                  active-text="显示"
                  inactive-text="非必填">
                </el-switch>
              </el-form-item>
              <el-form-item label="描述内容">
                <el-input v-model="formData.startDescription" size="mini" style="margin-top: 5px" placeholder="标题文字">
                </el-input>
              </el-form-item>
              <el-form-item label="描述颜色">
                <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
                  <el-input v-model="formData.startDescriptionColor" size="small" style="margin-top: 5px">
                    <template slot="append">
                      <el-color-picker size="mini" v-model="formData.startDescriptionColor"></el-color-picker>
                    </template>
                  </el-input>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="按钮内容">
                <el-input v-model="formData.startButtonTitle" size="mini" style="margin-top: 5px" placeholder="文字大小">
                </el-input>
              </el-form-item>
              <el-form-item label="按钮颜色">
                <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
                  <el-input v-model="formData.startButtonBackGround" size="small" style="margin-top: 5px">
                    <template slot="append">
                      <el-color-picker size="mini" v-model="formData.startButtonBackGround"></el-color-picker>
                    </template>
                  </el-input>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="字体颜色">
                <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
                  <el-input v-model="formData.startButtonColor" size="small" style="margin-top: 5px">
                    <template slot="append">
                      <el-color-picker size="mini" v-model="formData.startButtonColor"></el-color-picker>
                    </template>
                  </el-input>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="按钮字体">
                <el-input v-model="formData.startButtonFont" size="mini" type="number" style="margin-top: 5px" placeholder="按钮字体大小"></el-input>
              </el-form-item>

            </el-tab-pane>
            <el-tab-pane label="有团主进入" name="2">
              <el-form-item label="是否显示">
                <el-switch
                  v-model="formData.proceedShowFlag"
                  active-value="0"
                  inactive-value="1"
                  active-text="显示"
                  inactive-text="非必填">
                </el-switch>
              </el-form-item>
              <el-form-item label="描述内容">
                <el-input v-model="formData.proceedDescription" size="mini" style="margin-top: 5px" placeholder="标题文字">
                </el-input>
              </el-form-item>
              <el-form-item label="描述颜色">
                <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
                  <el-input v-model="formData.proceedDescriptionColor" size="small" style="margin-top: 5px">
                    <template slot="append">
                      <el-color-picker size="mini" v-model="formData.proceedDescriptionColor"></el-color-picker>
                    </template>
                  </el-input>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="按钮内容">
                <el-input v-model="formData.proceedButtonTitle" size="mini" style="margin-top: 5px" placeholder="文字大小">
                </el-input>
              </el-form-item>
              <el-form-item label="按钮颜色">
                <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
                  <el-input v-model="formData.proceedButtonBackGround" size="small" style="margin-top: 5px">
                    <template slot="append">
                      <el-color-picker size="mini" v-model="formData.proceedButtonBackGround"></el-color-picker>
                    </template>
                  </el-input>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="字体颜色">
                <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
                  <el-input v-model="formData.proceedButtonColor" size="small" style="margin-top: 5px">
                    <template slot="append">
                      <el-color-picker size="mini" v-model="formData.proceedButtonColor"></el-color-picker>
                    </template>
                  </el-input>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="按钮字体">
                <el-input v-model="formData.proceedButtonFont" size="mini" type="number" style="margin-top: 5px" placeholder="按钮字体大小"></el-input>
              </el-form-item>

            </el-tab-pane>
            <el-tab-pane label="满团员进入" name="3">
              <el-form-item label="是否显示">
                <el-switch
                  v-model="formData.endShowFlag"
                  active-value="0"
                  inactive-value="1"
                  active-text="显示"
                  inactive-text="非必填">
                </el-switch>
              </el-form-item>
              <el-form-item label="描述内容">
                <el-input v-model="formData.endDescription" size="mini" style="margin-top: 5px" placeholder="标题文字">
                </el-input>
              </el-form-item>
              <el-form-item label="描述颜色">
                <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
                  <el-input v-model="formData.endDescriptionColor" size="small" style="margin-top: 5px">
                    <template slot="append">
                      <el-color-picker size="mini" v-model="formData.endDescriptionColor"></el-color-picker>
                    </template>
                  </el-input>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="按钮内容">
                <el-input v-model="formData.endButtonTitle" size="mini" style="margin-top: 5px" placeholder="文字大小">
                </el-input>
              </el-form-item>
              <el-form-item label="按钮颜色">
                <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
                  <el-input v-model="formData.endButtonBackGround" size="small" style="margin-top: 5px">
                    <template slot="append">
                      <el-color-picker size="mini" v-model="formData.endButtonBackGround"></el-color-picker>
                    </template>
                  </el-input>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="字体颜色">
                <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
                  <el-input v-model="formData.endButtonColor" size="small" style="margin-top: 5px">
                    <template slot="append">
                      <el-color-picker size="mini" v-model="formData.endButtonColor"></el-color-picker>
                    </template>
                  </el-input>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="按钮字体">
                <el-input v-model="formData.endButtonFont" size="mini" type="number" style="margin-top: 5px" placeholder="按钮字体大小"></el-input>
              </el-form-item>

            </el-tab-pane>
          </el-tabs>



        </el-form>
      </div>
    </settingSlot>
<!--    <p style="display:none">{{getData}}</p>-->
  </div>
</template>

<script>
  import { mapState , mapGetters , mapMutations , mapActions } from 'vuex';

  import settingSlot from '../settingSlot'
  // import bgColorSelect from "../../pages/page-components/bgColorSelect";

  export default {
    components: { settingSlot },
    data() {
      return {
        formDataCopy : {
          tabActiveName: "1",
          pageMarginTop: 0,
          pageMarginBottom: 0,
          enablePayment: '0', // 是否开启支付，默认关闭
          startShowFlag: '0',//是否显示
          startDescription: "这是一段描述",//描述
          startDescriptionColor: '#000000',//描述颜色
          startButtonTitle: "点击购买！组建你的团队吧",//按钮内容
          startButtonColor: "#FFFFFF",//按钮颜色
          startButtonFont: 14,//按钮字体
          startButtonBackGround: "#f53f3f",//按钮背景
          proceedShowFlag: '0',//是否显示
          proceedDescription: "这是一段描述",//描述
          proceedDescriptionColor: '#000000',//描述颜色
          proceedButtonTitle: "点击购买！加入Ta的拼团吧",//按钮内容
          proceedButtonColor: "#FFFFFF",//按钮颜色
          proceedButtonFont: 14,//按钮字体
          proceedButtonBackGround: "#f53f3f",//按钮背景
          endShowFlag: '0',//是否显示
          endDescription: "本团已满，请创建新的报名链接",//描述
          endDescriptionColor: '#000000',//描述颜色
          endButtonTitle: "点击创建新的报名链接！",//按钮内容
          endButtonColor: "#FFFFFF",//按钮颜色
          endButtonFont: 14,//按钮字体
          endButtonBackGround: "#409EFF",//按钮背景
        },
        formData : {}
      };
    },
    props: {
      thememobile : { type: Object | Array },
      showData:{
        type: Object,
        default: ()=> {}
      },
    },
    computed: {
      ...mapState({
        componentsList: state => state.divpage.componentsList,
        clickComIndex : state => state.divpage.clickComIndex,
      })
    },
    mounted(){
      let that = this;
      if(that.IsEmptyObj(that.showData)){

        that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      } else {
        that.formData = that.showData
      }
      console.log("111  ",this.formData)
      that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
      // that.updateData({
      //   componentsList: that.componentsList
      // })
    },
    methods: {
      ...mapMutations([
        'updateData'
      ]),
      // 删除按钮
      delBtn(index){
        let that = this;
        that.$confirm('是否删除该按钮?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText : '取消',
          type : 'warning'
        }).then(()=>{
          that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
          that.updateData({ componentsList: that.componentsList });
        }).catch(()=>{})
      },
      cancel(){
        this.$emit('cancel')
      },
      reset(){
        let that = this;
        that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
        that.componentsList[that.clickComIndex]['data'] = this.formData;
      },
      confirm(){
        this.$emit('confirm', this.formData)
      },
      delete(){
        this.$emit('delete')
      },
      handleClick(tab, event) {
        this.formData.tabActiveName = tab.name;
      }
    },

    watch: {
      showData: {
        handler(newVal, oldVal) {
          this.formData = newVal ? newVal : this.formData;
        },
        deep: true
      },
      thememobile(){},
    }
  };
</script>
<style lang='less' scoped>

  .el-form-item{
    margin-bottom: 0;
  }
</style>
