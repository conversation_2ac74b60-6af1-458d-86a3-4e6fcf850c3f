import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/wxgoodstype/page',
    method: 'get',
    params: query
  })
}
export function getList() {
  return request({
    url: '/weixin/wxgoodstype/list',
    method: 'get',
  })
}

export function addObj(obj) {
  return request({
    url: '/weixin/wxgoodstype',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/weixin/wxgoodstype/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/wxgoodstype/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/wxgoodstype',
    method: 'put',
    data: obj
  })
}
