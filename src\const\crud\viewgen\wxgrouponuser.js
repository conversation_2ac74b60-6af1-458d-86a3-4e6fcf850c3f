export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  columnBtn: false,//列的显隐按钮
  searchShow: false,
  excelBtn: true,
  printBtn: false,
  viewBtn: false,
  searchMenuSpan: 6,
  menu: false,
  tree: true,
  index: true,
  column: [
    {
      label: 'id',
      prop: 'id',
      hide: true, // 隐藏ID字段
    },
    {
      label: '拼团状态',
      prop: 'status',
    },
    {
      label: '是否团长',
      prop: 'isLeader',
    },
    // {
    //   label: '参团组ID',
    //   prop: 'groupId',
    //   sortable: true,
    // },
    {
      label: '昵称',
      prop: 'nickName',
    },
    {
      label: '电话号码',
      prop: 'phone',
    },
    {
      label: 'openId',
      prop: 'openId',
      hide: true, // 隐藏openId字段
    },
    {
      label: '参团时间',
      prop: 'createTime',
      sortable: true,
    },
    {
      label: '成团人数',
      prop: 'grouponNum',
    },
    // {
    //   label: '头像',
    //   prop: 'headimgUrl',
    //   sortable: true,
    //   rules: [
    //     {
    //       max: 1000,
    //       message: '长度在不能超过1000个字符'
    //     },
    //   ]
    // },
    {
      label: '订单Id',
      prop: 'orderId',
      hide: true, // 隐藏订单Id字段
    },
    {
      label: '订单状态',
      prop: 'orderStatusDesc', // 订单状态描述
      slot: true, // 使用自定义插槽渲染状态样式
    },
    {
      label: '退款状态',
      prop: 'refundStatus', // 显示退款状态
      slot: true, // 使用自定义插槽渲染
    },
    {
      label: '购买详情',
      prop: 'purchaseDetail', // 显示购买详情
      slot: true, // 使用自定义插槽渲染
    },
    {
      label: '是否真实',
      prop: 'trueFlag',
      // dicData:[{ label: '是', value: '0' },
      //   { label: '否', value: '1' }
      // ],
      // filters:true,
      // filterMethod:function(value, row, column) {
      //   return row.trueFlag === value;
      // }
    },
  ]
};
