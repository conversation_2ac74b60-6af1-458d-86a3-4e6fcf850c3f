<!--商品分类标签-->
<template>
  <div class="cuttingLineSetting">
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">作品选档</p>
      <div slot="mainContent">
        <el-form ref="form" label-width="100px" :model="formData">
          <el-divider>基础属性</el-divider>
          <el-form-item label="组件上边距">
            <el-input v-model="formData.paddingTop" size="mini" type="number" style="margin-top: 5px"
                      placeholder="页面下边距">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="组件下边距">
            <el-input v-model="formData.paddingBottom" size="mini" type="number" style="margin-top: 5px"
                      placeholder="页面下边距">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
<!--          <el-form-item label="背景颜色">-->
<!--            <bg-color-select :thememobile="thememobile" :bgValue="formData.background"-->
<!--                             @onChange="formData.background = $event"></bg-color-select>-->
<!--          </el-form-item>-->
          <el-divider>档期设置</el-divider>
          <el-form-item label="已禁用显示">
            <el-input v-model="formData.disableText" size="mini" placeholder="禁用显示文本"></el-input>
          </el-form-item>
          <el-form-item label="未禁用显示">
            <el-input v-model="formData.unDisableText" size="mini" placeholder="未禁用显示文本"></el-input>
          </el-form-item>
          <el-form-item label="日期选中颜色">
            <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
              <el-input v-model="formData.selectedTabColor" size="small" style="margin-top: 5px">
                <template slot="append">
                  <el-color-picker size="mini" v-model="formData.selectedTabColor"></el-color-picker>
                </template>
              </el-input>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="时间选中颜色">
            <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
              <el-input v-model="formData.selectedItemColor" size="small" style="margin-top: 5px">
                <template slot="append">
                  <el-color-picker size="mini" v-model="formData.selectedItemColor"></el-color-picker>
                </template>
              </el-input>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="预约日期范围">
            <el-date-picker
              value-format="yyyy-MM-dd"
              type="daterange"
              size="mini"
              v-model="formData.dateRange"
              :picker-options="dateRangeOptions">
            </el-date-picker>
            <!--            <el-button type="primary" size="mini" icon="el-icon-edit" @click="openCalendar" circle></el-button>-->
          </el-form-item>
          <el-form-item label="剔除可选日期">
            <el-button type="primary" size="mini" @click="unableDateVisible = true">添加</el-button>
            <div v-for="(item,index) in  formData.unableDateList" :key="'unableDate'+index">
              <div style="display: inline-block;width: 100px;border: 1px solid #dddfe5;text-align: center;"
                   v-if="item.type == 1">{{ item.rule == 1 ? "法定节假日" : "周末" }}
              </div>
              <el-date-picker
                v-if="item.type == 2"
                :disabled="true"
                size="mini"
                type="daterange"
                value-format="yyyy-MM-dd"
                :value="item.date">
              </el-date-picker>
              <el-button type="danger" size="mini" @click="delUnableDate(index)">删除</el-button>
            </div>
          </el-form-item>
          <el-form-item label="预约时间范围">
            <div>
              上午
              <el-time-picker
                size="mini"
                is-range
                value-format="HH:mm:ss"
                :picker-options="{
                   selectableRange: '00:00:00 - 12:00:00'
                }"
                v-model="formData.timeRangeAm"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                @change="confirmTime"
                placeholder="选择时间范围">
              </el-time-picker>
            </div>
            <div>
              下午
              <el-time-picker
                size="mini"
                is-range
                :minTime="'12:00:00'"
                value-format="HH:mm:ss"
                v-model="formData.timeRangePm"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                @change="confirmTime"
                placeholder="选择时间范围">
              </el-time-picker>
            </div>
          </el-form-item>
          <el-form-item label="剔除可选时间">
            <el-button type="primary" size="mini" @click="unableTimeVisible = true">添加</el-button>
            <div v-for="(item,index) in  formData.unableTimeList" :key="'unableTime'+index">
              <el-time-picker
                :disabled="true"
                size="mini"
                is-range
                value-format="HH:mm:ss"
                :value="item">
              </el-time-picker>
              <el-button type="danger" size="mini" @click="delUnableTime(index)">删除</el-button>
            </div>
          </el-form-item>
          <el-form-item label="每段时间长度">
                <el-input-number v-model="formData.hourInterval" :min="0" :max="23" size="small"
                                 style="margin-top: 5px">
                </el-input-number>
                小时
              <el-input-number v-model="formData.minuteInterval" :min="0" :max="60" :step="5" size="small"
                                 style="margin-top: 5px">
              </el-input-number>
                分钟
          </el-form-item>
        </el-form>


        <!-- 日历弹出框 roomBoxVisible-->
        <el-dialog
          :append-to-body="true"
          title="档期选择"
          :visible.sync="calendarVisible"
          width="60%"
          center>
          <auction-calendar></auction-calendar>
        </el-dialog>
        <!-- 剔除可选日期 unableDateVisible-->
        <el-dialog
          :append-to-body="true"
          title="剔除可选日期"
          :visible.sync="unableDateVisible"
          @close="unableDateForm ={};"
          center>
          <div>
            <el-form :model="unableDateForm" label-width="100px"
                     class="demo-ruleForm">
              <el-form-item label="剔除类型">
                <el-radio-group v-model="unableDateForm.type">
                  <el-radio :label="1">特定日期</el-radio>
                  <el-radio :label="2">具体日期</el-radio>
                </el-radio-group>
              </el-form-item>
              <div v-if="unableDateForm.type==1">
                <el-form-item label="特定类型">
                  <el-radio-group v-model="unableDateForm.rule">
<!--                    <el-radio :label="1">法定节假日</el-radio>-->
                    <el-radio :label="2">周末</el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
              <el-form-item v-if="unableDateForm.type==2" label="具体日期" prop="region">
                <el-date-picker
                  size="mini"
                  type="daterange"
                  value-format="yyyy-MM-dd"
                  v-model="unableDateForm.date"
                  range-separator="至"
                  :picker-options="unableDateRangeOptions"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  placeholder="选择日期范围">
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="addUnableDate">确认</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-dialog>
        <!-- 剔除可选时间 unableTimeVisible-->
        <el-dialog
          :append-to-body="true"
          title="剔除可选时间"
          :visible.sync="unableTimeVisible"
          @close="unableTimeForm ={}"
          center>
          <div>
            <el-form :model="unableTimeForm" label-width="100px"
                     class="demo-ruleForm">
              <el-form-item label="具体时间">
                <el-time-picker
                  size="mini"
                  is-range
                  value-format="HH:mm:ss"
                  v-model="unableTimeForm.time"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  placeholder="选择时间范围">
                </el-time-picker>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="addUnableTime()">确认</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-dialog>
      </div>
    </settingSlot>
  </div>

</template>
<script>

import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';
import settingSlot from '../settingSlot';
import bgColorSelect from "../../pages/page-components/bgColorSelect";
import auctionCalendar from '@/views/viewgen/auctioncalendar/index'
import {dateFormat} from "@/util/date";

export default {
  components: {settingSlot, bgColorSelect, auctionCalendar},
  data() {
    return {
      unableDateForm: {},
      unableTimeForm: {},
      calendarVisible: false,
      unableTimeVisible: false,
      unableDateVisible: false,
      dateFlag: false,
      dateRange: [],
      dateRangeOptions: {
        disabledDate(time) {
          return time.getTime() < (Date.now() - (24 * 60 * 60 * 1000))
        },
      },
      //选择剔除日期参数
      unableDateRangeOptions: {
        disabledDate(time) {
          return time.getTime() < (Date.now() - (24 * 60 * 60 * 1000))
        },
      },
      formDataCopy: {
        paddingTop: 10,
        paddingBottom: 10,
        // background: "#E88181",
        isSection: false,//是否预约时间段
        isMultiple: false,//是否多选
        disableText: '已约满', //禁用显示的文本
        unDisableText: '可以约', //未禁用显示的文本
        hourInterval: 1, //时间间隔，小时为单位
        minuteInterval: 0, //时间间隔，分钟为单位
        selectedTabColor: '#27bb25',// 日期栏选中的颜色
        selectedItemColor: '#27bb25',// 时间选中的颜色
        dateRange: '',//日期范围
        beginTime: '08:00:00',//开始时间
        endTime: '18:00:00',//结束时间
        appointTime: '',
        timeRangeAm: [new Date(2016, 8, 10, 0, 0), new Date(2016, 9, 10, 12, 0)],
        timeRangePm: [new Date(2016, 8, 10, 13, 0), new Date(2016, 9, 10, 18, 0)],
        shopList: [],//场地选择
        unableDateList: [],//剔除日期list
        unableTimeList: [],//剔除时间list
        disableTimeSlot: {
          begin_time: '17:00:00',
          end_time: '18:00:00'
        },
      },
      formData: {}
    };
  },
  props: {
    thememobile: {type: Object | Array},
    showData: {
      type: Object,
      default: () => {
      }
    },
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
      clickComIndex: state => state.divpage.clickComIndex,
    })
  },
  mounted() {
    let that = this;
    if (that.IsEmptyObj(that.showData)) {
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
    } else {
      that.formData = that.showData
    }
    that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
    // that.updateData({
    //   componentsList: that.componentsList
    // })
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    // 删除按钮
    delBtn(index) {
      let that = this;
      that.$confirm('是否删除该按钮?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
        that.updateData({componentsList: that.componentsList});
      }).catch(() => {
      })
    },
    cancel() {
      this.$emit('cancel')
    },
    reset() {
      let that = this;
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      that.componentsList[that.clickComIndex]['data'] = this.formData;
    },
    confirm() {
      this.$emit('confirm', this.formData)
    },
    delete() {
      this.$emit('delete')
    },
    openCalendar() {
      this.calendarVisible = true
    },
    confirmTime(e) {
      console.log(e)
      this.formData.beginTime = this.timeRange[0];
      this.formData.endTime = this.timeRange[1];
    },
    addUnableDate() {
      if (!this.unableDateForm.type) {
        this.$message.warning("请选择剔除  类型");
        return
      }
      if (this.unableDateForm.type == 1 && (!this.unableDateForm.rule)) {
        this.$message.warning("请选择特定类型");
        return
      } else if (this.unableDateForm.type == 2 && (!this.unableDateForm.date)) {
        this.$message.warning("请填具体日期");
        return
      }
      for (let i = 0; i < this.formData.unableDateList.length; i++) {
        if (this.unableDateForm.type == 1 && this.unableDateForm.type == this.formData.unableDateList[i].type && this.unableDateForm.rule == this.formData.unableDateList[i].rule) {
          this.$message.warning("请不要添加重复类型");
          return;
        }else if (this.unableDateForm.type == 2 ) {
          for (let j = 0; j < this.formData.unableDateList.length; j++) {
            let o = this.formData.unableDateList[j];
            if(o.type==2 &&
              (
                (  new Date(this.unableDateForm.date[0]) > new Date(o.date[0]).getTime() && new Date(this.unableDateForm.date[0])< new Date(o.date[1]).getTime())
                  ||
                (  new Date(this.unableDateForm.date[1]) > new Date(o.date[0]).getTime() && new Date(this.unableDateForm.date[1])< new Date(o.date[1]).getTime())
              )
            ){
              this.$message.warning("请不要添加重复日期");
              return false;
            }
          }
        }
      }

      this.unableDateVisible = false;
      let obj = {
        type: this.unableDateForm.type,
        rule: this.unableDateForm.rule,
        date: this.unableDateForm.date,
      }
      this.formData.unableDateList.push(obj)
    },
    delUnableDate(index) {
      this.formData.unableDateList.splice(index, 1);
    },
    getUnableDateRangeOptions(time) {
      return time.getTime() < (Date.now() - (24 * 60 * 60 * 1000))
    },
    addUnableTime() {
      if (!this.unableTimeForm.time) {
        this.$message.warning("请完善具体时间");
        return
      }
      let date = dateFormat(new Date()).split(" ")[0];
      for (let j = 0; j < this.formData.unableTimeList.length; j++) {
        let o = this.formData.unableTimeList[j];
        if(
          (  new Date(date+" "+ this.unableTimeForm.time[0]) > new Date(date+" "+ o[0]).getTime() && new Date(date+" "+ this.unableTimeForm.time[0])< new Date(date+" "+ o[1]).getTime())
          ||
          (  new Date(date+" "+ this.unableTimeForm.time[1]) > new Date(date+" "+ o[0]).getTime() && new Date(date+" "+ this.unableTimeForm.time[1])< new Date(date+" "+ o[1]).getTime())
        ){
          this.$message.warning("请不要添加重复时间");
          return false;
        }
      }

      this.formData.unableTimeList.push(this.unableTimeForm.time);
      this.unableTimeVisible = false;
    },
    delUnableTime(index) {
      this.formData.unableTimeList.splice(index, 1);
    },
  },
  watch: {
    showData: {
      handler(newVal, oldVal) {
        this.formData = newVal ? newVal : this.formData;
      },
      deep: true
    },
    thememobile() {
    },
  }
};
</script>
<style lang='less' scoped>

.el-form-item {
  margin-bottom: 0;
}

</style>
