import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/weixin/wxmalive/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/weixin/wxmalive',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/weixin/wxmalive/' + id,
        method: 'get'
    })
}

export function delObj(appId,id) {
    return request({
        url: '/weixin/wxmalive/' +appId + '/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/weixin/wxmalive',
        method: 'put',
        data: obj
    })
}

export function getSharedCode(appId,id) {
  return request({
    url: '/weixin/wxmalive/sharedcode/' +appId + '/' + id,
    method: 'get'
  })
}

export function getPushUrl(appId,id) {
  return request({
    url: '/weixin/wxmalive/pushurl/' +appId + '/' + id,
    method: 'get'
  })
}

export function addGoods(obj) {
  return request({
    url: '/weixin/wxmalive/'+obj.appId+'/'+obj.roomId,
    method: 'post',
    data: obj.goodsIds
  })
}

export function deleteGoods(obj) {
  return request({
    url: '/weixin/wxmalive/'+obj.appId+'/'+obj.roomId+'/'+obj.goodsId,
    method: 'delete',
    data: obj
  })
}
