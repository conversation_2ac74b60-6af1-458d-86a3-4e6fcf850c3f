import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/wxuserfriend/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/weixin/wxuserfriend',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/weixin/wxuserfriend/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/wxuserfriend/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/wxuserfriend',
    method: 'put',
    data: obj
  })
}
