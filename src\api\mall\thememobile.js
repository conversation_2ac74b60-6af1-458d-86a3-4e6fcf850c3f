import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/mall/thememobile/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/mall/thememobile',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/mall/thememobile/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/mall/thememobile/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/mall/thememobile',
        method: 'put',
        data: obj
    })
}

export function getObj2() {
  return request({
    url: '/mall/thememobile',
    method: 'get'
  })
}
