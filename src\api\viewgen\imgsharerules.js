import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/imgsharerules/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/weixin/imgsharerules',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/weixin/imgsharerules/' + id,
    method: 'get'
  })
}
//pageId
export function getObjByPage(id) {
  return request({
    url: '/weixin/imgsharerules/one/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/imgsharerules/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/imgsharerules',
    method: 'put',
    data: obj
  })
}
