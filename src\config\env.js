// 配置编译环境和线上环境之间的切换
const env = process.env
// 阿里图表库avue、gocreateone
let iconfontVersion = ['567566_qo5lxgtishg','1116466_f0ovtx5y88q']
let iconfontUrl = `//at.alicdn.com/t/font_$key.css`
// 腾讯地图KEY
let qqMapKey = 'PFFBZ-RBM3V-IEEPP-UH6KE-6QUQE-C4BVJ'
//前端密码密钥，必须16位，和nacos配置文件base-gateway-dev.yml中的security.encode.key对应
let securityKey = '3D4239E21C0513B0'
//后台第三方微信登录appId，微信开放平台申请网站应用：https://open.weixin.qq.com/
let wxAppId = 'wxcc1c02ddaa34e6b1'
//后台第三方QQ登录ClientId，QQ互联申请应用: https://connect.qq.com/
let qqClientId = '101885077'
//移动端（gocreateone-plus-uniapp）商城H5域名，用于：1、公众号自定义菜单中商城H5域名；2、店铺二维码地址
let h5HostMobile = 'https://show.gocreateone.com'
// let h5HostMobile = 'https://shop.gongjunqi.com'
//PC端（gocreateone-plus-uniapp-pc）商城H5域名，用于：1、店铺首页PC端访问地址
let h5HostPC = 'http://demo1-pc.gocreateone.com'
//商城H5域名，用于：1、公众号自定义菜单中商城H5域名；2、店铺二维码地址
const domainName = 'http://mall.gongjunqi.com'
//小程序直播插件配置
const maLivePluginVersion = '1.2.10'
const maLivePluginProvider = 'wx2b03c6e691cd7370'
// if (env.NODE_ENV == 'development') {
//   h5HostMobile = 'http://localhost:8086'
// } else if (env.NODE_ENV == 'production') {
//   h5HostMobile = 'https://show.gocreateone.com'
// } else if (env.NODE_ENV == 'test') {
//
// }
export {
  qqMapKey,
  iconfontUrl,
  iconfontVersion,
  env,
  securityKey,
  wxAppId,
  qqClientId,
  h5HostMobile,
  h5HostPC,
  maLivePluginVersion,
  maLivePluginProvider
}
