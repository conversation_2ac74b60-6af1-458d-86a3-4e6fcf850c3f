<!-- 商品分类组件 -->
<template>
  <div class="cuttingLineComponent">
    <el-alert
      title="组件提示:"
      type="success"
      description="此组件用于页面背景音乐，请在实际页面查看"
      :closable="false">
    </el-alert>
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';

export default {
  data() {
    return {

    };
  },
  components: {},
  props: {
    thememobile: {type: Object | Array},
    setData: {type: Object | Array},
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {

  },
  mounted() {
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
  },
  watch: {
    componentsList(newVal, oldVal) {          //添加的时候触发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    }
  }
};
</script>
<style lang='less' scoped>

@import '../../colorui/main.css';
@import '../../colorui/icon.css';

.cuttingLineComponent {
  display: flex;
  justify-content: center;

}
</style>
