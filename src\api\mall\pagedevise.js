import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/mall/pagedevise/page',
        method: 'get',
        params: query
    })
}
// export function getPageByShop(shopId) {
//     return request({
//         url: '/mallapi/pagedevise?pageType=2&shopId=' + shopId,
//         method: 'get'
//     })
// }
export function getPageByShop(query) {
  return request({
    url: '/mall/pagedevise/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
    return request({
        url: '/mall/pagedevise',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/mall/pagedevise/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/mall/pagedevise/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/mall/pagedevise',
        method: 'put',
        data: obj
    })
}
