<template>
    <div class="goodsCoverComponent"  :style="{paddingBottom: `${setData.pagePaddingBottom}px`,paddingTop: `${setData.pagePaddingTop}px`,paddingLeft: `${setData.pagePaddingLeft}px`,paddingRight: `${setData.pagePaddingRight}px`}">
      <div v-for="col in setData.rowNumber" :key="col">
        <div  class="imgBlock" style="text-align: center;" >
          <div v-for="(row,index) in setData.colNumber" :key="'row'+index" style="display: inline-block;" :style="{width:`${(1/setData.colNumber)*100}%`}">
            <el-image src="https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg" style="height: auto;"  :style="{'aspect-ratio':`${getImgShowSize(setData.imgShowSize)}`,padding: `${setData.imgPadding}px`,width: `${setData.width}%` }">
              <div slot="placeholder" class="image-slot">
                加载中 <span class="dot">...</span>
              </div>
            </el-image>
            <h4 v-if="setData.nameFlag">《小小麋鹿》</h4>
<!--            <p v-if="setData.tagFlag=='0'">动物系列</p>-->
          </div>
        </div>
      </div>
      <el-pagination
        v-if="setData.loadingType==1"
        :disabled="true"
        layout="prev, pager, next"
        :total="1000">
      </el-pagination>
    </div>
</template>

<script>
import { mapState , mapGetters , mapMutations , mapActions } from 'vuex';
import placeholderImg from "../pages/page-components/placeholderImg";

export default {
    data() {
        return {};
    },
    components: { placeholderImg },
    props: {
        theme : { type: Object | Array },
        setData : { type: Object | Array },
        cId     : { type: Number },
        noEditor: {
            type: Boolean,
            default: false,
        }
    },
    computed: {
        ...mapState({
            componentsList: state => state.divpage.componentsList,
        }),
    },
    created() {
    },
    mounted() {
    },
    methods: {
        ...mapMutations([
            'updateData'
        ]),
        getImgShowSize(val){
            if(val ==1){
                return "1/1"
            }else if(val ==2){
              return "2/3"
            }else if(val ==3){
              return "3/2"
            }else if(val ==4){
              return "16/9"
            }
        }
    },
    watch:{
        setData(newVal, oldVal){},
        componentsList(newVal, oldVal){          //添加的时候出发（解决没有移动的时候不emit数据）
            let that = this;
            that.updateData({componentsList: that.componentsList})
        }
    },
    beforeDestroy(){
        // this.$root.Bus.$off('addHotSpot')
    },
};
</script>
<style lang='less' scoped>
  .el-carousel__item--card{

  }
  .goodsCoverComponent {
    position: relative;
    display: block;
    width: 100%;
    background: #ffffff;
  }
</style>
