<template>
  <div class="execution">
    <basic-container>
      <avue-crud ref="crud"
                 :page="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 v-model="form"
                 @on-load="getPage"
                 @refresh-change="refreshChange"
                 @row-update="handleUpdate"
                 @row-save="handleSave"
                 @row-del="handleDel"
                 @sort-change="sortChange"
                 @search-change="searchChange">
        <template slot-scope="scope" slot="menuLeft">
          <el-button type="primary"
                     icon="el-icon-plus"
                     size="small"
                     @click.stop="addMsgFromPublic">从自选库中添加
          </el-button>
          <el-button type="success"
                     icon="el-icon-upload"
                     size="small"
                     @click.stop="openSyncBox">同步公众号模版消息
          </el-button>
        </template>
        <template slot-scope="scope" slot="menu">
          <el-button icon="el-icon-edit" type="text" size="small" @click.stop="preExample(scope.row.example)">预览
          </el-button>
          <el-button icon="el-icon-delete" type="text" size="small" @click.stop="handleDel(scope.row)">删除</el-button>
        </template>
      </avue-crud>

      <!--      同步模版消息框-->
      <el-dialog
        :visible.sync="syncBoxVisible"
        :show-close="true"
        :append-to-body="true"
        width="30%"
        center>
        <div>
          <el-form :rules="syncAppRules" :ref="syncAppRef" :model="syncAppForm" label-width="auto"
                   :destroy-on-close="true">
            <el-form-item label="公众号" prop="appId">
              <el-select v-model="syncAppForm.appId" placeholder="请选择">
                <el-option
                  v-for="item in wxAppList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="submitSync">确认</el-button>
              <el-button @click="syncBoxVisible = false ">取消</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-dialog>
      <!--      查看显示例消息框-->
      <el-dialog
        title="示例消息"
        :visible.sync="exampleBoxVisible"
        :show-close="true"
        :append-to-body="true"
        width="50%"
        center>
        <div>
          <el-input type="textarea" resize="none" :readonly="true" v-model="exampleValue" autosize></el-input>
        </div>
      </el-dialog>

      <!--      公共选择框-->
      <el-dialog
        title="模版选择"
        :visible.sync="publicMsgBoxVisible"
        :show-close="false"
        :append-to-body="true"
        width="40%"
        center>
        <div style="padding-bottom: 5px">
          需要添加的公众号： <el-select v-model="addTemplateAppId" placeholder="请选择">
          <el-option
            v-for="item in wxAppList"
            :key="item.id"
            :label="item.name"
            :value="item.id">
          </el-option>
        </el-select>
        </div>
        <div style="overflow: auto;height: 60vh">

          <div v-for="(item,index) in publicTemplate" :key="index" style="padding-bottom: 3px" >
            <el-card shadow="hover" class="template_example">
              <div class="template_title ">
                <span>{{ item.title }}</span>
                <el-button  @click="addTemplate(item)" type="success" size="mini">添加按钮</el-button>
              </div>
              <div class="example_value ">
                <el-input type="textarea" resize="none" :readonly="true" v-model="item.example" autosize></el-input>
              </div>
            </el-card>
          </div>
        </div>
      </el-dialog>
    </basic-container>
  </div>
</template>

<script>
import {getPage, syncObj, addObj, putObj, delObj, getPublic, addPublic} from '@/api/wxmp/wxmptempmsg'
import {tableOption} from '@/const/crud/wxmp/wxmptempmsg'
import {mapGetters} from 'vuex'
import {getList as getWxAppList} from "@/api/wxmp/wxapp";

export default {
  name: 'wxmptempmsg',
  data() {
    return {
      wxAppList: [],
      publicMsgBoxVisible: false,//公共选择框
      syncBoxVisible: false,//同步选择框
      exampleBoxVisible: false,//示例消息框
      exampleValue: "",//示例消息框
      syncAppForm: {},//同步公众号
      syncAppRef: {},
      syncAppRules: {
        appId: [
          {required: true, message: '请选择公众号', trigger: 'submit'}
        ],
      },
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      publicTemplate: [],
      addTemplateAppId: '',
    }
  },
  created() {
    getWxAppList({
      appType: '2'
    }).then(res => {
      let data = res.data
      this.wxAppList = data;
    });
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.permissions['weixin:wxmptempmsg:add'] ? true : false,
        delBtn: this.permissions['weixin:wxmptempmsg:del'] ? true : false,
        editBtn: this.permissions['weixin:wxmptempmsg:edit'] ? true : false,
        viewBtn: this.permissions['weixin:wxmptempmsg:get'] ? true : false
      };
    }
  },
  methods: {
    searchChange(params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    sortChange(val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage(page, params) {
      this.tableLoading = true
      getPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
      }, params, this.paramsSearch)).then(res => {
        console.log(res.data)
        this.tableData = res.data.data.records
        this.page.total = res.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this
      this.$confirm('是否确认删除此数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        return delObj(row.id)
      }).then(data => {
        _this.$message({
          showClose: true,
          message: '删除成功',
          type: 'success'
        })
        this.getPage(this.page)
      }).catch(function (err) {
      })
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      putObj(row).then(res => {
        this.$message({
          showClose: true,
          message: '修改成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      addObj(row).then(res => {
        this.$message({
          showClose: true,
          message: '添加成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page)
    },
    /**
     * addMsgFromPublic
     */
    addMsgFromPublic() {
      this.publicMsgBoxVisible = true;
      getPublic().then(res => {
        this.publicTemplate = res.data.data;
      }).catch(() => {
        this.tableLoading = false
      })
    },
    /**
     * 打开同步公众号弹出框
     */
    openSyncBox() {
      this.syncBoxVisible = true;
    },
    /**
     * 同步公众号后台消息
     */
    submitSync() {
      this.$refs[this.syncAppRef].validate((valid) => {
        if (valid) {
          syncObj(this.syncAppForm.appId).then(res => {
            this.$message({
              showClose: true,
              message: '同步成功',
              type: 'success'
            })
            this.syncBoxVisible = false;
            this.refreshChange()
          }).catch(() => {
          })
        }
      });
    },
    preExample(val) {
      this.exampleValue = val;
      this.exampleBoxVisible = true;
    },
    //添加公共的模版消息
    addTemplate(obj) {
      console.log(obj)
      if(!this.addTemplateAppId){
        this.$message.warning("请选择添加公众号");
        return
      }
      let params ={
        appId:this.addTemplateAppId,
        priTmplId:obj.priTmplId,
      }
      addPublic(params).then(res => {
        this.publicTemplate = res.data.data;
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
