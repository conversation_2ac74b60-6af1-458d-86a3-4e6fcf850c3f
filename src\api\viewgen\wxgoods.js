import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/wxgoods/pageByTagId',
    method: 'get',
    params: query
  })
}
//排序处查询分页
export function getSortPage(query) {
  return request({
    url: '/weixin/wxgoods/sortpage',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/weixin/wxgoods',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/weixin/wxgoods/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/wxgoods/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/wxgoods',
    method: 'put',
    data: obj
  })
}

/**
 * 可以修改整个
 * @param obj
 * @returns {*}
 */
export function putShelf(obj) {
  return request({
    url: '/weixin/wxgoods/obj',
    method: 'put',
    data: obj
  })
}

/**
 * 更新作品标签
 * @param obj
 * @returns {*}
 */
export function updateRemark(obj) {
  return request({
    url: '/weixin/wxgoods/remark',
    method: 'put',
    data: obj
  })
}



/**
 * 标签抽屉根据标签查询作品粉分页
 * @param query
 * @returns {*}
 */
export function getPageByTag(query) {
  return request({
    url: '/weixin/wxgoods/pagetag',
    method: 'get',
    params: query
  })
}


