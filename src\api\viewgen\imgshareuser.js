import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/imgshareuser/page',
    method: 'get',
    params: query
  })
}

export function getBindingPage(query) {
  return request({
    url: '/weixin/imgshareuser/bindingPag',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/weixin/imgshareuser',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/weixin/imgshareuser/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/imgshareuser/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/imgshareuser',
    method: 'put',
    data: obj
  })
}

/**
 * 发布客片
 * @param obj
 * @returns {*}
 */
export function handleRelease(obj) {
  return request({
    url: '/weixin/imgshareuser/release',
    method: 'put',
    data: obj
  })
}

/**
 * 验收客片
 * @param obj
 * @returns {*}
 */
export function handleCheck(obj) {
  return request({
    url: '/weixin/imgshareuser/check',
    method: 'put',
    data: obj
  })
}
