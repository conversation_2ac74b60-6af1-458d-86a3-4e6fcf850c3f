import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/wxgoodssortdetail/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/weixin/wxgoodssortdetail',
    method: 'post',
    data: obj
  })
}
//批量新增
export function addBatch(list) {
  return request({
    url: '/weixin/wxgoodssortdetail/batch',
    method: 'post',
    data: list
  })
}

export function getObj(id) {
  return request({
    url: '/weixin/wxgoodssortdetail/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/wxgoodssortdetail/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/wxgoodssortdetail',
    method: 'put',
    data: obj
  })
}
