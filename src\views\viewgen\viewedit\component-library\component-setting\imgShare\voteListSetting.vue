<template>
  <div class="rollingListSetting">
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">投票列表</p>
      <div slot="mainContent">
        <el-form ref="form" label-width="100px" :model="formData">
          <el-divider>基础属性</el-divider>
          <el-form-item label="上边界">
            <el-input v-model="formData.pageMarginTop" size="mini" type="number" style="margin-top: 5px" placeholder="距离上方组件的长度">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="下边界">
            <el-input v-model="formData.pageMarginBottom" size="mini" type="number" style="margin-top: 5px" placeholder="距离下方组件的长度">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="左边界">
            <el-input v-model="formData.pageMarginLeft" size="mini" type="number" style="margin-top: 5px" placeholder="距离左边框的长度">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="右边界">
            <el-input v-model="formData.pageMarginRight" size="mini" type="number" style="margin-top: 5px" placeholder="距离右边框的长度">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="背景颜色">
            <el-input v-model="formData.background" size="mini" style="margin-top: 5px">
              <template slot="append">
                <el-color-picker size="mini" v-model="formData.background"></el-color-picker>
              </template>
            </el-input>
          </el-form-item>
          <el-divider>内容设置</el-divider>
          <el-form-item label="上圆角设置">
            <el-slider v-model="formData.topBorderRadius" :min="0" :max="40"></el-slider>
          </el-form-item>
          <el-form-item label="下圆角设置">
            <el-slider v-model="formData.bottomBorderRadius" :min="0" :max="40"></el-slider>
          </el-form-item>
          <el-form-item  label="内容上距">
            <el-input v-model="formData.contentPaddingTop" size="mini"  :mini="0" type="number" style="margin-top: 5px" placeholder="组件内部与上边界的距离">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item  label="内容下距">
            <el-input v-model="formData.contentPaddingBottom" size="mini"  :mini="0" type="number" style="margin-top: 5px" placeholder="组件内部与上边界的距离">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item  label="内容左距">
            <el-input v-model="formData.contentPaddingLeft" size="mini"  :mini="0" type="number" style="margin-top: 5px" placeholder="">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item  label="内容右距">
            <el-input v-model="formData.contentPaddingRight" size="mini"  :mini="0" type="number" style="margin-top: 5px" placeholder="">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
    </settingSlot>
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';
import settingSlot from '../settingSlot'

export default {
  components: {settingSlot,},
  data() {
    return {
      formDataCopy: {
        pageMarginTop:0,
        pageMarginBottom:0,
        pageMarginLeft:0,
        pageMarginRight:0,
        background:'#ffffff',
        topBorderRadius:0,
        bottomBorderRadius:0,
        contentPaddingTop:0,
        contentPaddingBottom:0,
        contentPaddingLeft:0,
        contentPaddingRight:0,
      },
      formData: {},
      interiorBoxVisible: false
    };
  },
  props: {
    thememobile: {type: Object | Array},
    showData: {
      type: Object,
      default: () => {
      }
    },
  },
  watch: {
    showData: {
      handler(newVal, oldVal) {
        this.formData = newVal ? newVal : this.formData;
      },
      deep: true
    },

  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
      clickComIndex: state => state.divpage.clickComIndex,
    })
  },
  created(){
    this.pageId = window.localStorage.getItem('viewEditId');
    console.log("滚动列表",this.pageId)
  },
  mounted() {
    let that = this;
    if (that.IsEmptyObj(that.showData)) {

      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
    } else {
      that.formData = that.showData
    }

    that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
    // that.updateData({
    //   componentsList: that.componentsList
    // })
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    // 删除按钮
    cancel() {
      this.$emit('cancel')
    },
    reset() {
      let that = this;
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      that.componentsList[that.clickComIndex]['data'] = this.formData;
    },
    confirm() {
      this.$emit('confirm', this.formData)
    },
    delete() {
      this.$emit('delete')
    },
  },
};
</script>
<style lang='less' scoped>
.el-form-item {
  margin-bottom: 0;
}

</style>
