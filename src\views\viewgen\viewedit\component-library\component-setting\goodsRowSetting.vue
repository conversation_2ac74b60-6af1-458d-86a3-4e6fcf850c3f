<template>
  <div>
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">作品甑选</p>
      <!-- <div slot="hint">
        提示：建议最少3个，最多10个。
      </div> -->
      <div slot="mainContent">
        <el-form ref="form"  label-width="100px" :model="formData">
          <el-divider>基础属性</el-divider>
          <el-form-item label="标题">
            <el-input v-model="formData.title" size="mini" placeholder="标题"></el-input>
          </el-form-item>
          <el-form-item label="标题颜色">
            <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
              <el-input v-model="formData.titleColor" size="small" style="margin-top: 5px">
                <template slot="append">
                  <el-color-picker size="mini" v-model="formData.titleColor"></el-color-picker>
                </template>
              </el-input>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="标题图标">
            <icon-select style="float:left;margin-right: 10px;"  @onChangeIcon="formData.titleIcon = $event"></icon-select>
            <div :style="{color: `${formData.titleColor}`}" :class="formData.titleIcon"></div>
          </el-form-item>
          <el-form-item label="页面下边距">
            <el-input v-model="formData.pageSpacing" size="mini" type="number" style="margin-top: 5px" placeholder="页面下边距">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>

          <el-divider>跳转设置</el-divider>
          <el-form-item label="点击更多">
            <wx-page-select :isSystemUrl="formData.moreIsSystemUrl" @switchChange="formData.moreIsSystemUrl=$event" :page="formData.morePageUrl" @change="formData.morePageUrl=$event"></wx-page-select>
          </el-form-item>
          <el-form-item label="点击内容">
            <wx-page-select :isSystemUrl="formData.contentIsSystemUrl" @switchChange="formData.contentIsSystemUrl=$event" :page="formData.contentPageUrl" @change="formData.contentPageUrl=$event"> </wx-page-select>
          </el-form-item>
          <el-divider>内容显示</el-divider>
          <el-form-item label="排序规则" >
            <el-popover
              placement="top-start"
              width="450"
              trigger="click">
              <div >
                <h3>提示：</h3>
                <div>商品甄选默认加载五个</div>
                <div>默认排序为上架时间</div>
              </div>
              <el-button class="warning_msg_btn"  slot="reference" type="warning"  size="mini" icon="el-icon-question" circle></el-button>
            </el-popover>
            <el-tag v-if="formData.sortRule.id"> {{formData.sortRule.name}}</el-tag>
            <el-button  @click="deleteSort()"  v-if="formData.sortRule && formData.sortRule.id" icon="el-icon-delete"  type="danger" size="mini"></el-button>
            <el-button @click="openSortBox()"  v-if="!formData.sortRule.id" icon="el-icon-sort" size="mini"></el-button>
          </el-form-item>
          <el-form-item label="显示比例">
            <el-radio-group v-model="formData.imgShowSize">
              <el-radio :label="1">比例一</el-radio>
              <el-radio :label="2">比例二</el-radio>
              <el-radio :label="3">比例三</el-radio>
              <el-radio :label="4">比例四</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否显示名称">
            <el-switch
              v-model="formData.nameFlag"
              active-text="显示"
              inactive-text="不显示">
            </el-switch>
          </el-form-item>
<!--          <el-form-item label="内容上边距">-->
<!--            <el-input-number v-model="formData.topSpacing" size="small" controls-position="right" :min="0"-->
<!--                             :max="30"></el-input-number>-->
<!--            px-->
<!--          </el-form-item>-->
<!--          <el-form-item label="内容下边距">-->
<!--            <el-input-number v-model="formData.bottomSpacing" size="small" controls-position="right" :min="0"-->
<!--                             :max="30"></el-input-number>-->
<!--            px-->
<!--          </el-form-item>-->
<!--          <el-form-item label="内容左边距">-->
<!--            <el-input-number v-model="formData.leftSpacing" size="small" controls-position="right" :min="0"-->
<!--                             :max="30"></el-input-number>-->
<!--            px-->
<!--          </el-form-item>-->
<!--          <el-form-item label="内容右边距">-->
<!--            <el-input-number v-model="formData.rightSpacing" size="small" controls-position="right" :min="0"-->
<!--                             :max="30"></el-input-number>-->
<!--            px-->
<!--          </el-form-item>-->
<!--          <el-form-item label="内容间距" v-show="formData.rowRules==1">-->
<!--            <el-input-number v-model="formData.interval" size="small" controls-position="right" :min="0"-->
<!--                             :max="30"></el-input-number>-->
<!--            px-->
<!--          </el-form-item>-->


          <!--          <el-form-item label="展示类型">-->
<!--            <el-radio-group v-model="formData.showType">-->
<!--              <el-radio :label="0">具体商品</el-radio>-->
<!--              <el-radio :label="1">标签选择</el-radio>-->
<!--            </el-radio-group>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="商品" v-show="formData.showType==0">-->
<!--            <div >-->
<!--              <el-button class="addBtn" type="primary" icon="el-icon-circle-plus" plain size="mini" @click="dialogVisibleGoods =true">选择商品</el-button>-->
<!--            </div>-->
<!--          </el-form-item>-->
<!--          <div>-->
<!--            <draggable v-model="formData.goodsList" :options="{filter:'.notDraggable', preventOnFilter: false, animation:500}" >-->
<!--            <div v-for="(item,index) in formData.goodsList" :key="index" style="margin-top: 8px;">-->
<!--              <el-row>-->
<!--                <el-col :span="3" style="text-align: center;">-->
<!--                  <div class="draggable-focus"><i class=" el-icon-d-caret" ></i></div>-->
<!--                  <div @click="delItem(index)" class="del-focus" ><i class="delIcon el-icon-delete" ></i></div>-->
<!--                </el-col>-->
<!--                <el-col :span="21">-->
<!--                  <div class="notDraggable" style="margin: 5px 0;">-->
<!--                    <img :src="item.picUrls[0]" style="float:left;width: 70px;height: 70px" >-->
<!--                    <el-form-item label="" label-width="0px" style="margin-left:20px;padding-top: 8px;">-->
<!--                      <el-input v-model="item.name" size="mini" placeholder="名称" style="width:80%;margin-left: 10px"></el-input>-->
<!--                    </el-form-item>-->
<!--                  </div >-->
<!--                </el-col>-->
<!--              </el-row>-->
<!--            </div>-->
<!--            </draggable>-->
<!--          </div>-->
        </el-form>
      </div>
    </settingSlot>

    <el-dialog title="请选择商品" :visible.sync="dialogVisibleGoods" width="83%" top="20px"  append-to-body>
      <goods-select></goods-select>
      <span slot="footer" class="dialog-footer">
                  <el-button @click="dialogVisibleGoods = false">取 消</el-button>
                  <el-button type="primary" @click="true">确 定</el-button>
                </span>
    </el-dialog>

    <!-- 商品排序栏 goodsSortBoxVisible-->
    <el-dialog
      :append-to-body="true"
      title="排序规则"
      :visible.sync="goodsSortBoxVisible"
      width="80%"
      center>
      <goods-sort-select ref="goodsSortSelect" :appId="appId"  @confirm="sortConfirm"></goods-sort-select>
    </el-dialog>
  </div>
</template>

<script>

  import { mapState , mapGetters , mapMutations , mapActions } from 'vuex';

  import draggable from "vuedraggable";

  import settingSlot from './settingSlot'
  import iconSelect from '../pages/page-components/iconSelect.vue'

  import wxPageSelect from '@/components/wx-page-select/Index.vue'
  import goodsSelect from "@/views/viewgen/goodslist/goodsSelect";
  import goodsSortSelect from "@/views/viewgen/goodssort/selectSort";
  export default {
    components: { draggable, settingSlot,iconSelect,goodsSelect,wxPageSelect,goodsSortSelect},
    data() {
      return {
        dialogVisibleGoods: false,
        goodsSortBoxVisible: false,
        formDataCopy : {
          title: '作品甄选',
          titleColor: 'red',
          titleIcon: 'cuIcon-message',
          pageSpacing: 0,
          moreIsSystemUrl:false,
          morePageUrl:'',
          sortRule:{id:''},
          contentIsSystemUrl:false,
          contentPageUrl:'',
          imgShowSize:1,
          nameFlag:true,
        },
        formData : {}
      };
    },
    props: {
      showData:{
        type: Object,
        default: ()=> {}
      },
      config: {
        type: Object,
        default: ()=> {}
      },
      appId: {type: Object | Array},
    },
    computed: {
      ...mapState({
        componentsList: state => state.divpage.componentsList,
        clickComIndex : state => state.divpage.clickComIndex,
      })
    },
    mounted(){
      let that = this;
      console.log("来了 ",that.showData)
      if(that.IsEmptyObj(that.showData)){
        that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      } else {
        that.formData = that.showData
      }
      that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
    },
    methods: {
      ...mapMutations([
        'updateData'
      ]),
      // 删除项目
      delItem(index){
        let that = this;
        if(that.formData.goodsList.length<=1){
          that.$message.error("请至少保留一条项目")
          return false;
        }
        that.$confirm('是否删除该项目?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText : '取消',
          type : 'warning'
        }).then(()=>{
          that.$delete(that.formData.goodsList, index)
        }).catch(()=>{})
      },
      // 删除按钮
      delBtn(index){
        let that = this;
        that.$confirm('是否删除该按钮?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText : '取消',
          type : 'warning'
        }).then(()=>{
          that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
          that.updateData({ componentsList: that.componentsList });
        }).catch(()=>{})
      },
      cancel(){
        this.$emit('cancel')
      },
      reset(){
        let that = this;
        that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
        that.componentsList[that.clickComIndex]['data'] = this.formData;
      },
      confirm(){
        this.$emit('confirm', this.formData)
      },
      delete(){
        this.$emit('delete')
      },
      openSortBox() {
        this.goodsSortBoxVisible = true;
      },
      deleteSort() {
          this.formData.sortRule = {id:""}
      },
      sortConfirm(obj) {
        // console.log("排序规则确认",obj)
        this.formData.sortRule = {id: obj.id, name: obj.name};
        this.goodsSortBoxVisible = false;
      },

    },
    watch: {
      showData: {
        handler(newVal, oldVal) {
          this.formData = newVal ? newVal : this.formData;
        },
        deep: true
      },
    }
  };
</script>
<style lang='less' scoped>
  @import '../colorui/main.css';
  @import '../colorui/icon.css';

  .tm-select-bg {
    text-align: center;
    cursor: pointer;
    padding: 10px 0;
  }

  .tm-select-bg:hover {
    background: #efefef;
  }
  .el-form-item{
    margin-bottom: 0;
  }
  .warning_msg_btn{
    margin-right: 10px;
  }
</style>
