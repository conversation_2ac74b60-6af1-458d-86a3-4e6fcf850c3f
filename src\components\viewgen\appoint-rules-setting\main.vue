<!--选档规则-->
<template>
  <div>
    <el-form ref="form" class="page_setting" :model="appointInfo" label-width="120px">
      <el-divider>基础属性</el-divider>
      <el-form-item label="选档条件">
        <el-radio-group v-model="appointInfo.type">
          <!--            <el-radio :label="2">直接选档</el-radio>-->
          <!--            <el-radio :label="1">购买后选档</el-radio>-->
          <el-radio :label="'0'">成团后选档</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="购买页面">
        <el-button :type="this.appointInfo.payPageId?'primary':'warning'" size="mini" @click="openPageSelectedBox">
          {{ getPageName() }}
        </el-button>
      </el-form-item>
      <el-form-item label="指定场地">
        <el-button :type="this.appointInfo.roomIdList?'primary':'warning'" size="mini" @click="openRoomBox">
          {{ getRoomName() }}
        </el-button>
      </el-form-item>
      <el-form-item label="允许用户换档">
        <el-radio-group v-model="appointInfo.changeFlag">
          <el-radio :label="'0'">否</el-radio>
          <el-radio :label="'1'">是</el-radio>
        </el-radio-group>
      </el-form-item>
      <div v-if="appointInfo.changeFlag=='1'">
        <el-form-item  label="允许换档次数">
          <el-input-number v-model="appointInfo.changeNum" size="small" controls-position="right" :min="1" :max="100"></el-input-number>
        </el-form-item>
        <el-form-item label="允许换挡时间">
          <el-input-number v-model="appointInfo.changeTime" size="small" controls-position="right" :min="1"
                           :max="100"></el-input-number>
          小时
        </el-form-item>
<!--        <el-form-item label="是否需要花费">-->
<!--          <el-radio-group v-model="appointInfo.changePayFlag">-->
<!--            <el-radio :label="'0'">否</el-radio>-->
<!--            <el-radio :label="'1'">是</el-radio>-->
<!--          </el-radio-group>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="花费金额" v-if="appointInfo.changePayFlag == '1'">-->
<!--          <el-input-number v-model="appointInfo.changePayPrice" size="small" :precision="2"  controls-position="right" :min="1"-->
<!--                           :max="999999999"></el-input-number>-->
<!--          元-->
<!--        </el-form-item>-->
      </div>
    </el-form>

    <!-- 场地选择 roomBoxVisible-->
    <el-dialog
      :append-to-body="true"
      title="场地选择"
      :visible.sync="roomBoxVisible"
      width="60%"
      center>
      <el-cascader-panel
        v-model="roomSelected"
        :props="{multiple:true, expandTrigger: 'hover' ,value: 'id',label: 'name',children:'roomList'}"
        :options="shopAndRoomList"
        @change="confirmRoom">
      </el-cascader-panel>
    </el-dialog>

    <!--    页面选择框-->
    <el-dialog
      title="档期所需页面"
      :visible.sync="pageSelectedBoxVisible"
      :close-on-click-modal="false"
      append-to-body>
      <div class="block">
        <el-cascader-panel
          v-model="selectedPage"
          :options="pageList"
          :props="{ expandTrigger: 'hover' ,value: 'id',label: 'pageName',children: 'pageList',}"
          @change="pageChange"></el-cascader-panel>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {getShopAndRoomTree} from "@/api/wxmp/shopinfo";
import {getGroupList} from "@/api/viewgen/pagedevise";
import {getObjByCon, putObj} from "@/api/viewgen/appointRules";

export default {
  name: "appointSetting",
  props: {
    appId: [String],
    pageId: [String],
  },
  components: {},
  data() {
    return {
      pageSelectedBoxVisible: false,
      selectedPage: "",//档期所需页面
      pageList: [], //页面选择数组
      shopAndRoomList: [],
      roomBoxVisible: false,
      roomSelected: [],
      appointInfo: {},//档期规则信息
      qrMessageBoxVisible: false, //扫码消息框
      tagBoxFlag: '',//标签弹出框标记
      tagBoxVisible: false,  //用户标签框
      selectedTagId: '',//选中的tagId
      grouponTime: [],//拼团时间
    }
  },
  created() {
    this.getAppointRulesData();
    let params = {
      appIdList: [this.appId],
      pageTypeList: [2]//活动选档
    }
    getGroupList(params).then(res => {
      let list = res.data.data;
      list.forEach(o => {
        o.pageName = this.getPageTypeName(o.id);
      })
      this.pageList = list;
    }).catch()
  },
  mounted() {
  },
  computed: {},
  methods: {
    getAppointRulesData() {
      if (this.pageId) {
        getObjByCon({pageId: this.pageId}).then(res => {
          this.appointInfo = res.data.data;
          console.log("this.appointInfo", this.appointInfo)
        });
      }
    },
    update() {
      putObj(this.appointInfo).then(res => {
        console.log("档期更新结果", res);
      }).catch()
    },
    openRoomBox() {
      getShopAndRoomTree().then(res => {
        this.shopAndRoomList = res.data.data;
        //回显数据
        if (this.appointInfo.roomIdList) {
          let roomList = this.appointInfo.roomIdList.split(",");
          this.roomSelected=[];
          this.shopAndRoomList.forEach(shop => {
            shop.roomList.forEach(o=> {
              roomList.forEach(select =>{
                if(select == o.id){
                  this.roomSelected.push([shop.id, o.id]);
                }
              })
            })
          })

        }
      }).catch(() => {
      })
      this.roomBoxVisible = true;
    },
    getRoomName() {
      if (this.appointInfo.roomIdList) {
        return "点击查看详情"
      }
      return "没有配置，请选择场地"
    },
    getPageName() {
      if (this.appointInfo.payPageId) {
        for (let i = 0; i < this.pageList.length; i++) {
          let obj = this.pageList[i];
          for (let j = 0; j < obj.pageList.length; j++) {
            let page = obj.pageList[j]
            if (page.id == this.appointInfo.payPageId) {
              return page.pageName;
            }
          }
        }
      } else {
        return "没有配置，请选择场地"
      }
    },
    confirmRoom(list) {
      console.log("111", list)
      console.log("shopAndRoomList", this.shopAndRoomList)
      let resStr = '';
      list.forEach(item => {
        if (!resStr) {
          resStr = item[1];
        } else {
          resStr = resStr + "," + item[1];
        }
      });
      this.appointInfo.roomIdList = resStr;
      this.roomSelected = list;
      console.log("roomIdList", this.appointInfo.roomIdList)
    },
    openPageSelectedBox() {
      let params = {
        appIdList: [this.appId],
        pageTypeList: [2]//活动选档
      }
      getGroupList(params).then(res => {
        let list = res.data.data;
        list.forEach(o => {
          o.pageName = this.getPageTypeName(o.id);
        })
        this.pageList = list;
        //数据回显
        this.pageList.forEach(type=>{
          type.pageList.forEach(page=>{
            if(page.id == this.appointInfo.payPageId){
              this.selectedPage = [page.pageType,page.id]
              return
            }
          })
        })
        this.pageSelectedBoxVisible = true;
      }).catch()
    },
    pageChange(val) {
      this.appointInfo.payPageId = val[1];
      this.pageSelectedBoxVisible = false;
    },
    getPageTypeName(type) {
      if ("1" == type) {
        return "首页"
      } else if ("2" == type) {
        return "拼团活动"
      } else if ("3" == type) {
        return "作品详情"
      } else if ("4" == type) {
        return "图文表单"
      } else if ("5" == type) {
        return "客照分享"
      }
    },
    getName() {

      return
    },
  }
};
</script>

<style lang="scss" scoped>

.page_setting {
  overflow: scroll;
  height: 80vh;
}

.el-form-item {
  margin-bottom: 0;
}
</style>
