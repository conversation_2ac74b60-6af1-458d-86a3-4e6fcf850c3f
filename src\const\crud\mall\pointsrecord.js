export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  excelBtn: true,
  printBtn: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  viewBtn: true,
  menu: false,
  searchMenuSpan: 6,
  column: [
    {
      label: '店铺',
      prop: 'shopId',
      type: 'select',
      filterable: true,
      props: {
        label: 'name',
        value: 'id'
      },
      search: true,
      editDisabled: true,
      dicUrl: '/mall/shopinfo/list',
      rules: [{
        required: true,
        message: '请选择店铺',
        trigger: 'blur'
      }]
    },
    {
      label: '用户',
      prop: 'userCode',
      search: true,
      slot: true
    },
    {
      label: '数量',
      prop: 'amount',
      sortable: true
    },
    {
      label: '描述',
      prop: 'description'
    },
    {
      label: '创建时间',
      prop: 'createTime',
      sortable: true
    },
  ]
}

export const tableOption2 = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menu: false,
  refreshBtn: false,
  column: [
    {
      label: '数量',
      prop: 'amount',
      sortable: true
    },
    {
      label: '描述',
      prop: 'description'
    },
    {
      label: '创建时间',
      prop: 'createTime',
      sortable: true
    },
  ]
}
