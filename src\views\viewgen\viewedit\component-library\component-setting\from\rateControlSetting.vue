<template>
  <div>
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">评分控件</p>
      <div slot="hint"></div>
      <div slot="mainContent">
        <el-divider>基础属性</el-divider>
        <el-form ref="form" label-width="100px" :model="formData">
          <el-form-item label="标题内容">
            <el-input v-model="formData.title" size="mini" style="margin-top: 5px" placeholder="标题文字">
            </el-input>
          </el-form-item>
          <el-form-item label="标题颜色">
            <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
              <el-input v-model="formData.titleColor" size="small" style="margin-top: 5px">
                <template slot="append">
                  <el-color-picker size="mini" v-model="formData.titleColor"></el-color-picker>
                </template>
              </el-input>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="标题大小">
            <el-input v-model="formData.titleSize" size="mini" style="margin-top: 5px" placeholder="文字大小">
            </el-input>
          </el-form-item>

          <el-form-item label="标题加粗">
            <el-switch
              v-model="formData.titleWeight"
              active-text="加粗"
              inactive-text="普通">
            </el-switch>
          </el-form-item>
          <el-divider></el-divider>
          <el-form-item label="描述内容">
            <el-input v-model="formData.describe" size="mini" style="margin-top: 5px" placeholder="标题文字">
            </el-input>
          </el-form-item>
          <el-form-item label="描述颜色">
            <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
              <el-input v-model="formData.describeColor" size="small" style="margin-top: 5px">
                <template slot="append">
                  <el-color-picker size="mini" v-model="formData.describeColor"></el-color-picker>
                </template>
              </el-input>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="描述大小">
            <el-input v-model="formData.describeSize" size="mini" style="margin-top: 5px" placeholder="文字大小">
            </el-input>
          </el-form-item>
          <el-form-item label="描述加粗">
            <el-switch
              v-model="formData.describeWeight"
              active-text="加粗"
              inactive-text="普通">
            </el-switch>
          </el-form-item>
          <el-divider></el-divider>
          <el-form-item label="评分初始值">
              <el-input-number v-model="formData.initRate" size="mini" :step="this.formData.isHalf?0.5:1" :min="0" :max="5" label="描述文字" step-strictly></el-input-number>
          </el-form-item>
          <el-form-item label="评分颜色">
            <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
              <el-input v-model="formData.selectColor"  size="small" style="margin-top: 5px" @input="changeColor">
                <template slot="append">
                  <el-color-picker size="mini" v-model="formData.selectColor" @change="changeColor" ></el-color-picker>
                </template>
              </el-input>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="是否半星">
            <el-switch
              v-model="formData.isHalf"
              active-text="半星"
              @change="isHalfChange"
              inactive-text="非半星">
            </el-switch>
          </el-form-item>
          <el-form-item label="评分类型">
            <el-radio-group v-model="formData.rateType">
              <el-radio :label="1">文字</el-radio>
              <el-radio :label="2">星级</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-show="formData.rateType == 1" label="评分文字" >
            <div v-for="(item,index) in formData.textList" :key="index">
              <div style="display: flex">
                <span style="display: flex;width: 20%;justify-content: center;align-items: center;">{{getRateNumber(index)}}星</span>
                <el-input  v-show="getRateInput(index)" :value="item" type="text"  @input="(e)=>textListInput(e,index)" placeholder="请输入内容" >
                </el-input>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </settingSlot>
  </div>
</template>

<script>

import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';

import settingSlot from './../settingSlot'
import iconSelect from '../../pages/page-components/iconSelect.vue'
export default {
  components: {settingSlot,iconSelect},
  data() {
    return {
      formDataCopy: {
        title: '摄影师',
        titleColor: '#000000',
        titleSize: 18,
        titleWeight: false,//标题是否加粗
        describe: '请对摄影师进行评分',//描述
        describeColor: '#666666',//描述字体色
        describeSize: 12,
        describeWeight: false,
        initRate: 0,
        selectColor: '#F7BA2A',
        voidColor: '#ececec',
        rateColors:['#F7BA2A','#F7BA2A','#F7BA2A'],
        rateType: 1,//1文字 2星级
        isHalf: false,//是否半星
        textList: ['极差', '失望', '一般', '满意', '惊喜'],//是否半星
      },
      formData: {},
    };
  },
  props: {
    showData: {
      type: Object,
      default: () => {
      }
    },
    config: {
      type: Object,
      default: () => {
      }
    }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
      clickComIndex: state => state.divpage.clickComIndex,
    })
  },
  watch: {
    showData: {
      handler(newVal, oldVal) {
        this.formData = newVal ? newVal : this.formData;
      },
      deep: true
    },
  },
  mounted() {
    let that = this;
    console.log("that.showData",that.showData)
    console.log("check",that.IsEmptyObj(that.showData),this.showData)
    if (that.IsEmptyObj(that.showData)) {
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
    } else {
      that.formData = that.showData
    }
    that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
    // that.updateData({
    //   componentsList: that.componentsList
    // })
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),

    // 添加项目
    addItem() {
      let that = this;
      that.pushItem()
    },
    pushItem() {
      let that = this;
      if (that.formData.noticeList.length >= 10) {
        that.$message.error("项目不能超过10条")
        return false;
      }
      that.formData.noticeList.push({
        id: Math.random(),
        imageUrl: '',
        imgWidth: 0,
        imgHeight: 0,
        pageUrl: '',
        content: '',
        tag: ''
      })
    },
    // 删除项目
    delItem(index) {
      let that = this;
      if (that.formData.swiperList.length <= 1) {
        that.$message.error("请至少保留一条项目")
        return false;
      }
      that.$confirm('是否删除该项目?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$delete(that.formData.noticeList, index)
      }).catch(() => {
      })
    },
    // 删除按钮
    delBtn(index) {
      let that = this;
      that.$confirm('是否删除该按钮?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
        that.updateData({componentsList: that.componentsList});
      }).catch(() => {
      })
    },
    cancel() {
      this.$emit('cancel')
    },
    reset() {
      let that = this;
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      that.componentsList[that.clickComIndex]['data'] = this.formData;
    },
    confirm() {
      this.$emit('confirm', this.formData)
    },
    delete() {
      this.$emit('delete')
    },
    // 拖动的事件等等=======================================================>
    datadragStart(e) {
      let that = this;
      that.updateData({clickComIndex: null})
      that.showSetBlock = false;
    },
    datadragUpdate(e) {
      e.preventDefault();
    },
    datadragEnd(e) {
      // console.log(e, "拖动结束");
    },
    datadragMove(e, originalEve) {
      return (e.draggedContext.element.text !== 'Gold（不可拖动元素）');
    },
    // 拖动的事件等等=======================================================<
    //渲染输入框的条件
    getRateInput(val){
      if(this.formData.isHalf){
        if(val%0.5==0){
          return true;
        }
      }else{
        if(val%1==0){
          return true;
        }
      }
      return false;
    },
    //渲染输入框的条件
    getRateNumber(val){
      let num = 0;
      let median = 0;
      if(this.formData.isHalf){
        median = 0.5;
      }else{
        median = 1;
      }
      for (let i = 0; i <val ; i++) {
        num = median + num;
      }
      return num;
    },
    //全半星改变
    isHalfChange(val){
      if(val){
        this.$set(this.formData, "textList", ['极差','极差', '失望', '失望', '一般', '一般', '满意', '满意', '惊喜', '惊喜'])
      }else{
        this.$set(this.formData, "textList", ['极差', '失望', '一般', '满意', '惊喜'])
      }
    },
    //输入值的改变
    textListInput(val,index){
      this.$set(this.formData.textList, index, val);
    },
    changeColor(val){
      this.$set(this.formData.rateColors, 0, val);
      this.$set(this.formData.rateColors, 1, val);
      this.$set(this.formData.rateColors, 2, val);
    }
  },

};
</script>
<style lang='less' scoped>

@import '../../colorui/main.css';

.el-form-item {
  margin-bottom: 0;
}

.menu_list_title_name {
  display: inline;
}

.menu_list_title {
  display: block;
  width: 80%;
}

.menu_list_tag {
  display: none;
  float: right;
}

.drag-item {
  padding: 0px 0 5px 20px;
  margin-bottom: 15px;
  margin-top: 20px;
  border: 1px solid transparent;

  &:hover {
    cursor: move;
    border: 1px dashed #1fc421;

    .menu_list_tag {
      display: inline;
    }
  }
}
</style>
