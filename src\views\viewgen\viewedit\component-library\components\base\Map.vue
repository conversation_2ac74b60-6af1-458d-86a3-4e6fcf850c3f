<!-- 地图组件 -->
<template>
  <div  class="flex justify-center align-center" :style="{background:`${setData.background}`, paddingTop:`${setData.paddingTop}px`,paddingBottom:`${setData.paddingBottom}px`}"   >
    <div :disabled="true" style="pointer-events: none"  @click.stop="true" :style="{width:`${setData.width}%`,height:`${setData.height}px`}" >
      <iframe id="mapPage" class="t_map_style" frameborder=0
              :src="url">
      </iframe>
    </div>
  </div>
</template>


<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';

export default {
  data() {
    return {
      url: "https://apis.map.qq.com/tools/poimarker?type=0&marker=coord:39.892326,116.342763;title:超好吃冰激凌;addr:北京市手帕口桥北铁路道口&key=NWJBZ-OOZWD-DEH4O-H2Z26-L4M4F-OZF4Z&referer=gocreateone",
    };
  },
  components: {},
  props: {
    thememobile: {type: Object | Array},
    setData: {type: Object | Array},
  },
  watch: {
    componentsList(newVal, oldVal) {          //添加的时候触发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    },
    'setData.location':{
      handler:function() {   //特别注意，不能用箭头函数，箭头函数，this指向全局
        this.changeUrl()
      },
      deep: true    //深度
    },
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {

  },
  mounted() {
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    changeUrl(){
      if(!this.setData.location || !this.setData.location.obj || !this.setData.location.obj.id){
        return;
      }
      let url = "https://apis.map.qq.com/tools/poimarker?type=0&key=NWJBZ-OOZWD-DEH4O-H2Z26-L4M4F-OZF4Z&referer=gocreateone";
      let coord = this.setData.location.obj.location.lat+","+this.setData.location.obj.location.lng;
      let title = this.setData.location.obj.title;
      let addr = this.setData.location.obj.address;
      let market = "&marker=coord:"+coord+";title:"+title+";addr:"+addr;
      url = url + market;
      // console.log("情趣，",url)
      this.url = url;
    }
  },

};
</script>
<style lang='less' scoped>

@import '../../colorui/main.css';
@import '../../colorui/icon.css';

.t_map_style{
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
}
</style>
