import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/payapi/payapplyform/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/payapi/payapplyform',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/payapi/payapplyform/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/payapi/payapplyform/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/payapi/payapplyform',
        method: 'put',
        data: obj
    })
}

export function subObj(obj) {
  return request({
    url: '/payapi/payapplyform/submit',
    method: 'post',
    data: obj
  })
}

export function statusObj(obj) {
  return request({
    url: '/payapi/payapplyform/applystatus',
    method: 'post',
    data: obj
  })
}
