.theme-star {
    .avue-contail {
        background-image: url("/img/bg/star-squashed.jpg");
        background-size: 100% 100%;
    }
    .avue-header,
    .avue-logo,
    .tags-container {
        background-color: transparent;
    }
    .el-card {
        opacity: .9;
    }
    .top {
        .el-dropdown {
            color: #fff;
            i {
                color: #fff;
            }
        }
        .top-item {
            i {
                color: #fff;
            }
        }
    }
    .avue-tabs {
        padding: 0 20px !important;
    }
    .avue-sidebar,
    .logo,
    .el-menu-item,
    .el-submenu__title,
    .el-menu {
        background-color: transparent !important
    }
    .logo_title,
    .avue-breadcrumb,
    {
        color: #fff !important;
        i {
            color: #fff;
        }
    }
    .el-menu--horizontal>.el-menu-item.is-active {
        border-bottom: none;
    }
    .top {
        border-bottom: none;
    }
    .avue-tags {
        background-color: transparent;
        border: none;
    }
    .tag-item {
        color: #fff !important;
        border: none !important;
        background-color: rgba(255, 255, 255, .5);
        &.is-active {
            color: #fff !important;
            border: 1px solid #fff !important;
            background-color: rgba(0, 0, 0, .4);
            .tag-item-icon {
                color: #fff !important;
            }
        }
    }
    .el-menu-item {
        i,
        span {
            color: #fff !important;
        }
        &:hover {
            i,
            span {
                color: #409eff !important;
            }
        }
        &.is-active {
            background-color: rgba(0, 0, 0, .4) !important;
            &:hover {
                background-color: rgba(0, 0, 0, .4) !important;
            }
        }
    }
    .el-submenu__title {
        i,
        span {
            color: #fff !important;
        }
        &:hover {
            i,
            span {
                color: #409eff !important;
            }
            background-color:transparent !important;
        }
    }
    .el-submenu .el-menu-item {
        &.is-active {
            background-color: rgba(0, 0, 0, .4) !important;
            span,
            i {
                color: $mainBg !important;
            }
            &:hover {
                background-color: rgba(0, 0, 0, .4) !important;
            }
        }
    }
    .top-search {
        input::-webkit-input-placeholder,
        textarea::-webkit-input-placeholder {
            /* WebKit browsers */
            color: #fff;
        }
        input:-moz-placeholder,
        textarea:-moz-placeholder {
            /* Mozilla Firefox 4 to 18 */
            color: #fff;
        }
        input::-moz-placeholder,
        textarea::-moz-placeholder {
            /* Mozilla Firefox 19+ */
            color: #fff;
        }
        input:-ms-input-placeholder,
        textarea:-ms-input-placeholder {
            /* Internet Explorer 10+ */
            color: #fff;
        }
    }
    .top-bar__item {
        i {
            color: #fff;
        }
    }
}