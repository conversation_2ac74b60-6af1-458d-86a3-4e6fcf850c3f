<template>
  <div class="phoneCheckComponent" :style="{background: `${setData.backgroundColor}`, marginBottom: `${setData.pageSpacing}px`}">
    <div :style="{color: `${setData.titleColor}`, fontSize: `${setData.titleSize}px`,fontWeight:`${setData.titleWeight?'bold':'normal'}`}">{{setData.title}}<i v-show="setData.required" style="color: #FF0000">*</i></div>
    <div :style="{color: `${setData.describeColor}`, fontSize: `${setData.describeSize}px`,fontWeight:`${setData.describeWeight?'bold':'normal'}`}">{{setData.describe}}</div>
    <div class="phone_check_box padding" >
      <div class="phone_check_icon cuIcon-phone" ></div>
      <input class="phone_check_input" disabled="true" :placeholder="'请输入'+setData.title">
    </div>
    <div class="phone_check_box padding">
      <div class="phone_check_icon cuIcon-safe" ></div>
      <input class="phone_check_input" disabled="true" :placeholder="'请输入'+setData.title"><el-button size="mini" :disabled="true">发送验证码</el-button>
    </div>
  </div>
</template>

<script>
import { mapState , mapGetters , mapMutations , mapActions } from 'vuex';
export default {
  data() {
    return {};
  },
  components: { },
  props: {
    theme : { type: Object | Array },
    setData : { type: Object | Array },
    cId     : { type: Number },
    noEditor: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {
  },
  mounted() {
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
  },
  watch:{
    setData(newVal, oldVal){},
    componentsList(newVal, oldVal){          //添加的时候出发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    }
  },
  beforeDestroy(){
    // this.$root.Bus.$off('addHotSpot')
  }
};
</script>
<style lang='less' scoped>
.phoneCheckComponent {
  position: relative;
  display: block;
  width: 100%;
  padding: 5px;
  background: #ffffff;

  .phone_check_box{
    padding: 2px;
    border: 1px solid rgba(118, 118, 118, 0.3);
  }
  .phone_check_icon{
    display: inline-block;
    padding: 0 5px;
  }
  .phone_check_input{
    flex: 1 1 0%;
    display: inline-block;
    border: 0;
    color:  #ffffff;
    width: 40%;
    position: relative;
    overflow: hidden;
  }
}

</style>
