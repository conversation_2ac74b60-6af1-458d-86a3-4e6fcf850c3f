import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/wxgoodstagtype/page',
    method: 'get',
    params: query
  })
}

export function getList(query) {
  return request({
    url: '/weixin/wxgoodstagtype/list',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/weixin/wxgoodstagtype',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/weixin/wxgoodstagtype/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/wxgoodstagtype/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/wxgoodstagtype',
    method: 'put',
    data: obj
  })
}
