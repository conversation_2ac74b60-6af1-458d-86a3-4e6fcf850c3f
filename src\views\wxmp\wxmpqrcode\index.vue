<template>
  <div class="execution">
    <el-tabs v-model="activeName" type="border-card" @tab-click="tabClick">
      <el-tab-pane label="二维码管理" name="1">
        <basic-container>
          <avue-crud ref="crud"
                     :page="page"
                     :data="tableData"
                     :permission="permissionList"
                     :table-loading="tableLoading"
                     :option="tableOption"
                     v-model="form"
                     @on-load="getPage"
                     @refresh-change="refreshChange"
                     @row-update="handleUpdate"
                     @row-save="handleSave"
                     @row-del="handleDel"
                     @sort-change="sortChange"
                     @search-change="searchChange">
            <template slot-scope="scope" slot="menuLeft">
              <el-button type="primary"
                         icon="el-icon-plus"
                         size="small"
                         @click.stop="openMpQrCodeBox('add')">新增二维码
              </el-button>
            </template>
            <template slot-scope="scope" slot="menu">
              <el-button icon="el-icon-full-screen" type="text" size="small" @click="getQrCode(scope.row.ticket)">二维码
              </el-button>
              <el-button icon="el-icon-edit" type="text" size="small" @click="openMpQrCodeBox('put',scope.row)">编辑
              </el-button>
              <el-button v-if="scope.row.endTime" icon="el-icon-delete" type="text" size="small" @click="handleDel(scope.row)">删除</el-button>
            </template>
          </avue-crud>

      <!--      提交、修改弹出框-->
          <el-dialog
        :title="qrCodeBoxForm.title"
        :visible.sync="qrCodeBoxVisible"
        :close-on-click-modal="false"
        @close="closeQrCodeBox"
        width="60%">
        <el-form :rules="formRules" :ref="qrCodeBoxForm" :model="qrCodeBoxForm" label-width="auto"
                 :destroy-on-close="true">
          <el-form-item label="公众号" prop="appId">
            <el-select v-model="qrCodeBoxForm.appId" placeholder="请选择" :disabled="qrCodeBoxForm.type == 'put'" @change="wxAppChange">
              <el-option
                v-for="item in wxAppList"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="二维码名称" prop="name">
            <el-input v-model="qrCodeBoxForm.name" :maxlength="25" style="width:450px" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="失效日期">
            <el-tooltip class="item" effect="dark" content="提示：1.永久二维码不可删除，上限为100000。2.临时二维码有效时间最大不能超过30天" placement="right">
              <el-date-picker
                :disabled="qrCodeBoxForm.type == 'put'"
                v-model="qrCodeBoxForm.endTime"
                align="right"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="date"
                placeholder="不填则代表长期有效"
                :picker-options="pickerOptions">
              </el-date-picker>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="扫码标签">
            <span @click="openTagBox">
              <el-tag v-show="qrCodeBoxForm.tagId" class="qrcode_form_tag" size="medium" :color="qrCodeBoxForm.tagBackColor"
                      :style="getFontColor(qrCodeBoxForm.tagFontColor)">{{ qrCodeBoxForm.tagName }}
              </el-tag>
              <el-button v-show="!qrCodeBoxForm.tagId" icon="el-icon-plus" size="mini"></el-button>
            </span>
            <el-button v-show="qrCodeBoxForm.tagId"  @click="deleteTag" icon="el-icon-delete" size="mini" type="danger"></el-button>
          </el-form-item>
          <el-form-item label="扫码消息">
            <div @click="openMessageBox">
              <el-input v-model="qrCodeBoxForm.messageName" :readonly="true"></el-input>
            </div>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submit">{{ qrCodeBoxForm.submitButton }}</el-button>
            <el-button @click="closeQrCodeBox ">取消</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
      <!--      二维码标签栏 tagBoxVisible-->
          <el-dialog
        title="标签"
        :visible.sync="tagBoxVisible"
        width="60%"
        center>
          <div style="overflow: hidden">
            <wx-user-tag  :selectedType="0"  :selectedTagId="qrCodeBoxForm.tagId"  :appId="tagAppId" v-on:ensureTag="ensureTag" @backFun="ensureTag"></wx-user-tag>
          </div>
      </el-dialog>

      <!--      二维码消息-->
          <el-dialog
              title="扫码消息"
              :visible.sync="qrMessageBoxVisible"
              :close-on-click-modal="false"
              center
              :append-to-body="true"
              lock-scroll
              width="80%">
              <div style="overflow: hidden">
                <qrMessage key="dialog" :selectedAppId="selectedAppId" v-on:ensureMsg="ensureMsg" @backFun="ensureMsg"></qrMessage>
              </div>
            </el-dialog>

      <!--      图片预览框-->
      <el-dialog
            :visible.sync="qrCodeVisible"
            :show-close="false"
            :append-to-body="true"
            width="30%"
            center>
            <div style="display: flex;align-items: center;justify-content: center;">
              <el-image :src="qrCodeUrl">
                <div slot="placeholder">
                  加载中<span>...</span>
                </div>
              </el-image>
            </div>
            <span slot="title" class="pre_view_title">
          <span>二维码</span>
        </span>
          </el-dialog>
    </basic-container>
      </el-tab-pane>
      <el-tab-pane label="扫码消息管理" name="2">
        <qrMessage key="box"  ref="qrMessageBox"></qrMessage>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {getPage, getObj, addObj, putObj, delObj} from '@/api/upms/wxmpqrcode'
import {tableOption} from '@/const/crud/upms/wxmpqrcode'
import {getUserTagAndType} from '@/api/wxmp/wxusertags'
import {getList as getWxAppList} from '@/api/wxmp/wxapp'
import {mapGetters} from 'vuex'
import qrMessage from '@/views/wxmp/wxmpqrcodemessage/index'
import wxUserTag from "@/views/wxmp/wxusertags/userTagSelect";
export default {
  name: 'wxmpqrcode',
  components: {
    qrMessage,
    wxUserTag
  },
  data() {
    return {
      tagBoxVisible:false,
      tagAppId:'',//标签盒需要的appId
      activeName:"1",
      wxAppList: [],
      selectedAppId: "",//添加表单需要的appId
      qrCodeBoxVisible: false,//表单显示
      qrMessageBoxVisible: false,//二维码消息显示
      qrCodeVisible: false,//二维码显示
      qrCodeUrl: "",//二维码地址
      qrCodeBoxForm: {
        tagId: ''
      },
      formRules: {
        appId: [
          {required: true, message: '请选择公众号', trigger: 'submit'}
        ],
        name: [
          {required: true, message: '请输入二维码名称', trigger: 'submit'},
        ],
      },
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      pickerOptions: {
        disabledDate(time) {//限制今天以后的30天
          return time.getTime() > Date.now() + (24 * 60 * 60 * 1000 * 30) || time.getTime() < (Date.now() );
        },
        shortcuts: [{
          text: '明天',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() + 3600 * 1000 * 24 * 1);
            picker.$emit('pick', date);
          }
        }, {
          text: '一周后',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() + 3600 * 1000 * 24 * 7);
            picker.$emit('pick', date);
          }
        }, {
          text: '两周后',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() + 3600 * 1000 * 24 * 7 * 2);
            picker.$emit('pick', date);
          }
        }]
      },
    }
  },
  created() {
    getWxAppList({
      appType: '2'
    }).then(res => {
      let data = res.data
      this.wxAppList = data;
      console.log(data)
      //默认加载第一个公众号的素材
      for (let i = 0; i < data.length; i++) {
        tableOption.column[2].dicData.push({label: data[i].name, value: data[i].id});
      }
    }).catch(() => {
    })
  },
  mounted() {
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.permissions['weixin:wxmpqrcode:add'] ? true : false,
        delBtn: this.permissions['weixin:wxmpqrcode:del'] ? true : false,
        editBtn: this.permissions['weixin:wxmpqrcode:edit'] ? true : false,
        viewBtn: this.permissions['weixin:wxmpqrcode:get'] ? true : false
      };
    }
  },
  methods: {
    tabClick(tab){
      if(tab.name=="2"){
        this.$refs.qrMessageBox.refreshChange();
      }
    },
    searchChange(params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    sortChange(val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage(page, params) {
      this.tableLoading = true
      getPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
      }, params, this.paramsSearch)).then(res => {
        this.tableData = res.data.data.records
        this.page.total = res.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this
      this.$confirm('是否确认删除此数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        return delObj(row.id)
      }).then(data => {
        _this.$message({
          showClose: true,
          message: '删除成功',
          type: 'success'
        })
        this.getPage(this.page)
      }).catch(function (err) {
      })
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      putObj(row).then(res => {
        this.$message({
          showClose: true,
          message: '修改成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      addObj(row).then(res => {
        this.$message({
          showClose: true,
          message: '添加成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page)
    },
    openMpQrCodeBox(type, obj) {
      if (type == 'add') {
        this.qrCodeBoxForm = {//真正的提交表单
          title: "新建作品",
          type: "add",
          submitButton: "立即创建",
          name: '',
          tagList: [],//标签列表
        };
        this.qrCodeBoxVisible = true;
      }
      if (type == 'put') {
        this.echoForm(obj)
      }
    },
    closeQrCodeBox(){
      this.$refs[this.qrCodeBoxForm].resetFields();
      this.qrCodeBoxVisible = false;
    },
    submit() {
      console.log("提交功能", this.qrCodeBoxForm);
      this.$refs[this.qrCodeBoxForm].validate((valid) => {
        if (valid) {
          if(!this.qrCodeBoxForm.id) {
            addObj(this.qrCodeBoxForm).then(res => {
              this.$message({
                showClose: true,
                message: '添加成功',
                type: 'success'
              })
              this.qrCodeBoxVisible = false;
              this.refreshChange()
            }).catch(() => {
            })
          }else{
            putObj(Object.assign(this.qrCodeBoxForm)).then(res => {
              this.goodsBoxVisible = false;
              this.$message({
                showClose: true,
                message: '修改成功',
                type: 'success'
              })
              this.qrCodeBoxVisible = false;
              this.refreshChange();
            }).catch(() => {
            })
          }
        }
      });
    },
    getFontColor(val) {
      if (!val) {
        return;
      }
      return "color:" + val;
    },
    openTagBox() {
      if(!this.qrCodeBoxForm.appId){
        this.$message.warning("请先选则一个公众号");
        return
      }
      this.tagAppId = this.qrCodeBoxForm.appId;
      this.tagBoxVisible = true;
    },
    deleteTag() {
      this.qrCodeBoxForm.tagId = "";
    },
    openMessageBox() {
      if(!this.qrCodeBoxForm.appId){
        this.$message.warning("请先选择公众号");
        return
      }
      this.selectedAppId = this.qrCodeBoxForm.appId;
      this.qrMessageBoxVisible = true;
    },
    echoForm(obj) {
      this.qrCodeBoxForm = JSON.parse(JSON.stringify(obj));
      this.qrCodeBoxForm.type = "put";
      this.qrCodeBoxForm.title = "修改二维码";
      this.qrCodeBoxForm.submitButton = "确认修改";
      this.qrCodeBoxVisible = true;
    },
    ensureMsg(obj){
      console.log("选中一行",obj);
      this.qrCodeBoxForm.messageId =  obj.id;
      this.qrCodeBoxForm.messageName =  obj.name;
      this.qrMessageBoxVisible = false;
    },
    getQrCode(ticket){
      this.qrCodeUrl = ticket;
      this.qrCodeVisible = true;
    },
    //弹出标签框的确认
    ensureTag(obj) {
      console.log("确认标签122", obj)
      this.selectedTag = obj;
      this.qrCodeBoxForm.tagId = obj.id;
      this.qrCodeBoxForm.tagName = obj.name;
      this.qrCodeBoxForm.tagBackColor = obj.backColor;
      this.qrCodeBoxForm.tagFontColor = obj.fontColor;
    },
    wxAppChange(){
      this.selectedTag = {};
      this.qrCodeBoxForm.messageId =   "";
      this.qrCodeBoxForm.messageName = "";
    }
  }
}
</script>

<style lang="scss" scoped>
.userTagBox {
  height: 400px;
  overflow: scroll
}

.qrcode_form_tag {
  margin-right: 10px;
}

.userTagBox_type {
  overflow: auto;
}

.userTagBox_tag {
  display: block;
  float: left;
  padding: 10px;
}
</style>
