export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  excelBtn: true,
  printBtn: true,
  viewBtn: true,
  searchMenuSpan: 6,
  column: [
    {
      label: '创建时间',
      prop: 'createTime',
      sortable: true,
      rules: [
        {
          required: true,
          message: '请输入创建时间',
          trigger: 'blur'
        },
      ]
    },
    {
      label: '最后签到时间',
      prop: 'updateTime',
      sortable: true,
      rules: [
        {
          required: true,
          message: '请输入最后更新（签到）时间',
          trigger: 'blur'
        },
      ]
    },
    {
      label: '用户',
      prop: 'userId',
      slot: true
    },
    {
      label: '连续天数',
      prop: 'continuDays',
      sortable: true,
      rules: [
        {
          required: true,
          message: '请输入连续天数',
          trigger: 'blur'
        },
      ]
    },
    {
      label: '累计天数',
      prop: 'cumulateDays',
      sortable: true,
      rules: [
        {
          required: true,
          message: '请输入累计天数',
          trigger: 'blur'
        },
      ]
    },
  ]
}
