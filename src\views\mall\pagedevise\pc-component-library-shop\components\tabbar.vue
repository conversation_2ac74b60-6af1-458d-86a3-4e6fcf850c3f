<!--
  - Copyright (C) 2018-2020
  - All rights reserved, Designed By www.gocreateone.com
  - 注意：
  - 本软件为www.gocreateone.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<!-- tab bar -->
<template>
    <div class="tabbarComponent" :style="{paddingBottom: `${setData.pageSpacing}px`}">
      <div class="cu-bar tabbar  classify-bg">
        <div class="action" data-cur="1" :style="{color: `${setData.textColor}`}">
          <div class='cuIcon-cu-image'>
            <span class="cuIcon-shop action-icon"></span>
          </div>
          <div class="text-df">首页</div>
        </div>
        <div class="action text-black"  data-cur="2">
          <div class='cuIcon-cu-image'>
            <span class="cuIcon-goods action-icon"></span>
          </div>
          <div class="text-df">全部商品</div>
        </div>
        <div class="action text-black"  data-cur="3">
          <div class='cuIcon-cu-image'>
            <span class="cuIcon-sort action-icon"></span>
          </div>
          <div class="text-df">分类</div>
        </div>
        <div class="action text-black" data-cur="4">
          <div class='cuIcon-cu-image text-sm'>
            <span class="cuIcon-service action-icon"></span>
          </div>
          <div class="text-df">客服</div>
        </div>
      </div>
    </div>
</template>

<script>
import { mapState , mapGetters , mapMutations , mapActions } from 'vuex';
import placeholderImg from "../pages/page-components/placeholderImg";

export default {
    data() {
        return {
          defaultImage: require('../assets/images/icon/<EMAIL>')
        };
    },
    components: { placeholderImg },
    props: {
        theme : { type: Object | Array },
        setData : { type: Object | Array },
        cId     : { type: Number },
        noEditor: {
            type: Boolean,
            default: false,
        }
    },
    computed: {
        ...mapState({
            componentsList: state => state.divpageShop.componentsList,
        }),
    },
    created() {
    },
    mounted() {
    },
    methods: {
        ...mapMutations([
            'updateData'
        ]),
    },
    watch:{
        setData(newVal, oldVal){},
        componentsList(newVal, oldVal){          //添加的时候出发（解决没有移动的时候不emit数据）
            let that = this;
            that.updateData({componentsList: that.componentsList})
        }
    },
    beforeDestroy(){
        // this.$root.Bus.$off('addHotSpot')
    }
};
</script>
<style lang='less' scoped>

  @import '../colorui/main.css';
  .tabbarComponent {
    font-weight: 300;
    .cu-list.grid.no-border {
      padding-bottom: 10px;
    }
    .classify-bg {
      height: 78px;
    }
    .action-icon{
      font-size: 20px!important;
    }
  }

</style>
