<!-- 微信选择页面的组件 -->
<template>
  <div>
    <el-switch v-show="showCustom" v-model="isSystemUrlTemp" active-color="#13ce66" active-text="系统地址"
      inactive-text="自定义地址" @change="switchChange">
    </el-switch>
    <div v-show="isSystemUrlTemp">
      <el-button @click="openPageBox" size="small" type="primary">{{ getName() }}</el-button>
      <el-input style="margin-top: 5px;" size="small" disabled placeholder="页面URL" v-model="pageTemp"></el-input>
    </div>
    <div v-show="!isSystemUrlTemp">
      <el-input style="margin-top: 5px;" size="small" placeholder="请输入域名，如：https://www.baidu.com" v-model="pageTemp"
        @blur="inputPageBlur" :maxlength="300" show-word-limit></el-input>
    </div>


    <!--    页面选择框-->
    <el-dialog title="页面选择" :visible.sync="pageSelectedBoxVisible" :close-on-click-modal="false" append-to-body>
      <div class="block">
        <el-cascader-panel v-model="selectedPage" :options="options" :props="{ expandTrigger: 'hover', value: 'obj' }"
          @change="pageChange"></el-cascader-panel>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { pageUrls } from '@/components/wx-page-select/pageUrls'
import { getListByType, getObj, getPageList, getTypeList } from '@/api/viewgen/pagedevise'
import { getList as getWxAppList } from "@/api/wxmp/wxapp";

export default {
  props: {
    page: {
      type: String,
    },  //地址
    clientType: [String], // 当前自定义页面类型
    isSystemUrl: {//初始化按钮  是否显自定义地址
      type: Boolean,
      default: false,
    },
    showCustom: {//是否显示选择按钮
      type: Boolean,
      default: true,
    }

  },
  watch: {
    page(val, oldVal) {
      // console.log("监听值改变111", val,)
      this.watchPageChange();
    },
    clientType(val, oldVal) {
    },

  },
  created() {
    this.initPage();

  },
  mounted() {
    // console.log("mounted",this.isSystemUrl)
  },
  data() {
    return {
      pageNameTemp: '', //临时展示名称
      pageUrls: [], //app 页面地址
      curType: '',
      pageTemp: '',//临时展示的url
      shopDisabled: true,
      spuDisabled: true,
      isArticleInfo: false,// 文章 pages/article/article-info/index'
      articleInfoList: [], // 文章列表
      shopList: [], // 店铺列表
      spuList: [], // 店铺的商品列表
      form: {
        page: '',
        pageType: '',
        shopId: '',
        spuId: '',
        articleInfoId: '',
      },
      isSystemUrlTemp: this.isSystemUrl,
      pageParamsList: [],//具体参数页面
      pageSelected: '',//选中的页面种类
      pageParamsSelected: '',//选中的具体页面
      selectedPage: '',//级连选择器 选中的具体页面
      pageSelectedBoxVisible: false,//页面选择框
      options: [], //页面选择数组
    }
  },

  methods: {
    initPage() {
      if (this.page) {
        let pageTemp = JSON.parse(JSON.stringify(this.page));
        this.pageTemp = pageTemp;
        if (pageTemp.indexOf('page_id') != -1) {
          this.isSystemUrlTemp = true
          let pageId = pageTemp.substring(this.page.indexOf('page_id=') + 8, this.page.indexOf('page_id=') + 27);
          getObj(pageId).then(res => {
            this.pageNameTemp = res.data.data.pageName
          }).catch()
        }
      }
    },
    //加载公众号
    getWxApp() {
      this.selectedPage = "";
      getWxAppList({
      }).then(res => {
        console.log("加载公众号", res)
        //得到类别
        this.options = [];
        let optionsList = [];
        res.data.forEach(o => {
          let obj = {
            obj: o,
            value: o.id,
            label: o.name,
            children: []
          };
          pageUrls.forEach(type => {
            obj.children.push({
              obj: type,
              value: type.type,
              label: type.name,
              children: []
            })
          })
          optionsList.push(obj)
        })

        // 添加系统内置页面分类
        let systemPagesObj = {
          obj: { id: 'system', name: '系统内置页面', tenantId: 'system' },
          value: 'system',
          label: '系统内置页面',
          children: [
            {
              obj: { type: 'order', name: '订单', url: '/pages/order/order-list/index' },
              value: 'order',
              label: '订单',
              children: [
                {
                  obj: { id: 'order-list', pageName: '订单列表页' },
                  value: 'order-list',
                  label: '订单列表页',
                }
              ]
            }
          ]
        };
        optionsList.push(systemPagesObj);

        getPageList().then(res => {
          console.log("加载页面list", res)
          let list = res.data.data;
          for (let i = 0; i < list.length; i++) {
            let page = list[i];
            for (let j = 0; j < optionsList.length; j++) {
              let app = optionsList[j];
              if (app.value == page.appId) {
                for (let k = 0; k < app.children.length; k++) {
                  let type = app.children[k];
                  if (type.value == page.pageType && app.value == page.appId) {
                    type.children.push({
                      obj: page,
                      value: page.id,
                      label: page.pageName,
                    })
                    break;
                  }
                }
                break;
              }
            }
          }
          this.options = optionsList;

          this.selectedPage = this.pageParamsSelected;
          this.pageSelectedBoxVisible = true;
        }).catch()

      }).catch((res) => {
        console.log(res)
      })
    },
    //监听地址改变
    watchPageChange() {
      this.pageTemp = this.page;
      if (this.pageTemp) {
        if (this.pageTemp.indexOf('page_id') != -1) {
          this.isSystemUrlTemp = true;
          let pageId = this.pageTemp.substring(this.page.indexOf('page_id=') + 8, this.page.indexOf('page_id=') + 27);
          // console.log("得到id", pageId)
          getObj(pageId).then(res => {
            // console.log("res",res)
            this.pageNameTemp = res.data.data.pageName
          }).catch()
        }
      }
    },
    setPagePath(url) {
      this.pageTemp = url;
    },
    handleSubmit() {
    },
    onChangePage() {
      this.getPageByType();
      //传值给父组件
      this.pageTemp = this.pageSelected.url;
    },
    getType() {
      getTypeList().then(res => {
        let list = res.data.data;
        let list2 = pageUrls;
        for (let i = 0; i < list.length; i++) {
          for (let j = 0; j < list2.length; j++) {
            if (list[i] == list2[j].type) {
              this.pageUrls.push(list2[j]);
              break;
            }
          }
        }
      }).catch()
    },
    //拿取具体页面
    getPageByType() {
      console.log("请求参数", this.pageSelected)
      if (!this.pageSelected) {
        return;
      }
      this.pageParamsList = [];
      this.pageParamsSelected = "",//选中的页面种类
        getListByType(this.pageSelected.type).then(res => {
          console.log("拿取类型", res);
          this.pageParamsList = res.data.data;
        }).catch()
    },
    //拿取具体页面并选择
    getPageByTypeAndSelect(id) {
      if (!this.pageSelected) {
        return;
      }
      this.pageParamsList = [];
      this.pageParamsSelected = "",//选中的页面种类
        getListByType(this.pageSelected.type).then(res => {
          this.pageParamsList = res.data.data;
          for (let i = 0; i < this.pageParamsList.length; i++) {
            if (this.pageParamsList[i].id == id) {//判断u
              this.pageParamsSelected = this.pageParamsList[i] //选中类型
              return;//跳出循环
            }
          }
        }).catch()
    },
    onChangePageParams(val) {
      console.log("选中的参数", val)
      //需要传上appId什么的
      if (val) {
        this.pageTemp = this.pageTemp + "?page_id=" + val.id;
      }

      // if(list[i].type == this.initPageData.pageType){
      //     url = h5HostMobile + list[i].url + "?page_id=" + this.id + "&app_id=" + this.initPageData.appId+"&tenant_id="+this.initPageData.tenantId;
      // if('1' == this.wxApp.isComponent){
      //   url= url + '&component_appid='+this.wxApp.componentAppId;
      // }
      console.log('点击二次', val);
      console.log('this.pageTemp', this.pageTemp);
      this.$emit('change', this.pageTemp, val);
    },
    //开关改变  清空
    switchChange() {
      this.pageSelected = "";
      this.pageParamsSelected = "";
      this.pageTemp = "";
      this.pageNameTemp = "";
      this.$emit('switchChange', this.isSystemUrlTemp);
      this.$emit('change', "");
    },
    //清空值
    clearValue() {
      this.pageSelected = "";
      this.pageParamsSelected = "";
      this.pageTemp = "";
    },
    //输入值完成
    inputPageBlur() {
      console.log("输入值完成")
      this.$emit('change', this.pageTemp);
    },
    //打开页面选择框
    openPageBox() {
      this.getWxApp();

    },
    pageChange(val) {
      console.log("选中的", val)
      let url = "";

      // 检测是否为系统内置页面（tenant_id为system时不添加参数）
      if (val[0].tenantId === 'system') {
        // 系统内置页面：直接使用纯净URL，不添加任何参数
        url = val[1].url;
      } else {
        // 处理普通页面：添加完整参数
        if (val[2].pageType == "7") {
          url = val[1].url + "?bind_id=" + val[2].id + "&app_id=" + val[0].id + "&tenant_id=" + val[0].tenantId;
        } else {
          url = val[1].url + "?page_id=" + val[2].id + "&app_id=" + val[0].id + "&tenant_id=" + val[0].tenantId;
        }
        if ('1' == val[0].isComponent) {
          url = url + '&component_appid=' + val[0].componentAppId;
        }
      }

      console.log("确认了", url)
      this.pageTemp = url;
      this.pageNameTemp =val[2].pageName;
      this.pageParamsSelected = url;
      this.$emit('change', this.pageTemp, val);
      this.pageSelectedBoxVisible = false;
    },
    getName() {
      if (!this.pageNameTemp) {
        return "请选择页面"
      }
      return this.pageNameTemp
    }

  }
}
</script>

<style scoped></style>
