export const tableOption = {
  dialogDrag: true,
  border: true,
  index: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  excelBtn: true,
  printBtn: true,
  viewBtn: false,
  addBtn: false,
  delBtn: false,
  editBtn: false,
  columnBtn: false,//列的显隐按钮
  searchMenuSpan: 6,
  selection: true, // 启用多选框
  column: [
    {
      label: 'id',
      prop: 'id',
      hide: true, // 隐藏ID字段
    },
    {
      label: '是否删除',
      prop: 'ableFlag',
    },
    {
      label: '开始时间',
      sortable: true,
      prop: 'startTime',
    },
    {
      label: '结束时间',
      prop: 'endTime',
    },
    {
      label: '店铺',
      prop: 'shopId',
      dicData:[],//过滤
      filterMethod:function(value, row, column) {
        return row.shopId === value;
      }
    },
    {
      label: '场地',
      prop: 'roomId',
      dicData:[],//过滤
      filterMethod:function(value, row, column) {
        return row.roomId === value;
      }
    },
    {
      label: '头像',
      prop: 'headimgUrl',
    },

    {
      label: '用户昵称',
      prop: 'nickName',
    },
    {
      label: '手机号',
      prop: 'phone',
    },
    {
      label: '创建时间',
      prop: 'createTime',
      sortable: true,
    },
    // {
    //   label: '订单id',
    //   prop: 'orderId',
    //   rules: [
    //     {
    //       max: 32,
    //       message: '长度在不能超过32个字符'
    //     },
    //   ]
    // },
    {
      label: '微信用户id',
      prop: 'userId',
      hide: true, // 隐藏微信用户id字段
    },
    {
      label: '订单状态',
      prop: 'orderStatusDesc', // 订单状态描述
      slot: true, // 使用自定义插槽渲染状态样式
    },
    {
      label: '退款状态',
      prop: 'refundStatus', // 显示退款状态
      slot: true, // 使用自定义插槽渲染
    },
    {
      label: '购买详情',
      prop: 'purchaseDetail', // 显示购买详情
      slot: true, // 使用自定义插槽渲染
    },
  ]
}
