import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/mall/couponinfo/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/mall/couponinfo',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/mall/couponinfo/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/mall/couponinfo/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/mall/couponinfo',
        method: 'put',
        data: obj
    })
}

export function getList(query) {
    return request({
        url: '/mall/couponinfo/list',
        method: 'get',
        params: query
    })
}
