export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  addBtn:false,
  editBtn:false,
  searchShow: false,
  viewBtn: false,
  direction:'rtl',
  searchMenuSpan: 6,
  column: [
    {
      label: '应用端',
      prop: 'clientType',
      type: 'select',
      sortable: true,
      rules: [
        {
          required: true,
          message: '请选择应用类型',
          trigger: 'blur'
        },
      ],
      formslot: true,
      dicData: [{
        label: '小程序',
        value: 'MA'
      }, {
        label: 'H5',
        value: 'H5'
      }, {
        label: 'APP',
        value: 'APP'
      }]
    },
    // {
    //   label: '是否启用',
    //   prop: 'enable',
    //   type: 'radio',
    //   sortable: true,
    //   slot: true,
    //   display: false,
    //   dicData: [{
    //     label: '关闭',
    //     value: '0'
    //   }, {
    //     label: '启用',
    //     value: '1'
    //   }]
    // },

    {
      prop: 'pageComponent',
      hide: true,
      span: 24,
      formslot: true
    },
  ]
}
