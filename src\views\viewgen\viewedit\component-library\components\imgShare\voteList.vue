<template>
  <div class="voteListComponent" :style="{marginBottom: `${setData.pageMarginBottom}px`,
       marginLeft: `${setData.pageMarginLeft}px`,
       marginRight: `${setData.pageMarginRight}px`,
       marginTop: `${setData.pageMarginTop}px`}">
    <div class="content" :style="{backgroundColor: setData.background,
          paddingTop:`${setData.contentPaddingTop}px`,
          paddingBottom:`${setData.contentPaddingBottom}px`,
          paddingLeft:`${setData.contentPaddingLeft}px`,
          paddingRight:`${setData.contentPaddingRight}px`,
          borderTopLeftRadius:`${setData.topBorderRadius}px`,
          borderTopRightRadius:`${setData.topBorderRadius}px`,
          borderBottomLeftRadius:`${setData.bottomBorderRadius}px`,
          borderBottomRightRadius:`${setData.bottomBorderRadius}px`}">
      <div  class="flex align-center justify-center" v-for="(item,index) in 3" :key="index" >
            <el-avatar class="avatar" v-for="(item,index2) in 8" :key="index2" :name="item" size="mini" icon="el-icon-user-solid"></el-avatar>
      </div>
    </div>
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';

export default {
  data() {
    return {};
  },
  components: {},
  props: {
    theme: {type: Object | Array},
    setData: {type: Object | Array},
    cId: {type: Number},
    noEditor: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {
  },
  mounted() {
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
  },
  watch: {
    setData(newVal, oldVal) {
    },
    componentsList(newVal, oldVal) {          //添加的时候出发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    }
  },
  beforeDestroy() {
    // this.$root.Bus.$off('addHotSpot')
  }
};
</script>
<style lang='less' scoped>

@import '../../colorui/main.css';
@import '../../colorui/icon.css';

.voteListComponent {
  display: flex;
}
.content {
  display: inline-block;
  width: 100%;
}
.avatar_box{
  display: inline-block;
  padding: 1px 1px;
}
.avatar {
}
</style>
