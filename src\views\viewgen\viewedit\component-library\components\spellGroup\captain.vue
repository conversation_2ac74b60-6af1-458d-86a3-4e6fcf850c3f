<template>
  <div class="captainComponent" :style="{marginBottom: `${setData.pageMarginBottom}px`,marginTop: `${setData.pageMarginTop}px`}">
    <div class="cu-item" v-if="setData.tabActiveName=='1'">
      <div class="content  text-center">
        <!-- <div class="cu-avatar round bg-img lg margin-xs text-yellow groupon-user" style="background-image:url('http://thirdwx.qlogo.cn/mmopen/icTdbqWNOwNQNM53x5ZAelJrwibQhlica0ujW2M6RVvtHD3eAFBfaaibXQQYQ7Jr8cSmNCnMibAeTW7ibH5IW5YVtzvw/132');height: 80px;width: 80px;"> -->
        <div class="cu-avatar round bg-img lg margin-xs text-yellow groupon-user"
             style="background-image:url('/img/spell-group/yiwen.png');height: 80px;width: 80px;">
          <span class="cu-tag badge bg-yellow">团长</span>
        </div>
      </div>
      <div class="captain_description" :style="{color: `${setData.startDescriptionColor}`}">
       <p >{{setData.startDescription}}</p>
      </div>
      <div class="captain_button">
        <el-button  :style="{background: `${setData.startButtonBackGround}`,color: `${setData.startButtonColor}`,fontSize: `${setData.startButtonFont}px`}"  style="width: 60%;">{{ setData.startButtonTitle }}</el-button>
      </div>
    </div>
    <div class="cu-item" v-else-if="setData.tabActiveName=='2'">
      <div class="content  text-center">
        <!-- <div class="cu-avatar round bg-img lg margin-xs text-yellow groupon-user" style="background-image:url('http://thirdwx.qlogo.cn/mmopen/icTdbqWNOwNQNM53x5ZAelJrwibQhlica0ujW2M6RVvtHD3eAFBfaaibXQQYQ7Jr8cSmNCnMibAeTW7ibH5IW5YVtzvw/132');height: 80px;width: 80px;"> -->
        <div class="cu-avatar round bg-img lg margin-xs text-yellow groupon-user"
             style="background-image:url('/img/spell-group/yiwen.png');height: 80px;width: 80px;">
          <span class="cu-tag badge bg-yellow">团长</span>
        </div>
      </div>
      <div class="captain_description" :style="{color: `${setData.proceedDescriptionColor}`}">
       <p >{{setData.proceedDescription}}</p>
      </div>
      <div class="captain_button">
        <el-button  :style="{background: `${setData.proceedButtonBackGround}`,color: `${setData.proceedButtonColor}`,fontSize: `${setData.proceedButtonFont}px`}"  style="width: 60%;">{{ setData.proceedButtonTitle }}</el-button>
      </div>
    </div>
    <div class="cu-item" v-else-if="setData.tabActiveName=='3'">
      <div class="content  text-center">
        <!-- <div class="cu-avatar round bg-img lg margin-xs text-yellow groupon-user" style="background-image:url('http://thirdwx.qlogo.cn/mmopen/icTdbqWNOwNQNM53x5ZAelJrwibQhlica0ujW2M6RVvtHD3eAFBfaaibXQQYQ7Jr8cSmNCnMibAeTW7ibH5IW5YVtzvw/132');height: 80px;width: 80px;"> -->
        <div class="cu-avatar round bg-img lg margin-xs text-yellow groupon-user"
             style="background-image:url('/img/spell-group/yiwen.png');height: 80px;width: 80px;">
          <span class="cu-tag badge bg-yellow">团长</span>
        </div>
      </div>
      <div class="captain_description" :style="{color: `${setData.endDescriptionColor}`}">
       <p >{{setData.endDescription}}</p>
      </div>
      <div class="captain_button">
        <el-button  :style="{background: `${setData.endButtonBackGround}`,color: `${setData.endButtonColor}`,fontSize: `${setData.endButtonFont}px`}"  style="width: 60%;">{{ setData.endButtonTitle }}</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';


export default {
  data() {
    return {};
  },
  components: {},
  props: {
    theme: {type: Object | Array},
    setData: {type: Object | Array},
    cId: {type: Number},
    noEditor: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {
  },
  mounted() {
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
  },
  watch: {
    setData(newVal, oldVal) {
    },
    componentsList(newVal, oldVal) {          //添加的时候出发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    }
  },
  beforeDestroy() {
    // this.$root.Bus.$off('addHotSpot')
  }
};
</script>
<style lang='less' scoped>

@import '../../colorui/main.css';
@import '../../colorui/icon.css';

.captain_description {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}
.captain_button {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
