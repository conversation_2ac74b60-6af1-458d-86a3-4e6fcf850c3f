<!--统一的素材库组件-->
<template>
  <div v-if="type == 'image'">
    <ul class="el-upload-list el-upload-list--picture-card" v-for="(item,index) in value" :key="index">
      <li tabindex="0" class="el-upload-list__item is-ready" :style="divStyle?divStyle:'width: '+width+'px;height: '+height+'px'" >
        <div>
          <img :src="item" alt="" class="el-upload-list__item-thumbnail">
          <span class="el-upload-list__item-actions">
            <span class="el-upload-list__item-preview" v-if="index != 0" @click="moveMaterial(index,'up')">
              <i class="el-icon-back"></i>
            </span>
            <span class="el-upload-list__item-preview" @click="zoomMaterial(index)">
              <i class="el-icon-view"></i>
            </span>
            <span class="el-upload-list__item-delete" @click="deleteMaterial(index)">
              <i class="el-icon-delete"></i>
            </span>
            <span class="el-upload-list__item-preview" v-if="index != value.length-1" @click="moveMaterial(index,'down')">
              <i class="el-icon-right"></i>
            </span>
          </span>
        </div>
      </li>
    </ul>
    <div tabindex="0" class="el-upload el-upload--picture-card" v-if="num > value.length" @click="toSeleteMaterial"
         :style="divStyle?divStyle:'width: '+width+'px;height: '+height+'px;'+'line-height:'+height+'px;'">
      <i class="el-icon-plus"></i>
    </div>

    <el-dialog
      append-to-body
      :visible.sync="dialogVisible"
      width="35%">
      <img :src="url" alt="" style="width: 100%">
    </el-dialog>

    <el-dialog
      title="素材库"
      append-to-body
      :visible.sync="listDialogVisible"
      width="70%">
      <div style="margin-bottom: 10px">
        <el-radio-group v-model="libraryType" @change="handleLibraryTypeChange">
          <el-radio :label="'system'">系统素材库</el-radio>
          <el-radio :label="'wechat'">微信素材库</el-radio>
        </el-radio-group>
      </div>
      <!-- 微信公众号选择器，仅当选择微信素材库时显示 -->
      <div style="margin-bottom: 5px" v-if="libraryType === 'wechat'">
        <el-select v-model="appId" placeholder="请选择公众号" @change="changeAppInfo">
          <el-option
            v-for="item in appList"
            :key="item.id"
            :label="item.name"
            :value="item.id">
          </el-option>
        </el-select>
      </div>
      <el-container>
        <el-aside width="unset">
          <div style="margin-bottom: 10px;margin-top: 20px;">
            <el-button
              class="el-icon-plus"
              size="small"
              @click="materialgroupAdd()">
              添加分组
            </el-button>
          </div>
          <el-tabs style="height: 400px;" tab-position="left" v-model="materialgroupObjId" v-loading="materialgroupLoading" @tab-click="tabClick">
            <el-tab-pane v-for="(item,index) in materialgroupList"
                         :key="index"
                         :name="item.id">
              <span slot="label"> {{item.name}}</span>
            </el-tab-pane>
          </el-tabs>
        </el-aside>
        <el-main>
          <el-card>
            <div slot="header">
              <el-row>
                <el-col :span="12">
                  <span>{{materialgroupObj.name}}</span>
                  <span v-if="materialgroupObj.id != '-1'">
                    <el-button size="small" type="text" class="el-icon-edit" style="margin-left: 10px;" @click="materialgroupEdit(materialgroupObj)">重命名</el-button>
                    <el-button size="small" type="text" class="el-icon-delete" style="margin-left: 10px;color: red" @click="materialgroupDelete(materialgroupObj)">删除</el-button>
                  </span>
                </el-col>
                <el-col :span="12" style="text-align: right;">
                  <el-upload
                    action="/upms/file/upload?fileType=image&dir=material/"
                    :headers="headers"
                    :file-list="[]"
                    :limit="libraryType === 'wechat' ? 10 : 3"
                    :multiple="libraryType === 'wechat'"
                    :on-progress="handleProgress"
                    :on-exceed="handleExceed"
                    :before-upload="beforeUpload"
                    :on-success="handleSuccess"
                    :on-error="handleError">
                    <el-button size="small" type="primary">点击上传</el-button>
                  </el-upload>
                </el-col>
              </el-row>
            </div>
            <div v-loading="tableLoading">
              <el-alert
                v-if="tableData.length <= 0"
                title="暂无数据"
                type="info"
                :closable="false"
                center
                show-icon>
              </el-alert>
              <el-row :gutter="5">
                <el-checkbox-group v-model="urls" :max="num - value.length">
                  <el-col :span="4" v-for="(item,index) in tableData" :key="index">
                  <el-card :body-style="{ padding: '5px' }">
                    <el-image
                      style="width: 100%;height: 200px"
                      :src="item.url"
                      fit="contain"
                      :preview-src-list="[item.url]"></el-image>
                    <div>
                      <el-checkbox class="material-name" :label="item.url">{{item.name}}</el-checkbox>
                      <el-row class="compile">
                        <el-col :span="6" class="col-do">
                          <el-button type="text" class="button-do" @click="materialRename(item)">改名</el-button>
                        </el-col>
                        <el-col :span="6" class="col-do">
                          <el-button type="text" class="button-do" @click="materialUrl(item)">链接</el-button>
                        </el-col>
                        <el-col :span="6" class="col-do">
                          <el-dropdown trigger="click" @command="handleCommand">
                            <el-button type="text" class="button-do">分组<i class="el-icon-arrow-down"></i></el-button>
                            <el-dropdown-menu slot="dropdown">
                              <el-dropdown-item v-for="(item2,index) in materialgroupList"
                                                v-if="index > 0"
                                                :key="index"
                                                :command="item.id + '-' + item2.id"
                                                :disabled="item.groupId == item2.id">{{item2.name}}</el-dropdown-item>
                            </el-dropdown-menu>
                          </el-dropdown>
                        </el-col>
                        <el-col :span="6" class="col-do">
                          <el-button type="text" class="button-do" style="color: red" @click="materialDel(item)">删除</el-button>
                        </el-col>
                      </el-row>
                    </div>
                  </el-card>
                </el-col>
                </el-checkbox-group>
              </el-row>
              <el-pagination
                @size-change="sizeChange"
                @current-change="currentChange"
                :current-page.sync="page.currentPage"
                :page-sizes="[12, 24]"
                :page-size="page.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="page.total"
                class="pagination" style="margin-top: 20px;">
              </el-pagination>
            </div>
          </el-card>
        </el-main>
      </el-container>
      <span slot="footer" class="dialog-footer">
        <el-button @click="listDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="sureUrls">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  // 系统素材库API
  import { getPage as systemMaterialgroupPage, addObj as systemMaterialgroupAdd, delObj as systemMaterialgroupDel, putObj as systemMaterialgroupEdit} from '@/api/mall/materialgroup'
  import { getPage as systemMaterialPage, addObj as systemMaterialAdd, delObj as systemMaterialDel, putObj as systemMaterialPut} from '@/api/mall/material'
  
  // 微信素材库API
  import { getPage as wxMaterialgroupPage, addObj as wxMaterialgroupAdd, delObj as wxMaterialgroupDel, putObj as wxMaterialgroupEdit} from '@/api/viewgen/wxmaterialgroup'
  import { getPage as wxMaterialPage, addObjList as wxMaterialAddList, delObj as wxMaterialDel, putObj as wxMaterialPut} from '@/api/viewgen/wxmaterial'
  import { getList as wxAppList} from '@/api/wxmp/wxapp'
  
  import store from "@/store"
  import {mapGetters} from 'vuex'
  import { getStore } from '@/util/store'
  import { judgePlatformPage } from '@/util/util'

  export default {
    name: "materialLibrary",
    props: {
      // 系统素材库参数
      shopId: {
        type: String,
      },
      // 微信素材库参数
      selectedAppId: {
        type: Array,
        default() {
          return []
        },
      },
      // 共用参数
      value: {
        type: Array,
        default() {
          return []
        },
      },
      type: {
        type: String,
      },
      divStyle: {
        type: String
      },
      num: {
        type: Number,
        default() {
          return 5
        },
      },
      width: {
        type: Number,
        default() {
          return 150
        }
      },
      height: {
        type: Number,
        default() {
          return 150
        }
      },
      // 新增参数：默认显示的素材库类型
      defaultLibraryType: {
        type: String,
        default: 'system' // 'system' 或 'wechat'
      }
    },
    watch: {
    },
    data() {
      return {
        headers: {
          Authorization: 'Bearer ' + store.getters.access_token
        },
        saveList: [], // oss保存成功的list（仅微信素材库使用）
        dialogVisible: false,
        url: '',
        listDialogVisible: false,
        materialgroupList: [],
        materialgroupObjId: '',
        materialgroupObj: {},
        materialgroupLoading: false,
        tableData: [],
        page: {
          total: 0, // 总页数
          currentPage: 1, // 当前页数
          pageSize: 12, // 每页显示多少条
          ascs: [],//升序字段
          descs: 'create_time'//降序字段
        },
        tableLoading: false,
        groupId: null,
        urls: [],
        // 微信公众号相关
        appList: [],
        appId: null,
        // 素材库类型 'system'系统素材库 'wechat'微信素材库
        libraryType: 'system'
      }
    },
    created() {
      if(this.userInfo.type == '-1'){
        let switchTenantId = getStore({ name: 'switchTenantId' })
        if(switchTenantId && !judgePlatformPage()){
          this.headers['switch-tenant-id'] = switchTenantId
        }
      }
      // 设置默认素材库类型
      this.libraryType = this.defaultLibraryType
    },
    computed: {
      ...mapGetters(['userInfo'])
    },
    methods: {
      // 素材库类型切换处理
      handleLibraryTypeChange() {
        this.materialgroupList = []
        this.materialgroupObjId = ''
        this.materialgroupObj = {}
        this.tableData = []
        this.urls = []
        this.page.currentPage = 1
        this.page.total = 0
        
        if (this.libraryType === 'wechat') {
          this.getAppList()
        } else {
          this.materialgroupPage()
        }
      },
      
      // 微信公众号列表获取
      getAppList() {
        wxAppList().then(res => {
          let appList = res.data;
          let type = store.getters.userInfo.type
          if(type == '1'){
            appList.unshift({
              id: '-1',
              name: '微信公共素材'
            })
          }
          this.appList = appList
          if(appList.length > 0){
            // 如果有selectedAppId，优先使用
            if (this.selectedAppId && this.selectedAppId.length > 0) {
              this.changeAppInfo(this.selectedAppId[0])
            } else {
              this.changeAppInfo(appList[0].id)
            }
          }
        })
      },
      
      // 微信公众号变更
      changeAppInfo(e) {
        this.appId = e
        this.materialgroupPage()
      },
      
      // 分页控制
      currentChange(currentPage) {
        this.page.currentPage = currentPage
        this.getPage(this.page)
      },
      
      // 图片移动
      moveMaterial(index, type) {
        if(type == 'up'){
          let tempOption = this.value[index - 1]
          this.$set(this.value, index - 1, this.value[index])
          this.$set(this.value, index, tempOption)
        }
        if(type == 'down'){
          let tempOption = this.value[index + 1]
          this.$set(this.value, index + 1, this.value[index])
          this.$set(this.value, index, tempOption)
        }
      },
      
      // 图片预览
      zoomMaterial(index) {
        this.dialogVisible = true
        this.url = this.value[index]
      },
      
      // 删除图片
      deleteMaterial(index) {
        let that = this
        this.$confirm('是否确认删除？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function() {
          that.value.splice(index, 1)
          that.urls = [];
          that.$emit('deleteMaterial', that.urls) // 点击确认后的回调
        })
      },
      
      // 打开素材选择弹窗
      toSeleteMaterial() {
        if (this.libraryType === 'wechat') {
          console.log("选中的", this.selectedAppId)
          this.getAppList();
        }
        this.listDialogVisible = true
        if(this.tableData.length <= 0){
          this.materialgroupPage()
        }
      },
      
      // 获取分组数据
      materialgroupPage() {
        this.materialgroupLoading = true
        const params = {
          total: 0,
          current: 1,
          size: 999,
          ascs: [],
          descs: 'create_time'
        }
        
        // 根据素材库类型选择不同API
        if (this.libraryType === 'wechat') {
          params.appId = this.appId
          wxMaterialgroupPage(params).then(response => {
            this.handleMaterialgroupResponse(response)
          })
        } else {
          params.shopId = this.shopId
          systemMaterialgroupPage(params).then(response => {
            this.handleMaterialgroupResponse(response)
          })
        }
      },
      
      // 处理分组数据响应
      handleMaterialgroupResponse(response) {
        this.materialgroupLoading = false
        let materialgroupList = response.data.data.records
        materialgroupList.unshift({
          id: '-1',
          name: '全部分组'
        })
        this.materialgroupList = materialgroupList
        this.tabClick({
          index: 0
        })
      },
      
      // 删除分组
      materialgroupDelete(materialgroupObj) {
        let that = this
        this.$confirm('是否确认删除该分组？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function() {
          const delFunc = that.libraryType === 'wechat' ? wxMaterialgroupDel : systemMaterialgroupDel
          delFunc(materialgroupObj.id)
            .then(function() {
              that.$delete(that.materialgroupList, materialgroupObj.index)
            })
        })
      },
      
      // 编辑分组名称
      materialgroupEdit(materialgroupObj) {
        let that = this
        this.$prompt('请输入分组名', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValue: materialgroupObj.name,
        }).then(({ value }) => {
          const editFunc = that.libraryType === 'wechat' ? wxMaterialgroupEdit : systemMaterialgroupEdit
          editFunc({
            id: materialgroupObj.id,
            name: value
          }).then(function() {
            materialgroupObj.name = value
            that.$set(that.materialgroupList, materialgroupObj.index, materialgroupObj)
          })
        }).catch(() => {
        })
      },
      
      // 添加分组
      materialgroupAdd() {
        let that = this
        this.$prompt('请输入分组名', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /[\S]/,
          inputErrorMessage: '分组名不能为空'
        }).then(({ value }) => {
          const addFunc = that.libraryType === 'wechat' ? wxMaterialgroupAdd : systemMaterialgroupAdd
          const params = {
            name: value
          }
          
          // 根据素材库类型设置不同参数
          if (that.libraryType === 'wechat') {
            params.appId = that.appId
          } else {
            params.shopId = that.shopId
          }
          
          addFunc(params).then(function() {
            that.materialgroupPage()
          })
        }).catch(() => {
        })
      },
      
      // 分组切换
      tabClick(tab, event) {
        this.urls = []
        let index = Number(tab.index)
        let materialgroupObj = this.materialgroupList[index]
        materialgroupObj.index = index
        this.materialgroupObj = materialgroupObj
        this.materialgroupObjId = materialgroupObj.id
        this.page.currentPage = 1
        this.page.total = 0
        if(materialgroupObj.id != '-1'){
          this.groupId = materialgroupObj.id
        }else{
          this.groupId = null
        }
        this.getPage(this.page)
      },
      
      // 获取素材数据
      getPage(page, params) {
        this.tableLoading = true
        const queryParams = Object.assign({
          current: page.currentPage,
          size: page.pageSize,
          descs: this.page.descs,
          ascs: this.page.ascs,
          groupId: this.groupId
        }, {})
        
        // 根据素材库类型设置参数和选择API
        if (this.libraryType === 'wechat') {
          queryParams.appId = this.appId
          wxMaterialPage(queryParams).then(response => {
            this.handlePageResponse(response)
          }).catch(() => {
            this.tableLoading = false
          })
        } else {
          queryParams.shopId = this.shopId
          systemMaterialPage(queryParams).then(response => {
            this.handlePageResponse(response)
          }).catch(() => {
            this.tableLoading = false
          })
        }
      },
      
      // 处理分页数据响应
      handlePageResponse(response) {
        let tableData = response.data.data.records
        this.page.total = response.data.data.total
        this.page.currentPage = this.page.currentPage
        this.page.pageSize = this.page.pageSize
        this.tableData = tableData
        this.tableLoading = false
      },
      
      // 改变每页显示条数
      sizeChange(val) {
        this.page.currentPage = 1
        this.page.pageSize = val
        this.getPage(this.page)
      },
      
      // 重命名素材
      materialRename(item) {
        let that = this
        this.$prompt('请输入素材名', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValue: item.name,
        }).then(({ value }) => {
          const putFunc = that.libraryType === 'wechat' ? wxMaterialPut : systemMaterialPut
          putFunc({
            id: item.id,
            name: value
          }).then(function() {
            that.getPage(that.page)
          })
        }).catch(() => {
        })
      },
      
      // 获取素材URL
      materialUrl(item) {
        this.$prompt('素材链接', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValue: item.url,
        }).then(({ value }) => {
        }).catch(() => {
        })
      },
      
      // 删除素材
      materialDel(item) {
        let that = this
        this.$confirm('是否确认删除该素材？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function() {
          const delFunc = that.libraryType === 'wechat' ? wxMaterialDel : systemMaterialDel
          delFunc(item.id)
            .then(function() {
              let index = that.urls.indexOf(item.url)
              if(index != -1){
                that.urls.splice(index, 1)
              }
              that.getPage(that.page)
            })
        })
      },
      
      // 处理分组修改
      handleCommand(command) {
        let that = this
        let s = command.split('-')
        const putFunc = that.libraryType === 'wechat' ? wxMaterialPut : systemMaterialPut
        putFunc({
          id: s[0],
          groupId: s[1]
        }).then(function() {
          that.getPage(that.page)
        })
      },
      
      // 上传进度处理
      handleProgress(event, file, fileList) {
        // let uploadProgress = file.percentage.toFixed(0)
        // this.uploadProgress = uploadProgress
      },
      
      // 上传成功处理
      handleSuccess(response, file, fileList) {
        let that = this
        this.uploadProgress = 0
        
        if (this.libraryType === 'wechat') {
          // 微信素材库批量上传处理
          console.log("file", file)
          console.log("fileList", fileList)
          this.saveList.push(file);
          let list = [];
          if (this.saveList.length == fileList.length) {
            for (let i = 0; i < this.saveList.length; i++) {
              let obj = {
                type: '1',
                groupId: this.groupId != '-1' ? this.groupId : null,
                name: this.saveList[i].name,
                url: this.saveList[i].response.link,
                appId: this.appId
              }
              list.push(obj);
            }
            wxMaterialAddList({
              list: list
            }).then(() => {
              this.saveList = [];
              that.getPage(that.page)
            })
          }
        } else {
          // 系统素材库单个上传处理
          systemMaterialAdd({
            shopId: this.shopId,
            type: '1',
            groupId: this.groupId != '-1' ? this.groupId : null,
            name: file.name,
            url: response.link
          }).then(function() {
            that.getPage(that.page)
          })
        }
      },
      
      // 上传错误处理
      handleError(err, file, fileList) {
        this.$message.error(err + '')
      },
      
      // 上传前检查
      beforeUpload(file) {
        const isPic =
          file.type === "image/jpeg" ||
          file.type === "image/png" ||
          file.type === "image/gif" ||
          file.type === "image/jpg"
        const isLt1M = file.size / 1024 / 1024 < 1
        if (!isPic) {
          this.$message.error("上传图片只能是 JPG、JPEG、PNG、GIF 格式!")
          return false
        }
        if (!isLt1M) {
          this.$message.error('上传头像图片大小不能超过 1MB!')
        }
        return isPic && isLt1M
      },
      
      // 超出上传数量限制处理
      handleExceed(files, fileList) {
        if (this.libraryType === 'wechat') {
          this.$message.warning('上传一次性限制选择 10 个文件，本次选择了 ' + files.length + '个文件');
        } else {
          this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
        }
      },
      
      // 确认选择的素材
      sureUrls() {
        this.urls.forEach(item => {
          this.$set(this.value, this.value.length, item)
        })
        this.$emit('sureSuccess', this.urls) // 点击确认后的回调
        this.listDialogVisible = false
      }
    }
  };
</script>

<style lang="scss" scoped>
  .material-name{
    padding: 0px 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    height: 20px;
    font-size: 13px;
    margin-top: 10px;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }

  .compile{
    padding-top: 10px;
    padding-bottom: 10px;
  }

  .col-do{
    text-align: center;
  }

  .button-do{
    padding: unset!important;
    font-size: 12px;
  }
</style> 