<template>
  <div class="functionButtonSetting">
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">页面分享</p>
      <div slot="mainContent">
        <el-form ref="form" label-width="100px" :model="formData">
          <el-divider>基础属性</el-divider>
          <el-form-item label="上边界">
            <el-input v-model="formData.pageMarginTop" size="mini"  :mini="0" type="number" style="margin-top: 5px" placeholder="组件内部与上边界的距离">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="下边界">
            <el-input v-model="formData.pageMarginBottom" size="mini" :mini="0" type="number" style="margin-top: 5px"
                      placeholder="组件内部与下边界的距离">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="左边界">
            <el-input v-model="formData.pageMarginLeft" size="mini" :mini="0" type="number" style="margin-top: 5px"
                      placeholder="组件内部与左边界的间隔">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="右边界">
            <el-input v-model="formData.pageMarginRight" size="mini" :mini="0" type="number" style="margin-top: 5px"
                      placeholder="组件内部与右边界的间隔">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="字体颜色">
            <el-input v-model="formData.fontColor" size="small" style="margin-top: 5px">
              <template slot="append">
                <el-color-picker size="mini" v-model="formData.fontColor"></el-color-picker>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="背景颜色">
            <el-input v-model="formData.backColor" size="small" style="margin-top: 5px">
              <template slot="append">
                <el-color-picker size="mini" v-model="formData.backColor"></el-color-picker>
              </template>
            </el-input>
          </el-form-item>
          <el-divider>风格设置</el-divider>
          <el-form-item label="风格选择">
            <el-radio-group v-model="formData.showType">
              <el-radio :label="0">经典风格</el-radio>
              <el-radio :label="1">简洁款式</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="上圆角设置">
            <el-slider v-model="formData.topBorderRadius" :min="0" :max="40"></el-slider>
          </el-form-item>
          <el-form-item label="下圆角设置">
            <el-slider v-model="formData.bottomBorderRadius" :min="0" :max="40"></el-slider>
          </el-form-item>
          <div v-if="formData.showType==1" >
            <el-form-item label="头像大小">
              <el-slider v-model="formData.avatarSize" :min="40" :max="120"></el-slider>
            </el-form-item>
          </div>
          <el-divider>内容设置</el-divider>
          <el-form-item  label="内部上边界">
            <el-input v-model="formData.insideMarginTop" size="mini"  :mini="0" type="number" style="margin-top: 5px" placeholder="组件内部与上边界的距离">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item  label="内部下边界">
            <el-input v-model="formData.insideMarginBottom" size="mini"  :mini="0" type="number" style="margin-top: 5px" placeholder="组件内部与上边界的距离">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item  label="内部上距">
            <el-input v-model="formData.insidePaddingTop" size="mini"  :mini="0" type="number" style="margin-top: 5px" placeholder="组件内部与上边界的距离">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item  label="内部下距">
            <el-input v-model="formData.insidePaddingBottom" size="mini"  :mini="0" type="number" style="margin-top: 5px" placeholder="组件内部与上边界的距离">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="首行介绍">
            <el-input v-model="formData.title" size="mini" placeholder="标题"></el-input>
          </el-form-item>
          <el-form-item label="分享介绍">
            <el-input v-model="formData.describe" size="mini" placeholder="标题"></el-input>
          </el-form-item>
          <el-form-item label="备注说明">
            <el-input v-model="formData.remark" size="mini" placeholder="标题"></el-input>
          </el-form-item>
          <el-form-item label="版权说明">
            <el-input v-model="formData.copyright" size="mini" placeholder="标题"></el-input>
          </el-form-item>

        </el-form>
      </div>
    </settingSlot>
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';
import draggable from "vuedraggable";
import settingSlot from '../settingSlot'
import store from "@/store";
// import bgColorSelect from "../../pages/page-components/bgColorSelect";
import wxPageSelect from '@/components/wx-page-select/Index.vue'
export default {
  components: {settingSlot,draggable,wxPageSelect},
  data() {
    return {
      formDataCopy: {
        pageMarginTop: 0,
        pageMarginBottom: 0,
        pageMarginLeft: 0,
        pageMarginRight: 0,
        pagePaddingTop: 0,
        pagePaddingBottom: 0,
        fontColor: '#f53f3f',
        backColor: '#FFFFFF',
        showType: 0,//展示风格
        topBorderRadius: 0,//上部圆角
        bottomBorderRadius: 0,//下部圆角
        avatarSize: 80,//头像大小

        insidePaddingBottom:0,
        insidePaddingTop:0,
        insideMarginBottom:0,
        insideMarginTop:0,
        title: "#客照抢先看#",
        describe: "正在分享宝宝的抢先看",
        remark: "滑至底部为TA的宝宝送上祝福吧！",
        copyright: "COPYRIGHT ©BY SIMLEKIDS",//版权说明
      },
      formData: {},
      header: {Authorization: 'Bearer ' + store.getters.access_token},
      actionUrl: '/upms/file/upload?fileType=image&dir=wx/spellGroup/',
      uploadIndex: '',
      addFontButtonVisible: true,
      addImgButtonVisible: true
    };
  },
  props: {
    thememobile: {type: Object | Array},
    showData: {
      type: Object,
      default: () => {
      }
    },
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
      clickComIndex: state => state.divpage.clickComIndex,
    })
  },
  mounted() {
    let that = this;
    if (that.IsEmptyObj(that.showData)) {

      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
    } else {
      that.formData = that.showData
    }
    that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
    // that.updateData({
    //   componentsList: that.componentsList
    // })
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    // 删除按钮
    delBtn(index) {
      let that = this;
      that.$confirm('是否删除该按钮?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
        that.updateData({componentsList: that.componentsList});
      }).catch(() => {
      })
    },
    cancel() {
      this.$emit('cancel')
    },
    reset() {
      let that = this;
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      that.componentsList[that.clickComIndex]['data'] = this.formData;
    },
    confirm() {
      this.$emit('confirm', this.formData)
    },
    delete() {
      this.$emit('delete')
    },
    handleClick(tab, event) {
      this.formData.tabActiveName = tab.name;
    },
    addFontButton() {
      let obj = {
        width: 40,
        title: "立即购买",
        imgUrl: "",
        showType: 1,
        fontColor: '#FFFFFF',
        backColor: '#f53f3f',
        fontSize: 15,
        fontSpacing: 0,
        fontWeight: false,
      };
      this.formData.buttonList.push(obj);
      if( this.formData.buttonList.length>=4){
        this.addFontButtonVisible = false;
        this.addImgButtonVisible = false;
      }else{
        this.addFontButtonVisible = true;
        this.addImgButtonVisible = true;
      }
    },
    addImgButton() {
      this.formData.buttonList.push({
        showType: 2,
        width: 30,
        imgUrl: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
        buttonType: 1,
      });
      if( this.formData.buttonList.length>=4){
        this.addFontButtonVisible = false;
        this.addImgButtonVisible = false;
      }else{
        this.addFontButtonVisible = true;
        this.addImgButtonVisible = true;
      }
    },
    delButton(index) {
      this.formData.buttonList.splice(index, 1);
      if( this.formData.buttonList.length>=4){
        this.addFontButtonVisible = false;
        this.addImgButtonVisible = false;
      }else{
        this.addFontButtonVisible = true;
        this.addImgButtonVisible = true;
      }
    },
    // 拖动的事件等等=======================================================>
    datadragStart(e) {
      let that = this;
      that.updateData({clickComIndex: null})
      that.showSetBlock = false;
    },
    datadragUpdate(e) {
      e.preventDefault();
    },
    datadragEnd(e) {
      // console.log(e, "拖动结束");
    },
    datadragMove(e, originalEve) {
      return (e.draggedContext.element.text !== 'Gold（不可拖动元素）');
    },
    // 拖动的事件等等=======================================================>

    //按钮的图片上传
    handleAvatarSuccess(res, file) {
      this.$set(this.formData.buttonList[this.uploadIndex],"imgUrl",res.link)
      // this.imageUrl = URL.createObjectURL(file.raw);
    },
    //弹窗图片的上传
    handleAvatar2Success(res, file) {
      this.$set(this.formData.buttonList[this.uploadIndex],"popupImgUrl",res.link)
      // this.imageUrl = URL.createObjectURL(file.raw);
    },
    beforeAvatarUpload(file,index) {
      const isJPG = file.type === 'image/jpeg';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!');
      }
      this.uploadIndex = index;
      return isJPG && isLt2M;
    }
  },

  watch: {
    showData: {
      handler(newVal, oldVal) {
        this.formData = newVal ? newVal : this.formData;
      },
      deep: true
    },
    thememobile() {
    },
  }
};
</script>
<style lang='less' scoped>

.el-form-item {
  margin-bottom: 0;
}

</style>
