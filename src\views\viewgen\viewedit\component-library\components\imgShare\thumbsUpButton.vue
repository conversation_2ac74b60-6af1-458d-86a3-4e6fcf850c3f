<template>
  <div class="thumbsUpButtonComponent"
       :style="{marginBottom: `${setData.pageMarginBottom}px`,marginTop: `${setData.pageMarginTop}px`,marginLeft: `${setData.pageMarginLeft}px`,marginRight: `${setData.pageMarginRight}px`}">
    <div style="display: flex"
      :style="{'justify-content':`${setData.location}`, background:`${setData.backColor}`}">
      <div v-if="setData.type ==0" :style="{width:`${setData.size=='100%'?'100%':''}`, paddingBottom: `${setData.contentPaddingBottom}px`,paddingTop: `${setData.contentPaddingTop}px`,paddingLeft: `${setData.contentPaddingLeft}px`,paddingRight: `${setData.contentPaddingRight}px`}">
        <el-button
          style="display: inline-block"
          :size="setData.size!='100%'?setData.size:''"
          :style="{width:`${setData.size=='100%'?'100%':''}`,
                color:`${setData.fontColor}`,
                fontWeight:`${setData.fontWeight}`,
                fontSize:`${setData.fontSize}px`,
                letterSpacing:`${setData.spacing}px`,
                borderRadius: `${setData.borderRadius}px`,
                background:`${setData.buttonColor}`,}">{{ setData.content }}
        </el-button>
      </div>
      <div v-if="setData.type ==1"
           :style="{ paddingBottom: `${setData.contentPaddingBottom}px`,paddingTop: `${setData.contentPaddingTop}px`,paddingLeft: `${setData.contentPaddingLeft}px`,paddingRight: `${setData.contentPaddingRight}px`}"
           style="width: 100%;align-items: center;align-items: center">
        <div
          :style="{
                color:`${setData.fontColor}`,
                background:`${setData.backColor}`,}">
          <el-image  :src="setData.imgUrl"
                    :style="{borderRadius: `${setData.borderRadius}px`}"
                    style="height: 200px;display: flex;align-items: center;justify-content: center;background-color: transparent">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline">图片展示</i>
            </div>
          </el-image>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';

export default {
  data() {
    return {};
  },
  components: {},
  props: {
    theme: {type: Object | Array},
    setData: {type: Object | Array},
    cId: {type: Number},
    noEditor: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {
  },
  mounted() {
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
  },
  watch: {
    setData(newVal, oldVal) {
    },
    componentsList(newVal, oldVal) {          //添加的时候出发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    }
  },
  beforeDestroy() {
    // this.$root.Bus.$off('addHotSpot')
  }
};
</script>
<style lang='less' scoped>

@import '../../colorui/main.css';
@import '../../colorui/icon.css';

.thumbsUpButtonComponent {

}

.content {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

</style>
