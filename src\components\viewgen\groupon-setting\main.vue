<template>
  <div>
      <el-form ref="form" class="page_setting" :model="grouponInfo" label-width="80px">
        <el-divider>基础属性</el-divider>
        <el-form-item label="商品名称">
          <el-input v-model="grouponInfo.goodsName" size="mini" placeholder="用户购买后在支付记录显示的名称"></el-input>
        </el-form-item>
        <el-form-item label="活动时间">
          <el-date-picker
            v-model="grouponTime"
            size="mini"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            :picker-options="pickerOptions"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="成团人数">
          <el-radio-group v-model="grouponInfo.number">
            <el-radio :label="2">2人</el-radio>
            <el-radio :label="3">3人</el-radio>
            <el-radio :label="4">4人</el-radio>
            <el-radio :label="5">5人</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="拼团价格">
          <el-input-number v-model="grouponInfo.price" type="number" :precision="2" :min="0" :max="99999999"  size="mini"></el-input-number>
        </el-form-item>
        <el-form-item label="单买价格">
          <el-input-number v-model="grouponInfo.soloPrice" type="number" :precision="2" :min="0" :max="99999999"  size="mini"></el-input-number>
        </el-form-item>
        <el-divider>标签设置</el-divider>
        <el-form-item label="唤起支付">
            <el-button @click="openTagBox('prePayTagId')" v-show="!grouponInfo.prePayTag" icon="el-icon-plus" size="mini"></el-button>
            <el-tag @click="openTagBox('prePayTagId')" v-show="grouponInfo.prePayTag"  size="medium" :color="grouponInfo.prePayTag?grouponInfo.prePayTag.backColor:''"
                    :style="grouponInfo.prePayTag?getFontColor(grouponInfo.prePayTag.fontColor):''">{{ grouponInfo.prePayTag?grouponInfo.prePayTag.name:'' }} </el-tag>
            <el-button v-show="grouponInfo.prePayTag" @click="deleteUserTag('prePayTagId')"  type="danger" icon="el-icon-delete" size="mini"></el-button>
        </el-form-item>
        <el-form-item label="成功支付">
            <el-button  @click="openTagBox('sucPayTagId')" v-show="!grouponInfo.sucPayTag" icon="el-icon-plus" size="mini"></el-button>
            <el-tag  @click="openTagBox('sucPayTagId')" v-show="grouponInfo.sucPayTag"  size="medium" :color="grouponInfo.sucPayTag?grouponInfo.sucPayTag.backColor:''"
                    :style="grouponInfo.sucPayTag?getFontColor(grouponInfo.sucPayTag.fontColor):''">{{ grouponInfo.sucPayTag?grouponInfo.sucPayTag.name:'' }} </el-tag>
            <el-button v-show="grouponInfo.sucPayTag" @click="deleteUserTag('sucPayTagId')"  type="danger" icon="el-icon-delete" size="mini"></el-button>
        </el-form-item>
        <el-form-item label="成功成团">
            <el-button @click="openTagBox('sucGroupTagId')" v-show="!grouponInfo.sucGroupTag" icon="el-icon-plus" size="mini"></el-button>
            <el-tag @click="openTagBox('sucGroupTagId')" v-show="grouponInfo.sucGroupTag"  size="medium" :color="grouponInfo.sucGroupTag?grouponInfo.sucGroupTag.backColor:''"
                    :style="grouponInfo.sucGroupTag?getFontColor(grouponInfo.sucGroupTag.fontColor):''">{{ grouponInfo.sucGroupTag?grouponInfo.sucGroupTag.name:'' }} </el-tag>
            <el-button v-show="grouponInfo.sucGroupTag" @click="deleteUserTag('sucGroupTagId')"  type="danger" icon="el-icon-delete" size="mini"></el-button>
        </el-form-item>
        <el-divider>限制设置</el-divider>
        <el-form-item label="用户标签">
            <el-button @click="openTagBox('limitUserTagId')" v-show="!grouponInfo.limitUserTag" icon="el-icon-plus" size="mini"></el-button>
            <el-tag @click="openTagBox('limitUserTagId')" v-show="grouponInfo.limitUserTag"  size="medium" :color="grouponInfo.limitUserTag?grouponInfo.limitUserTag.backColor:''"
                    :style="grouponInfo.limitUserTag?getFontColor(grouponInfo.limitUserTag.fontColor):''">{{ grouponInfo.limitUserTag?grouponInfo.limitUserTag.name:'' }} </el-tag>
            <el-button v-show="grouponInfo.limitUserTag" @click="deleteUserTag('limitUserTagId')"  type="danger" icon="el-icon-delete" size="mini"></el-button>
        </el-form-item>
        <el-form-item label="开团金额">
          <el-input-number v-model="grouponInfo.limitUserPrice" type="number" :min="0" :max="999999999" size="mini"></el-input-number>
        </el-form-item>
        <el-form-item label="提示标题">
          <el-input v-model="grouponInfo.limitRemindMessage" size="mini"></el-input>
        </el-form-item>
        <el-form-item label="提示内容">
          <el-input v-model="grouponInfo.limitButtonMessage" size="mini"></el-input>
        </el-form-item>
        <el-divider>是否强制获取手机号</el-divider>
        <el-form-item label="规则选择">
          <el-radio-group v-model="grouponInfo.phoneFlag">
            <el-radio :label="'1'">付款前</el-radio>
            <el-radio :label="'2'">付款后</el-radio>
            <el-radio :label="'3'">不判断</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-divider>参团通知</el-divider>
        <el-form-item label="参团通知">
          <div @click="openMessageBox('joinMessageId')">
            <el-input v-model="grouponInfo.joinMessageName" size="mini" :readonly="true"
                      placeholder="参团后的通知">
              <el-button @click.stop="grouponInfo.joinMessageName=''"  slot="append" icon="el-icon-delete">删除</el-button>
            </el-input>
          </div>
        </el-form-item>
        <el-form-item label="成团通知">
          <div @click="openMessageBox('successMessageId')">
            <el-input v-model="grouponInfo.successMessageName" size="mini" :readonly="true"
                      placeholder="成团后的通知">
              <el-button @click.stop="grouponInfo.successMessageName=''"  slot="append" icon="el-icon-delete">删除</el-button>
            </el-input>
          </div>
        </el-form-item>
      </el-form>

    <!--      微信消息-->
    <el-dialog
      title="订阅消息"
      :visible.sync="qrMessageBoxVisible"
      :close-on-click-modal="false"
      center
      :append-to-body="true"
      lock-scroll
      width="80%">
      <div style="overflow: hidden">
        <qrMessage :selectedAppId="this.appId" :type="'1'" v-on:ensureMsg="ensureMsg" @backFun="ensureMsg"></qrMessage>
      </div>
    </el-dialog>

    <!--      微信用户标签-->
    <el-dialog
      title="标签选择"
      :visible.sync="tagBoxVisible "
      :close-on-click-modal="false"
      center
      :append-to-body="true"
      lock-scroll
      width="80%">
      <div style="overflow: hidden">
        <wx-user-tag  :selectedType="0"  :appId="appId" :selectedTagId="selectedTagId" v-on:ensureTag="ensureTag" @backFun="ensureTag"></wx-user-tag>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {getByPageId as getSpellGroup, putObj as updateSpellGroup} from '@/api/viewgen/spellGroup'
import qrMessage from '@/views/wxmp/wxmpqrcodemessage' // 扫码消息
import wxUserTag from '@/views/wxmp/wxusertags/userTagSelect' // 用户标签

  export default {
    name: "grouponSetting",
    props: {
      pageId: [String],
      appId: [String],
    },
    components: {
      qrMessage,
      wxUserTag,
    },
    data() {
      return {
        grouponInfo:{},//拼团信息
        qrMessageBoxVisible:false, //扫码消息框
        qrMessageBoxFlag:'', //扫码消息标志
        tagBoxFlag: '',//标签弹出框标记
        tagBoxVisible:false,  //用户标签框
        selectedTagId:'',//选中的tagId
        grouponTime:[],//拼团时间
        pickerOptions: {//拼团时间快捷选项
          shortcuts: [{
            text: '最近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }]
        },
      }
    },
    created() {
    },
    mounted(){
      this.getGrouponData();
    },
    computed: {
    },
    methods:{
      getGrouponData(){
        if (this.pageId) {
          getSpellGroup(this.pageId).then(res => {
            this.grouponInfo = res.data.data;
            if(this.grouponInfo.startTime){
              this.grouponTime.push(this.grouponInfo.startTime);
            }
            if(this.grouponInfo.endTime){
              this.grouponTime.push(this.grouponInfo.endTime);
            }
          });
        }
      },
      openTagBox(flag) {
        this.selectedTagId = this.grouponInfo[flag];
        this.tagBoxFlag = flag;
        this.tagBoxVisible = true;
      },
      ensureMsg(obj) {
        console.log("确认消息", obj)
        if (this.qrMessageBoxFlag == 'successMessageId') {
          this.grouponInfo.successMessageId = obj.id;
          this.grouponInfo.successMessageName = obj.name;

        } else if (this.qrMessageBoxFlag == 'joinMessageId') {
          this.grouponInfo.joinMessageId = obj.id;
          this.grouponInfo.joinMessageName = obj.name;
        }
        this.qrMessageBoxVisible = false;
      },
      ensureTag(obj) {
        console.log("确认标签", obj)
        if (this.tagBoxFlag == 'prePayTagId') {
          this.grouponInfo.prePayTagId = obj.id;
          this.grouponInfo.prePayTag = obj;
        } else if (this.tagBoxFlag == 'sucPayTagId') {
          this.grouponInfo.sucPayTagId = obj.id;
          this.grouponInfo.sucPayTag = obj;
        } else if (this.tagBoxFlag == 'sucGroupTagId') {
          this.grouponInfo.sucGroupTagId = obj.id;
          this.grouponInfo.sucGroupTag = obj;
        } else if (this.tagBoxFlag == 'limitUserTagId') {
          this.grouponInfo.limitUserTagId = obj.id;
          this.grouponInfo.limitUserTag = obj;
        }
        this.tagBoxVisible = false;
      },
      deleteUserTag(type){
        if (type == 'prePayTagId') {
          this.grouponInfo.prePayTagId = "";
          this.grouponInfo.prePayTag = "";
        } else if (type == 'sucPayTagId') {
          this.grouponInfo.sucPayTagId = "";
          this.grouponInfo.sucPayTag = "";
        } else if (type == 'sucGroupTagId') {
          this.grouponInfo.sucGroupTagId = "";
          this.grouponInfo.sucGroupTag = "";
        } else if (type == 'limitUserTagId') {
          this.grouponInfo.limitUserTagId = "";
          this.grouponInfo.limitUserTag = "";
        }
      },
      //转化得到字体颜色
      getFontColor(val) {
        if (!val) {
          return;
        }
        return "color:" + val;
      },
      //保存方法
      update(){
        this.grouponInfo.startTime = this.grouponTime[0];
        this.grouponInfo.endTime = this.grouponTime[1];
        console.log("拼团活动更新了",this.grouponInfo)
        updateSpellGroup(this.grouponInfo).then(res=>{
          console.log(res)
        }).catch();
      },
      openMessageBox(flag) {
        this.qrMessageBoxFlag = flag;
        this.qrMessageBoxVisible = true;
      },
    }
  };
</script>

<style lang="scss" scoped>

.page_setting{
  overflow: scroll;
  height:80vh;
}
.el-form-item {
  margin-bottom: 0;
}
</style>
