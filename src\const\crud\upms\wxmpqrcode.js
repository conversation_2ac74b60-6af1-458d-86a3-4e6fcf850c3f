export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  excelBtn: true,
  printBtn: true,
  viewBtn: false,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  columnBtn: false,//列的显隐按钮
  searchMenuSpan: 6,
  column: [
    {
      label: '主键',
      prop: 'id',
      sortable: true,
      hide:true
    },
    {
      label: '二维码名称',
      prop: 'name',
      sortable: true,
      rules: [
        {
          max: 32,
          message: '长度在不能超过32个字符'
        },
      ]
    },
    {
      label: '公众号',
      prop: 'appId',
      sortable: true,
      filterable: true,
      filters:true,
      filterMethod:function(value, row, column) {
        return row.appId === value;
      },
      dicData:[],
    },
    {
      label: '扫码消息',
      prop: 'messageName',
      sortable: false,
    },
    {
      label: '创建者',
      prop: 'createName',
      sortable: true,
    },
    {
      label: '创建时间',
      prop: 'createTime',
      sortable: true,
      rules: [
      ]
    },
    {
      label: '背景颜色',
      hide: true,
      prop: 'tagBackColor',
    },
    {
      label: '字体颜色',
      hide: true,
      prop: 'tagFontColor',
    },
    {
      label: '更新者',
      prop: 'updateName',
      sortable: true,
    },
    {
      label: '更新时间',
      prop: 'updateTime',
      sortable: true,
      rules: [
      ]
    },
    {
      label: '到期时间',
      prop: 'endTime',
      sortable: true,
      format: 'yyyy-MM-dd',
      valueFormat: 'yyyy-MM-dd',
      dicData: [{
        label: '长期有效',
        value: '',
      }]
    },
    // {
    //   label: '是否裂变（0是，1否））',
    //   prop: 'fissionFlag',
    //   sortable: true,
    //   rules: [
    //     {
    //       max: 2,
    //       message: '长度在不能超过2个字符'
    //     },
    //   ]
    // },
    // {
    //   label: '裂变名称',
    //   prop: 'fissionName',
    //   sortable: true,
    //   rules: [
    //     {
    //       max: 200,
    //       message: '长度在不能超过200个字符'
    //     },
    //   ]
    // },
    // {
    //   label: '背景图片',
    //   prop: 'background',
    //   sortable: true,
    //   rules: [
    //     {
    //       max: 200,
    //       message: '长度在不能超过200个字符'
    //     },
    //   ]
    // },
    {
      label: '扫码标签',
      prop: 'ticket',
      hide:true,
    },

  ]
}
