export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  excelBtn: true,
  printBtn: true,
  viewBtn: true,
  searchMenuSpan: 6,
  menu: false,
  column: [
    {
      label: '创建时间',
      prop: 'createTime',
      sortable: true,
      rules: [
        {
          required: true,
          message: '请输入创建时间',
          trigger: 'blur'
        },
      ]
    },
    {
      label: '用户信息',
      prop: 'userId',
      sortable: true,
      slot: true,
      rules: [
        {
          required: true,
          message: '请输入用户编号',
          trigger: 'blur'
        },
        {
          max: 32,
          message: '长度在不能超过32个字符'
        },
      ]
    },
    {
      label: '详细信息',
      prop: 'relationId',
      sortable: true,
      slot: true,
      width: 260,
      rules: [
        {
          required: true,
          message: '请输入关联ID：商品类型为商品ID',
          trigger: 'blur'
        },
        {
          max: 32,
          message: '长度在不能超过32个字符'
        },
      ]
    },
  ]
}
