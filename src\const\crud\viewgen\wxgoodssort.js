export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  excelBtn: false,//导出
  addBtn: false,
  printBtn: true,
  viewBtn: false,
  editBtn: false,
  delBtn: false,
  searchMenuSpan: 6,
  column: [
    {
      label: '主键',
      prop: 'id',
      hide: true,
      display: true,
    },
    {
      label: '排序名称',
      prop: 'name',
      sortable: true,
      rules: [
        {
          max: 25,
          message: '长度在不能超过25个字符'
        },
      ]
    },
    // {
    //   label: 'appId',
    //   prop: 'appId',
    //   filters:true,
    //   filterMethod:function(value, row, column) {
    //     return row.appId === value;
    //   },
    //   dicData:[],
    // },
  ]
}



export const selectTableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menu: false,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  excelBtn: false,//导出
  addBtn: false,
  printBtn: true,
  viewBtn: false,
  editBtn: false,
  delBtn: false,
  searchMenuSpan: 6,
  column: [
    {
      label: '主键',
      prop: 'id',
      hide: true,
      display: true,
    },
    {
      label: '排序名称',
      prop: 'name',
      sortable: true,
      rules: [
        {
          max: 25,
          message: '长度在不能超过25个字符'
        },
      ]
    },
    // {
    //   label: 'appId',
    //   prop: 'appId',
    //   filters:true,
    //   filterMethod:function(value, row, column) {
    //     return row.appId === value;
    //   },
    //   dicData:[],
    // },
  ]
}
