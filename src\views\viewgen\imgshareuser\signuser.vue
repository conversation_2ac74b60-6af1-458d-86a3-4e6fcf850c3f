<template>
  <div class="execution">
    <basic-container>
      <avue-crud ref="crud"
                 :page="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 v-model="form"
                 @on-load="getPage"
                 @refresh-change="refreshChange"
                 @row-update="handleUpdate"
                 @row-save="handleSave"
                 @row-del="handleDel"
                 @sort-change="sortChange"
                 @search-change="searchChange">
        <template slot-scope="scope" slot="menu">
          <el-button type="info" size="mini" @click.stop="openVoteRecord(scope.row)">记录</el-button>
          <el-button type="warning" size="mini" @click.stop="copyPageUrl(scope.row)">地址</el-button>
          <el-button type="success" size="mini" @click.stop="openImgUpload(scope.row)">上传客照</el-button>
          <el-button type="primary" size="mini" v-if="scope.row.status=='1'" @click.stop="handleRelease(scope.row)">待发布</el-button>
          <el-button type="primary" size="mini" v-if="scope.row.status=='2'" :disabled="true">收集中</el-button>
          <el-button style="color: white;background-color: #F56C6C" size="mini" v-if="scope.row.status=='3'" @click.stop="handleCheck(scope.row)">待验收</el-button>
          <el-button style="color: white;background-color: #011017" type="primary" size="mini" v-if="scope.row.status=='4'">已完成</el-button>
        </template>
      </avue-crud>
    </basic-container>
    <!--    记录弹出框-->
    <el-dialog title="投票记录" :visible.sync="voteRecordVisible" width="80%" lock-scroll :close-on-click-modal="false"
               :append-to-body="true" center>
      <div style="overflow: hidden">
        <vote-record :shareObj="shareObj"></vote-record>
      </div>
    </el-dialog>
    <!--    上传客照弹出框-->
    <el-dialog title="上传客照" :visible.sync="imgUploadVisible" width="80%" lock-scroll :close-on-click-modal="false"
               :append-to-body="true" center>
      <div style="overflow: hidden">
        <el-form :model="shareForm" :rules="shareFormRules" ref="shareForm" label-width="100px">
          <el-form-item label="页面选择" prop="pageId">
            <el-select v-model="shareForm.pageId" placeholder="请选择展示页面">
              <el-option
                v-for="item in pageList"
                :key="item.id"
                :label="item.pageName"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="页面标题" >
            <el-input v-model="shareForm.title"></el-input>
          </el-form-item>
          <el-form-item label="分享标题" >
            <el-input v-model="shareForm.shareTitle"></el-input>
          </el-form-item>
          <el-form-item label="分享描述" >
            <el-input v-model="shareForm.shareDescribe"></el-input>
          </el-form-item>
          <el-form-item label="分享图片" prop="titleImgUrl">
            <MaterialList :value="shareForm.titleImgUrl?[shareForm.titleImgUrl]:[]"  @sureSuccess="shareForm.titleImgUrl = $event?$event[0]:''" @deleteMaterial="shareForm.titleImgUrl = ''"
                          type="image"  :divStyle="'width:50%;margin-bottom:0px;height:90px;line-height: 90px;'" :num=1 ></MaterialList>
          </el-form-item>
          <el-form-item label="定时发布" prop="releaseFlag">
            <el-switch
              active-value="1"
              inactive-value="0"
              v-model="shareForm.releaseFlag"></el-switch>
          </el-form-item>
          <el-form-item v-show="shareForm.releaseFlag=='1'" label="发布时间" prop="releaseTime">
            <el-date-picker
              v-model="shareForm.releaseTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              placeholder="选择日期时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="客照上传" prop="shareImg">
            <draggable v-model="shareForm.shareImgList" @start="datadragStart" @update="datadragUpdate"
                       @end="datadragEnd"
                       :disabled="false" :move="datadragMove" :options="{animation:500}">
              <transition-group>
                <div class="image_preview" v-for="(item,index) in shareForm.shareImgList"
                     :key="'shareContentImg'+index">
                  <el-image
                    class="image_preview_image"
                    fit="fill"
                    :src="item"
                    :preview-src-list="shareForm.preContentImgList">
                  </el-image>
                  <div class="img_dialog">
                    <span
                      @click="handleRemove('content',null,index)">
                    <i class="el-icon-delete"></i>
                  </span>
                  </div>
                </div>
              </transition-group>
            </draggable>
            <el-upload
              :file-list="uploadContent.list"
              :show-file-list="false"
              :action="serverUrl"
              :headers="header"
              :before-upload="(file)=>{return beforeUpload(file)}"
              accept=".jpg,.jpeg,.png,.bmp,.JPG,.JPEG,.PNG,.BMP"
              :on-success="(res,file,fileList)=>{return uploadSuccess(res,file,fileList,'content',uploadContent)}"
              :on-change="(file,fileList)=>{return handleUploadChange(file,fileList,uploadContent)}"
              multiple>
              <el-image class="waite_upload_img">
                <div style="margin-top:32%" slot="error">
                  <i v-show="uploadContent.uploadIconVisible" class="el-icon-upload2">点击上传</i>
                  <el-progress v-show="uploadContent.processIconVisible" type="circle" :width="55"
                               :percentage="uploadContent.percentage"></el-progress>
                </div>
              </el-image>
            </el-upload>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitShareImg">立即创建</el-button>
            <el-button @click="imgUploadVisible = false">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {getBindingPage, putObj, delObj, handleRelease,handleCheck} from '@/api/viewgen/imgshareuser'
import {getListByType} from '@/api/viewgen/pagedevise'
import {tableOption} from '@/const/crud/viewgen/signuser'
import {mapGetters} from 'vuex'
import draggable from "vuedraggable";
import store from "@/store";
import voteRecord from '@/views/viewgen/imgshareuser/voteRecord'
import {pageUrls} from "@/components/wx-page-select/pageUrls";
import {h5HostMobile} from "@/config/env";
import MaterialList from '@/components/material/wxlist.vue'

export default {
  name: 'signuser',
  props:{
    wxAppList:{
      type: Array,
    },
  },
  components: {
    draggable,
    voteRecord,
    MaterialList,
  },
  watch: {
    wxAppList(newVal, oldVal) {
      this.setWxAppList(newVal);
    }
  },
  data() {
    const validatorTime = (rule, value, callback) => {
      if (this.shareForm.releaseFlag == '1' && !this.shareForm.releaseTime) {
        callback(new Error('请选择发布时间'))
      } else {
        callback();
      }
    };
    return {
      wxAppListTemp:this.wxAppList,
      serverUrl: "/upms/file/upload?fileType=image&dir=wx/imgShare/", // 这里写你要上传的图片服务器地址
      header: {Authorization: 'Bearer ' + store.getters.access_token}, // 有的图片服务器要求请求头需要有token
      pageList: [],
      titleImgUrlVisible: false,//预览图片
      imgUploadVisible: false,//上传图片弹出框
      voteRecordVisible: false,//投票记录弹出框
      shareObj:{},//分享记录查看
      shareForm: {},
      uploadContent: { //内容上传
        list: [],
        uploadIconVisible: true,
        processIconVisible: false,
      },
      uploadShare: { //分享上传
        list: [],
        uploadIconVisible: true,
        processIconVisible: false,
      },
      shareFormRules: {
        pageId: [
          {required: true, message: '请选择展示页面', trigger: 'submit'}
        ],
        title: [
          {required: true, message: '请输入页面标题', trigger: 'submit'},
        ],
        releaseTime: [{required: true, trigger: 'submit', validator: validatorTime}]
      },
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption
    }
  },
  created() {
    console.log("传入的wxAppList",this.wxAppList)
    this.setWxAppList(this.wxAppList);
  },
  mounted () {

  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.permissions['weixin:imgshareuser:add'] ? true : false,
        delBtn: this.permissions['weixin:imgshareuser:del'] ? true : false,
        editBtn: this.permissions['weixin:imgshareuser:edit'] ? true : false,
        viewBtn: this.permissions['weixin:imgshareuser:get'] ? true : false
      };
    }
  },
  methods: {
    setWxAppList(newVal){
      this.wxAppListTemp =newVal;
      for (let i = 0; i < this.wxAppListTemp.length; i++) {
        tableOption.column[tableOption.column.length-1].dicData.push({label: this.wxAppListTemp[i].name, value: this.wxAppListTemp[i].id});
      }
      console.log("tableOption",tableOption)
    },
    searchChange(params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    sortChange(val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage(page, params) {
      this.tableLoading = true
      getBindingPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
      }, params, this.paramsSearch)).then(res => {
        console.log("分页",res.data.data.records)
        this.tableData = res.data.data.records
        this.page.total = res.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel (row, index) {
      let _this = this
      this.$confirm('是否确认删除此数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then( ()=> {
        return delObj(row.id)
      }).then(data => {
        _this.$message({
          showClose: true,
          message: '删除成功',
          type: 'success'
        })
        this.getPage(this.page)
      }).catch( (err)=> {
      })
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      putObj(row).then(res => {
        this.$message({
          showClose: true,
          message: '修改成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      addObj(row).then(res => {
        this.$message({
          showClose: true,
          message: '添加成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    // 拖动的事件等等=======================================================>
    datadragStart(e) {
      let that = this;
      // that.updateData({clickComIndex: null})
      that.showSetBlock = false;
    },
    datadragUpdate(e) {
      e.preventDefault();
    },
    datadragEnd(e) {
      // console.log(e, "拖动结束");
    },
    datadragMove(e, originalEve) {
      return (e.draggedContext.element.text !== 'Gold（不可拖动元素）');
    },
    //====================================================================>
    beforeUpload(file, item) {
      const isLt1M = file.size / 1024 / 1024 < 1;
      if (!isLt1M) {
        this.$message.error('上传图片大小不能超过 1MB!');
        item.uploadIconVisible = true;
        item.processIconVisible = false;
        item.uploadDisabled = false;
      }
      return isLt1M;
    },
    uploadSuccess(res, file, fileList, type, item) {
      if (type == 'share') {
        this.shareForm.titleImgUrl = res.link;
      }
      if (type == 'content') {
        this.shareForm.shareImgList.push(res.link);
        this.shareForm.preContentImgList.push(res.link);
      }
      this.$message.success("上传成功")
    },
    uploadError(res, item) {
      item.uploadIconVisible = true;
      item.processIconVisible = false;
      item.uploadDisabled = false;
      this.$message.error("上传失败")
    },
    handleUploadChange(file, fileList, item) {
      if (file.status == "ready") {
        item.percentage = file.percentage;
        item.uploadDisabled = true;
        item.uploadIconVisible = false;
        item.processIconVisible = true;
      } else if (file.status == "success") {
        item.uploadIconVisible = true;
        item.processIconVisible = false;
        item.uploadDisabled = false;
      }
    },
    handleRemove(type, item, index) {
      if (type == 'share') {
        this.shareForm.titleImgUrl = '';
      }
      if (type == "content") {
        this.shareForm.shareImgList.splice(index, 1);
        this.shareForm.preContentImgList.splice(index, 1)
      }
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page)
    },
    /**
     * 获取页面列表
     */
    getPageList() {
      let type = '5';
      getListByType(type).then(res => {
        console.log("dsafds", res);
        this.pageList = res.data.data;
      }).catch(err => {

      })
    },
    /**
     * 打开上传客照框
     */
    openImgUpload(obj) {
      this.getPageList();
      // console.log("obj",obj)
      this.shareForm = Object.assign({}, obj);
      console.log("shareForm", this.shareForm)
      if(!this.shareForm.title){
        this.shareForm.title = this.shareForm.name;
      }
      // console.log("shareForm", this.shareForm)
      this.imgUploadVisible = true;
    },
    /**
     * 提交上传客照
     */
    submitShareImg() {
      this.$refs['shareForm'].validate(valid => {
        if (valid) {
          // this.shareForm.shareImgList = JSON.parse(JSON.stringify(this.shareForm.shareImgList));
          console.log("提交上传客照", this.shareForm)
          putObj(this.shareForm).then(res => {
            if (res.data.data) {
              this.$message({
                message: '修改成功',
                type: 'success',
                duration: 2000
              })
              this.refreshChange();
            } else {
              this.$message({
                message: res.data.msg,
                type: 'error',
                duration: 2000
              })
            }
          }).catch(() => {
            this.$message({
              message: '修改失败',
              type: 'error',
              duration: 2000
            })
          })
          this.imgUploadVisible = false;
        } else {
          return false
        }
      })

    },
    /**
     * 打开投票记录
     */
    openVoteRecord(obj) {
      this.shareObj = obj;
      this.voteRecordVisible = true;
    },
    /**
     * 发布客片
     */
    handleRelease(obj) {
      this.$confirm('是否确认发布客片', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then( ()=> {
        handleRelease(obj).then(res => {
          this.getPage(this.page)
          this.$message({
            showClose: true,
            message: '发布成功',
            type: 'success'
          })
        }).catch(err => {

        })
      }).catch( (err)=> {
      })
    },
    /**
     * 验收客片
     */
    handleCheck(obj) {
      this.$confirm('是否确认验收客片', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then( ()=> {
        handleCheck(obj).then(res => {
          this.$message({
            showClose: true,
            message: '验收成功',
            type: 'success'
          })
          this.refreshChange();
        }).catch(err => {

        })
      }).catch( (err)=> {
      })
    },
    /**
     * 复制地址
     */
    copyPageUrl(obj) {
      console.log("page", obj);
      console.log("this.wxAppListTemp",this.wxAppListTemp)
      let url = "";
      let list =  pageUrls;//app 页面地址
      for (let i in list ) {
        if(list[i].type == "5"){
          url = h5HostMobile + list[i].url + "?page_id=" + obj.pageId + "&data_id=" + obj.id+ "&app_id=" + obj.appId
        }
      }
      for (let i = 0; i < this.wxAppListTemp.length; i++) {
        console.log(this.wxAppListTemp[i])
        if(obj.appId == this.wxAppListTemp[i].id ){
          url= url+"&tenant_id="+this.wxAppListTemp[i].tenantId;
          if('1' == this.wxAppListTemp[i].isComponent){
            url= url + '&component_appid='+this.wxApp.componentAppId;
            break;
          }
          break;
        }
      }
      console.log("urls",url);

      this.$copyText(url).then( e => {
        this.$message.success("复制成功")
        console.log(e)
      }, function (e) {
        this.$message.error("复制失败")
        console.log(e)
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.img_dialog {
  position: absolute;
  left: 80%;
  top: 0;
  width: 30px;
  opacity: 0;
}

.image_preview {
  position: relative;
  float: left;
  display: inline;
  margin: 0px 15px 10px 0px;

  .image_preview_image {
    border: 1px solid transparent;
    width: 150px;
    height: 150px;
  }

  &:hover .img_dialog {
    text-align: center;
    cursor: default;
    text-align: center;
    color: #fff;
    opacity: 1;
    font-size: 20px;
    background-color: rgba(0, 0, 0, .5);
    transition: opacity .3s;
  }
}

.waite_upload_img {
  border: 1px #8c939d dashed;
  border-radius: 6px;
  width: 150px;
  height: 150px
}
</style>
