<!--所有团队-->
<template>
  <div class="execution">
    <avue-crud ref="crud"
               :page.sync="groupPage"
               :data="tableData"
               :permission="permissionList"
               :table-loading="tableLoading"
               :option="tableOption"
               v-model="form"
               @on-load="getGroupPage"
               @refresh-change="refreshChange"
               @sort-change="sortGroupChange"
               :row-style="rowStyle"
               @search-change="searchChange">
      <template slot-scope="scope" slot="menuLeft">
        <el-input v-model="searchValue" :maxlength="64" placeholder="可以输入电话号码、昵称、openId" style="width:250px;padding-right: 5px"
                  size="small">
        </el-input>
        <el-button type="primary"
                   icon="el-icon-search"
                   size="small"
                   @click.stop="search">搜索
        </el-button>
        <el-button type="primary"
                   icon="el-icon-refresh-left"
                   size="small"
                   @click.stop="reset">重置
        </el-button>
      </template>
      <template slot-scope="scope" slot="status">
        <el-tag v-if="scope.row.status == '0'" type="warning" size="mini" effect="light">拼团中</el-tag>
        <el-tag v-if="scope.row.status == '1'" type="success" size="mini" effect="light">拼团成功</el-tag>
      </template>
      <template slot-scope="scope" slot="isLeader">
        <el-tag v-if="scope.row.isLeader == '0'" type="light" size="mini">团员</el-tag>
        <el-tag v-if="scope.row.isLeader == '1'" type="success" effect="dark" size="mini">团主</el-tag>
      </template>
      <template slot-scope="scope" slot="trueFlag">
        <el-tag v-if="scope.row.trueFlag == '0'" type="success" size="mini">是</el-tag>
        <el-tag v-if="scope.row.trueFlag == '1'" type="danger" effect="dark" size="mini">否</el-tag>
      </template>
      <template slot-scope="scope" slot="grouponNum">
        {{getLackNum(scope.row)}}
      </template>

      <!-- 自定义订单状态列显示 -->
      <template slot="orderStatusDesc" slot-scope="scope">
        <el-tag
          :type="getOrderStatusTagType(scope.row.orderInfo ? scope.row.orderInfo.statusDesc : '')"
          size="small">
          {{ scope.row.orderInfo ? scope.row.orderInfo.statusDesc : '-' }}
        </el-tag>
      </template>

      <!-- 自定义退款状态列显示 -->
      <template slot="refundStatus" slot-scope="scope">
        <el-tag
          v-if="getLatestRefundStatus(scope.row)"
          :type="getRefundStatusTagType(getLatestRefundStatus(scope.row))"
          size="small">
          {{ getLatestRefundStatus(scope.row) }}
        </el-tag>
        <span v-else>-</span>
      </template>

      <!-- 自定义购买详情列显示 -->
      <template slot="purchaseDetail" slot-scope="scope">
        <span v-if="getPurchaseDetail(scope.row)">
          {{ getPurchaseDetail(scope.row) }}
        </span>
        <span v-else>-</span>
      </template>
    </avue-crud>
  </div>
</template>

<script>
import {getGroupPage,putObj, quicklyFinish} from '@/api/viewgen/wxgrouponuser'
import {tableOption} from '@/const/crud/viewgen/wxallgrouponuser'
import {mapGetters} from 'vuex'

export default {
  name: 'unwxgrouponuser',
  props: {
    id: String//页面Id来的
  },
  data() {
    return {
      bathList: [],//批量选择列表
      batchFinishBoxVisible: false,
      form: {},
      tableData: [],
      joinData: [],
      groupPage: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      joinPage: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      searchValue: "",//搜索值
    }
  },
  created() {

  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.permissions['weixinapi:wxspellgroupuser:add'] ? true : false,
        delBtn: this.permissions['weixinapi:wxspellgroupuser:del'] ? true : false,
        editBtn: this.permissions['weixinapi:wxspellgroupuser:edit'] ? true : false,
        viewBtn: this.permissions['weixinapi:wxspellgroupuser:get'] ? true : false
      };
    }
  },
  methods: {
    //快速成团
    quicklyFinish(obj) {
      this.$confirm('确认进行此操作, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let list = [];
        list.push({
          grouponId: obj.grouponId,
          groupId: obj.groupId,
        });
        quicklyFinish(list).then(res => {
          console.log("快速成团", res)
          this.$message.success('快速成团操作成功')
          this.refreshUnGroup();
        });
      }).catch(() => {

      });

    },
    //打开批量成团
    batchFinish() {
      if (!this.bathList || this.bathList.length == 0) {
        this.$message.warning("请先勾选至少一条数据")
        return;
      }
      quicklyFinish(this.bathList).then(res => {
        console.log("快速成团", res)
        this.$message.success('快速成团操作成功')
        this.refreshUnGroup();
      });
      // this.batchFinishBoxVisible = true;
    },
    searchChange(params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getGroupPage(this.page, params)
      done()
    },
    sortGroupChange(val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.groupPage.descs = []
        this.groupPage.ascs = prop
      } else if (val.order == 'descending') {
        this.groupPage.ascs = []
        this.groupPage.descs = prop
      } else {
        this.groupPage.ascs = []
        this.groupPage.descs = []
      }
      this.getGroupPage(this.groupPage)
    },
    getGroupPage(page, params) {
      this.tableLoading = true
      if (!params) {
        params = {id: this.id};
      }
      getGroupPage(Object.assign({
        current: this.groupPage.currentPage,
        size: this.groupPage.pageSize,
        descs: this.groupPage.descs,
        ascs: this.groupPage.ascs,
      }, params, this.paramsSearch)).then(res => {
        this.tableData = res.data.data.records
        this.groupPage.total = res.data.data.total
        this.groupPage.currentPage = page.currentPage
        this.groupPage.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },

    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleJoinUpdate: function (row, index, done, loading) {
      putObj(row).then(res => {
        this.$message({
          showClose: true,
          message: '修改成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      // console.log('刷新')
      this.getGroupPage(this.groupPage, {id: this.id})
    },
    selectionChange(list) {
      let res = [];
      list.forEach(obj=>{
        res.push({
          grouponId: obj.grouponId,
          groupId: obj.groupId,
        });
      })
      this.bathList = res;
    },
    search() {
      this.paramsSearch = {
        id: this.id,
        status: this.status,
        nickName: this.searchValue,
        phone: this.searchValue,
        openId: this.searchValue
      }
      this.groupPage.currentPage = 1
      this.getGroupPage(this.groupPage)
    },
    reset() {
      this.searchValue = "";
      this.paramsSearch = {}
      this.groupPage.currentPage = 1
      this.getGroupPage(this.groupPage)
    },
    rowStyle ({ row, rowIndex }) {
      if (row.isLeader == '1' ) {
        return {
          backgroundColor: "#b8cfef",
        };
      }
      return "";
    },
    getLackNum(row){
      if(row.isLeader =='1') {
        let num = row.grouponNum -( row.children ? row.children.length+1 : 1);
        if(num == 0){
          return "已完成";
        }else {
          return num;
        }
      }else if(row.isLeader =='0') {
        return "";
      }
    },
    // 根据订单状态返回对应的标签类型
    getOrderStatusTagType(statusDesc) {
      if (statusDesc === '已完成') {
        return 'success'
      } else if (statusDesc === '已取消') {
        return 'danger' // Element UI的el-tag使用danger表示错误状态
      } else {
        return '' // 默认样式
      }
    },
    // 获取最新的退款状态
    getLatestRefundStatus(row) {
      // 从订单数据中获取退款状态
      if (row.orderInfo && row.orderInfo.listOrderRefunds && row.orderInfo.listOrderRefunds.length > 0) {
        const latestRefund = row.orderInfo.listOrderRefunds[row.orderInfo.listOrderRefunds.length - 1]
        return latestRefund.statusDesc || latestRefund.status || '-'
      }
      return null
    },
    // 根据退款状态返回对应的标签类型
    getRefundStatusTagType(statusDesc) {
      if (statusDesc === '退款申请中') {
        return 'warning' // 黄色
      } else if (statusDesc === '同意退款') {
        return 'danger' // 红色
      } else if (statusDesc === '拒绝') {
        return '' // primary蓝色（默认）
      } else {
        return 'info' // 其他状态用info灰色
      }
    },
    // 获取购买详情
    getPurchaseDetail(row) {
      // 从订单数据中获取商品详情
      if (row.orderInfo && row.orderInfo.listOrderItem && row.orderInfo.listOrderItem.length > 0) {
        const firstItem = row.orderInfo.listOrderItem[0]
        return `${firstItem.spuName}|${firstItem.specInfo}`
      }
      return null
    }
  }
};
</script>

<style lang="scss" scoped>
</style>
