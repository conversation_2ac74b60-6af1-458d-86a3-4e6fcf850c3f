export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  viewBtn: true,
  searchMenuSpan: 6,
  labelWidth: 200,
  column: [
    {
      label: '微信支付商户号',
      prop: 'mchId',
      sortable: true,
      rules: [
        {
          max: 64,
          message: '长度在不能超过64个字符'
        },
      ]
    },
    {
      label: '微信支付商户密钥',
      prop: 'mchKey',
      sortable: true,
      rules: [
        {
          max: 64,
          message: '长度在不能超过64个字符'
        },
      ]
    },
  ]

  // column: [
  //   {
  //     label: '商户id',
  //     prop: 'appId',
  //     rules: [{
  //       required: true,
  //       message: 'appId不能为空',
  //       trigger: 'blur'
  //     }]
  //   },{
  //     label: 'appSecret',
  //     prop: 'appSecret',
  //     rules: [{
  //       required: true,
  //       message: 'appSecret不能为空',
  //       trigger: 'blur'
  //     }]
  //   },{
  //     label: 'token',
  //     prop: 'token',
  //     rules: [{
  //       required: true,
  //       message: 'token不能为空',
  //       trigger: 'blur'
  //     }]
  //   },{
  //     label: 'aesKey',
  //     prop: 'aesKey',
  //     rules: [{
  //       required: true,
  //       message: 'aesKey不能为空',
  //       trigger: 'blur'
  //     }]
  //   }
  // ]

}
