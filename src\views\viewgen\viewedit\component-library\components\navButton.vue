<template>
    <div class="navButtonComponent" :style="{paddingTop: `${setData.pageTopSpacing}px`,paddingBottom: `${setData.pageBotSpacing}px`,display:'fixed'}">
<!--      导航-->
      <div  style="background:#ffffff">
        <div class="cu-list grid no-border" :class="'col-'+setData.rowNum" >
          <div class="cu-item" v-for="(item,index) in setData.navButtons" :key="index" style="">
            <div :url="item.pageUrl" hover-class="none">
              <img :src="item.imageUrl ? item.imageUrl : defaultImage"  class="nav_bt_img"/>
              <div style="margin-top: -10px" :style="{color: `${setData.textColor}`}" >{{item.navName}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>

<script>
import { mapState , mapGetters , mapMutations , mapActions } from 'vuex';
import placeholderImg from "../pages/page-components/placeholderImg";

export default {
    data() {
        return {
          defaultImage: require('../assets/images/icon/<EMAIL>')
        };
    },
    components: { placeholderImg },
    props: {
        theme : { type: Object | Array },
        setData : { type: Object | Array },
        cId     : { type: Number },
        noEditor: {
            type: Boolean,
            default: false,
        }
    },
    computed: {
        ...mapState({
            componentsList: state => state.divpage.componentsList,
        }),
    },
    created() {
    },
    mounted() {
    },
    methods: {
        ...mapMutations([
            'updateData'
        ]),
    },
    watch:{
        setData(newVal, oldVal){},
        componentsList(newVal, oldVal){          //添加的时候出发（解决没有移动的时候不emit数据）
            let that = this;
            that.updateData({componentsList: that.componentsList})
        }
    },
    beforeDestroy(){
        // this.$root.Bus.$off('addHotSpot')
    }
};
</script>
<style lang='less' scoped>

  @import '../colorui/main.css';
  .navButtonComponent {

    .cu-list.grid.no-border {
      padding-bottom: 10px;
    }
  }
  .navButtonImg_large{
    width: 60px !important;
    height: 60px !important;
    margin: 10px 0;
  }
  .navButtonImg_large{
    width: 40px !important;
    height: 40px !important;
    margin: 10px 0;
  }

</style>
