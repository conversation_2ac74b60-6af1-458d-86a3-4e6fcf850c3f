import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/wxmptempmsg/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/weixin/wxmptempmsg',
    method: 'post',
    data: obj
  })
}

export function syncObj(id) {
  return request({
    url: '/weixin/wxmptempmsg/sync/'+id,
    method: 'post',
  })
}

export function getObj(id) {
  return request({
    url: '/weixin/wxmptempmsg/' + id,
    method: 'get'
  })
}

export function getPublic() {
  return request({
    url: '/weixin/wxmptempmsg/public/',
    method: 'get'
  })
}

export function addPublic(obj) {
  return request({
    url: '/weixin/wxmptempmsg/public/add',
    method: 'post',
    data: obj
  })
}


export function delObj(id) {
  return request({
    url: '/weixin/wxmptempmsg/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/wxmptempmsg',
    method: 'put',
    data: obj
  })
}
