<template>
  <div class="datePickerComponent" :style="{background: `${setData.backgroundColor}`, marginBottom: `${setData.pageSpacing}px`}">
    <div :style="{color: `${setData.titleColor}`, fontSize: `${setData.titleSize}px`,fontWeight:`${setData.titleWeight?'bold':'normal'}`}">{{setData.title}}<i v-show="setData.required" style="color: #FF0000">*</i></div>
    <div :style="{color: `${setData.describeColor}`, fontSize: `${setData.describeSize}px`,fontWeight:`${setData.describeWeight?'bold':'normal'}`}">{{setData.describe}}</div>
      <el-date-picker
        style="width: 100%"
        align="right"
        size="mini"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="选择日期"
        :disabled="true">
      </el-date-picker>

  </div>
</template>

<script>
import { mapState , mapGetters , mapMutations , mapActions } from 'vuex';
export default {
  data() {
    return {};
  },
  components: { },
  props: {
    theme : { type: Object | Array },
    setData : { type: Object | Array },
    cId     : { type: Number },
    noEditor: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {
  },
  mounted() {
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
  },
  watch:{
    setData(newVal, oldVal){},
    componentsList(newVal, oldVal){          //添加的时候出发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    }
  },
  beforeDestroy(){
    // this.$root.Bus.$off('addHotSpot')
  }
};
</script>
<style lang='less' scoped>
@import '../../colorui/main.css';
@import '../../colorui/icon.css';
.datePickerComponent {
  position: relative;
  display: block;
  width: 100%;
  padding: 5px;
  background: #ffffff;

  .single_line_font_input_box{
    padding: 2px;
    border: 1px solid rgba(118, 118, 118, 0.3);
  }
  .single_line_font_icon{
    display: inline-block;
    padding: 0 5px;
  }
  .single_line_font_input{
    flex: 1 1 0%;
    display: inline-block;
    border: 0;
    color:  #ffffff;
    width: 80%;
    position: relative;
    overflow: hidden;
  }
}

</style>
