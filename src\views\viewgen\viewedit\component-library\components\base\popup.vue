<template>
  <div class="popup-container">
    <div class="popup-content" v-if="visible">
      <div
        v-if="setData.imageUrl"
        class="popup-image"
        :style="{
          width: setData.width + 'px',
          height: setData.height + 'px',
          backgroundImage: `url(${setData.imageUrl})`,
          backgroundSize: setData.backgroundSize || 'cover',
          backgroundPosition: setData.backgroundPosition || 'center',
          backgroundRepeat: setData.backgroundRepeat || 'no-repeat'
        }"
      ></div>
      <div v-else class="popup-image-placeholder" :style="{width: setData.width + 'px', height: setData.height + 'px'}">
        <span>请设置图片</span>
      </div>
      <div class="popup-close" >
        <i class="el-icon-close"></i>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'popupComponent',
  props: {
    setData: {
      type: Object,
      default: () => ({
        imageUrl: '',
        linkUrl: '',
        width: 300,
        height: 400,
        autoClose: false,
        closeDelay: 3,
        showDelay: 1,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      })
    },
    config: {
      type: Object,
      default: () => ({})
    },
    cId: {
      type: [Number, String],
      default: ''
    },
    thememobile: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      visible: true,
      timer: null,
      showTimer: null
    }
  },
  mounted() {
    // 处理显示延迟
    if (this.isActualPage() && this.setData.showDelay > 0) {
      this.visible = false;
      this.showTimer = setTimeout(() => {
        this.visible = true;
        // 仅在实际页面环境中处理自动关闭逻辑
        if (this.isActualPage() && this.setData.autoClose && this.setData.closeDelay > 0) {
          this.timer = setTimeout(() => {
            this.closePopup();
          }, this.setData.closeDelay * 1000);
        }
      }, this.setData.showDelay * 1000);
    } else if (this.isActualPage() && this.setData.autoClose && this.setData.closeDelay > 0) {
      this.timer = setTimeout(() => {
        this.closePopup();
      }, this.setData.closeDelay * 1000);
    }
  },
  beforeDestroy() {
    if (this.timer) {
      clearTimeout(this.timer);
    }
    if (this.showTimer) {
      clearTimeout(this.showTimer);
    }
  },
  methods: {
    // 判断是否在实际页面中渲染，而不是在编辑器中
    isActualPage() {
      return false; // 默认在编辑器中
    },
    closePopup() {
      this.visible = false;
      if (this.timer) {
        clearTimeout(this.timer);
      }
    },
    handleImageClick() {
      if (this.setData.linkUrl) {
        // 处理页面跳转
        this.$router.push(this.setData.linkUrl);
      }
    }
  }
}
</script>

<style scoped>
.popup-container {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  margin: 20px 0;
  padding: 10px;
}

.popup-content {
  position: relative;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.popup-image {
  display: block;
  border-radius: 4px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  min-width: 200px;
  min-height: 200px;
  cursor: pointer;
}

.popup-image-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  min-width: 200px;
  min-height: 200px;
  color: #909399;
}

.popup-close {
  margin-top: 10px;
  width: 30px;
  height: 30px;
  background-color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.popup-close i {
  font-size: 18px;
  color: #333;
}
</style>
