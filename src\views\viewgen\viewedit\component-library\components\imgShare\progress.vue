<template>
  <div class="progressComponent"
       :style="{marginBottom: `${setData.pageMarginBottom}px`,
       marginLeft: `${setData.pageMarginLeft}px`,
       marginRight: `${setData.pageMarginRight}px`,
       marginTop: `${setData.pageMarginTop}px`}">
    <div :style="{
     backgroundColor:`${setData.backColor}`,
     paddingTop:`${setData.contentPaddingTop}px`,
     paddingBottom:`${setData.contentPaddingBottom}px`,
     paddingLeft:`${setData.contentPaddingLeft}px`,
     paddingRight:`${setData.contentPaddingRight}px`,
      }">
      <el-progress :text-inside="true" :stroke-width="setData.size" :color="setData.color"
                   :percentage="70"></el-progress>
      <div style="display: flex;padding-top:5px;  justify-content: right;align-items: center">
        <div>当前人气：70/100</div>
      </div>
    </div>
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';


export default {
  data() {
    return {};
  },
  components: {},
  props: {
    theme: {type: Object | Array},
    setData: {type: Object | Array},
    cId: {type: Number},
    noEditor: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {
  },
  mounted() {
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    format(percentage) {
      if (!this.setData.percentFlag) {
        return percentage = '';
      }
      return percentage === 100 ? '满' : `${percentage}%`;
    }
  },
  watch: {
    setData(newVal, oldVal) {
    },
    componentsList(newVal, oldVal) {          //添加的时候出发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    }
  },
  beforeDestroy() {
    // this.$root.Bus.$off('addHotSpot')
  }
};
</script>
<style lang='less' scoped>

@import '../../colorui/main.css';
@import '../../colorui/icon.css';

</style>
