<template>
  <div class="pageSetting">
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">团员组件</p>
      <div slot="mainContent">
        <el-divider>基础属性</el-divider>
        <el-form ref="form" label-width="100px" :model="formData">
          <el-form-item label="页面上距">
            <el-input v-model="formData.pageMarginTop" size="mini" type="number" style="margin-top: 5px"
                      placeholder="与上面元素的间隔">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="页面下距">
            <el-input v-model="formData.pageMarginBottom" size="mini" type="number" style="margin-top: 5px"
                      placeholder="与下面元素的间隔">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-divider>底部提示设置</el-divider>
          <el-tabs v-model="formData.tabActiveName" @tab-click="handleClick">
            <el-tab-pane label="未满团" name="1">
              <el-form-item label="未满团描述">
                <el-input v-model="formData.emptyMsg" size="mini" style="margin-top: 5px" placeholder="文字大小">
                </el-input>
              </el-form-item>
            </el-tab-pane>
            <el-tab-pane label="满团后" name="2">
              <el-form-item label="满团后描述">
                <el-input v-model="formData.fullMsg" size="mini" style="margin-top: 5px" placeholder="文字大小">
                </el-input>
              </el-form-item>
            </el-tab-pane>
            <el-tab-pane label="单买成团" name="3">
              <el-form-item label="满团后描述">
                <el-input v-model="formData.soloMsg" size="mini" style="margin-top: 5px" placeholder="文字大小">
                </el-input>
              </el-form-item>
            </el-tab-pane>
          </el-tabs>
        </el-form>
      </div>
    </settingSlot>
    <!--    <p style="display:none">{{getData}}</p>-->
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';

import settingSlot from '../settingSlot'
// import bgColorSelect from "../../pages/page-components/bgColorSelect";

export default {
  components: {settingSlot},
  data() {
    return {
      formDataCopy: {
        tabActiveName: 1,
        pageMarginTop: 0,
        pageMarginBottom: 0,
        emptyMsg: "点击加入拼团吧！",//底部内容
        fullMsg: "已经满团无法参加！",//底部内容
        soloMsg: "恭喜您一人成团成功！",//底部内容
      },
      formData: {}
    };
  },
  props: {
    thememobile: {type: Object | Array},
    showData: {
      type: Object,
      default: () => {
      }
    },
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
      clickComIndex: state => state.divpage.clickComIndex,
    })
  },
  mounted() {
    let that = this;
    if (that.IsEmptyObj(that.showData)) {

      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
    } else {
      that.formData = that.showData
    }
    console.log("111  ", this.formData)
    that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
    // that.updateData({
    //   componentsList: that.componentsList
    // })
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    // 删除按钮
    delBtn(index) {
      let that = this;
      that.$confirm('是否删除该按钮?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
        that.updateData({componentsList: that.componentsList});
      }).catch(() => {
      })
    },
    cancel() {
      this.$emit('cancel')
    },
    reset() {
      let that = this;
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      that.componentsList[that.clickComIndex]['data'] = this.formData;
    },
    confirm() {
      this.$emit('confirm', this.formData)
    },
    delete() {
      this.$emit('delete')
    },
    handleClick(tab, event) {
      this.formData.tabActiveName = tab.name;
    }
  },

  watch: {
    showData: {
      handler(newVal, oldVal) {
        this.formData = newVal ? newVal : this.formData;
      },
      deep: true
    },
    thememobile() {
    },
  }
};
</script>
<style lang='less' scoped>

.el-form-item {
  margin-bottom: 0;
}
</style>
