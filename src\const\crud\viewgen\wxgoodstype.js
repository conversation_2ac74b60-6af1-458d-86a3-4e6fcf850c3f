export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  excelBtn: true,
  printBtn: true,
  viewBtn: true,
  searchMenuSpan: 6,
  column: [
    {
      label: '主键',
      prop: 'id',
    },
    {
      label: '创建者',
      prop: 'createId',
    },
    {
      label: '创建时间',
      prop: 'createTime',
      sortable: true,
      rules: [
      ]
    },
    {
      label: '更新者',
      prop: 'updateId',
      sortable: true,
      rules: [
        {
          max: 32,
          message: '长度在不能超过32个字符'
        },
      ]
    },
    {
      label: '更新时间',
      prop: 'updateTime',
      sortable: true,
      rules: [
      ]
    },
    {
      label: '逻辑删除标记（0：显示；1：隐藏）',
      prop: 'delFlag',
      sortable: true,
      rules: [
        {
          max: 2,
          message: '长度在不能超过2个字符'
        },
      ]
    },
    {
      label: '是否置顶标记（0：是；1：否）',
      prop: 'topFlag',
      sortable: true,
      rules: [
        {
          max: 2,
          message: '长度在不能超过2个字符'
        },
      ]
    },
    {
      label: '所属租户',
      prop: 'tenantId',
      sortable: true,
      rules: [
        {
          required: true,
          message: '请输入所属租户',
          trigger: 'blur'
        },
        {
          max: 32,
          message: '长度在不能超过32个字符'
        },
      ]
    },
    {
      label: '类别名称',
      prop: 'name',
      sortable: true,
      rules: [
        {
          max: 25,
          message: '长度在不能超过25个字符'
        },
      ]
    },
  ]
}
