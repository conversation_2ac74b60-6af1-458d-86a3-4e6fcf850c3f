export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  index: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  excelBtn: true,
  printBtn: true,
  viewBtn: true,
  menu:false,
  searchMenuSpan: 6,
  column: [
    {
      label: '主键',
      prop: 'id',
      hide: true, // 隐藏主键字段
    },
    {
      label: '订单单号',
      prop: 'orderNo',
    },
    {
      label: '订单名',
      prop: 'name',
    },
    {
      label: '订单状态',
      prop: 'statusDesc', // 显示订单状态
      slot: true, // 使用自定义插槽渲染状态样式
    },
    {
      label: '退款状态',
      prop: 'refundStatus', // 显示退款状态
      slot: true, // 使用自定义插槽渲染
    },
    {
      label: '订单类型',
      prop: 'orderType',
      hide: true, // 隐藏订单类型字段
      dicData: [{
        label: '普通订单',
        value: '0'
      }, {
        label: '砍价订单',
        value: '1'
      }, {
        label: '拼团订单',
        value: '2'
      }, {
        label: '秒杀订单',
        value: '3'
      }, {
        label: '按钮订单',
        value: '4'
      }]
    },
    {
      label: '页面名称',
      prop: 'pageName',
      dicData: [ {
        label: '页面已删除',
        value: ''
      }],
      filterMethod:function(value, row, column) {
        return row.pageType === value;
      }
    },
    {
      label: '公众号',
      prop: 'appId',
      dicData: []
    },
    {
      label: '用户名称',
      prop: 'nickName',
    },
    {
      label: '用户手机号',
      prop: 'phone',
    },
     {
      label: '订单手机号',
      prop: 'orderPhone',
    },
    {
      label: '创建时间',
      prop: 'createTime',
      sortable: true,
    },
    {
      label: '交易类型',
      prop: 'tradeType',
      hide: true, // 隐藏交易类型字段
    },
    {
      label: '是否支付',
      prop: 'isPay',
      sortable: true,
      dicData: [{
        label: '否',
        value: '0'
      }, {
        label: '是',
        value: '1'
      }]
    },

    {
      label: '购买商品',
      prop: 'purchaseProduct',
      slot: true, // 使用自定义插槽渲染
    },
    {
      label: '支付金额',
      prop: 'paymentPrice',

    },
    {
      label: '付款时间',
      prop: 'paymentTime',
      sortable: true,

    },
    {
      label: '支付交易ID',
      prop: 'transactionId',
    },


  ]
}
