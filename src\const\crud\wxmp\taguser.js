export const tableOption = {
  dialogDrag:true,
  border: true,
  index: false,
  indexLabel: '序号',
  stripe: true,
  selection: true,
  menuAlign: 'center',
  align: 'center',
  editBtn: false,
  delBtn: false,
  addBtn: false,
  excelBtn: true,
  printBtn: true,
  viewBtn: false,
  searchShow: false,
  searchBtn: false,
  menuWidth: 150,
  menuType:'text',
  columnBtn: false,//列的显隐按钮
  searchMenuSpan: 6,
  defaultSort:{
    prop: 'subscribeTime',
    order: 'descending'
  },
  column: [
    {
      label: '头像',
      prop: 'headimgUrl',
      imgWidth:50,
      width:80,
      dataType: 'string',
      type: 'upload',
      listType: 'picture-img',
      editDisplay:false
    },
    {
      label: '昵称',
      prop: 'nickName',
      width:100,
      sortable:true,
      // search:true,
      editDisplay:false
    },
    {
      label: '是否订阅',
      prop: 'subscribe',
      width:80,
      type: 'select',
      // sortable:true,
      editDisplay:false,
      slot:true,
      dicUrl: '/upms/dict/type/wx_subscribe'
    },
    {
      label: '电话',
      width:100,
      prop: 'phone',
    },
    {
      label: 'openId',
      prop: 'openId',
      width:130,
    },
    {
      label: 'union_id',
      prop: 'unionId',
      width:130,
      // search:true
    },
    {
      label: '标签',
      prop: 'tagList',
      slot: true,
      editDisplay: false
    },
    {
      label: '关注渠道',
      prop: 'subscribeScene',
      type: 'select',
      sortable:true,
      width:100,
      // search:true,
      editDisplay:false,
      dicUrl: '/upms/dict/type/wx_subscribe_scene'
    },
    {
      label: '关注时间',
      prop: 'subscribeTime',
      type: 'datetime',
      width:95,
      sortable:true,
      editDisplay:false
    },
    {
      label: '性别',
      prop: 'sex',
      width: 80,
      type: 'select',
      sortable:true,
      // search:true,
      editDisplay:false,
      slot:true,
      dicUrl: '/upms/dict/type/wx_sex'
    },

    {
      label: '备注',
      prop: 'remark',
      width:100,
    },
    {
      label: '二维码扫码场景',
      prop: 'qrSceneStr',
      type: 'select',
      dicUrl: '/upms/dict/type/wx_qr_scene_str',
      width: 130,
      sortable: true,
      // search: true,
      editDisplay: false
    },
    {
      label: '关注次数',
      prop: 'subscribeNum',
      width:100,
      sortable:true,
      editDisplay:false
    },
    {
      label: '创建时间',
      prop: 'createTime',
      type: 'datetime',
      sortable:true,
      hide:true,
      editDisplay:false
    },
    {
      label: '更新时间',
      prop: 'updateTime',
      type: 'datetime',
      sortable:true,
      hide:true,
      editDisplay:false
    },
    {
      label: '取关时间',
      prop: 'cancelSubscribeTime',
      type: 'datetime',
      sortable:true,
      hide:true,
      editDisplay:false
    },
    {
      label: '最近定位',
      prop: 'latitude',
      slot: true,
      editDisplay: false,
      viewDisplay: false,
      width: 80
    }
  ]
}
