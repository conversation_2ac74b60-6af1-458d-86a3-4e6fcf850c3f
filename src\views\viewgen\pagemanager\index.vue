<template>
  <div class="execution">
    <basic-container>
      <el-form label-width="100px" :model="form">
        <el-row type="flex" justify="start">
          <el-form-item label="所选公众号:">
            <el-select size="small" v-model="appIdList" @change="appChange" placeholder="请选择" multiple collapse-tags>
              <el-option
                v-for="item in wxAppList"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="页面选择:">
            <el-select size="small" style="width: 150px" v-model="currentPageTypeList" @change="pageTypeChange"
                       placeholder="请选择类型" multiple collapse-tags>
              <el-option
                v-for="item in pageTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button size="small" type="primary" @click="getPage(page)">确认搜索</el-button>
            <el-button size="small" @click="reset">重置</el-button>
          </el-form-item>
        </el-row>
        <el-row type="flex" justify="start">
        </el-row>
      </el-form>
      <avue-crud ref="crud"
                 :page.sync="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 v-model="form"
                 @on-load="getPage"
                 @refresh-change="refreshChange"
                 @row-update="handleUpdate"
                 @row-del="handleDel"
                 @sort-change="sortChange"
                 @size-change="sizeChange"
                 @search-change="searchChange">
        <template slot-scope="scope" slot="menuLeft">
          <el-button type="primary"
                     icon="el-icon-plus"
                     size="small"
                     @click.stop="openAddBox">新增
          </el-button>
        </template>
        <template slot-scope="scope" slot="menu">
          <el-button @click="copyPage(scope.row)" icon="el-icon-document-copy" type="text"
                     size="small">复制页面
          </el-button>
          <el-button v-if="scope.row.pageType ==4" @click="getFormData(scope.row)" icon="el-icon-s-data" type="text"
                     size="small">查看数据
          </el-button>
          <el-button v-if="scope.row.pageType ==5" @click="openBindPageList(scope.row)" icon="el-icon-document"
                     type="text"
                     size="small">设置绑定页
          </el-button>
          <el-button v-if="scope.row.pageType ==2" @click="getGrouponData(scope.row)" icon="el-icon-s-data" type="text"
                     size="small">查看数据
          </el-button>
          <el-button v-if="permissionList.editBtn" @click="editView(scope.row)" icon="el-icon-edit" type="text"
                     size="small">编辑
          </el-button>
          <el-button v-if="permissionList.delBtn" @click="handleDel(scope.row,scope.index)" icon="el-icon-delete"
                     type="text" size="small">删除
          </el-button>
        </template>
        <template slot-scope="scope" slot="enable">
          <el-switch
            v-model="scope.row.enable"
            active-value="0"
            inactive-value="1"
            active-color="#13ce66"
            inactive-color="#ff4949"
            @change="handleUpdate(scope.row)">
          </el-switch>
        </template>
      </avue-crud>
    </basic-container>

    <!--     提交表单-->
    <el-dialog
      title="新建页面"
      :visible.sync="addBoxVisible"
      :close-on-click-modal="false"
      @close="resetForm"
      center
      :append-to-body="true"
      lock-scroll
      width="40%">
      <div style="overflow: hidden">
        <el-form :rules="formRules" :ref="pageForm" :model="pageForm" label-width="auto">
          <el-form-item label="公众号" prop="appId">
            <el-select v-model="pageForm.appId" clearable placeholder="请选择">
              <el-option
                v-for="item in wxAppList"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="页面名称" prop="pageName">
            <el-input v-model="pageForm.pageName" :maxlength="25" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="页面类型" prop="pageType">
            <el-select v-model="pageForm.pageType" clearable placeholder="请选择">
              <el-option
                v-for="item in pageTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="使用模版" prop="templateFlag">
            <el-switch
              v-model="pageForm.templateFlag"
              active-color="#13ce66"
              inactive-color="#ff4949">
            </el-switch>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="saveFrom">确认</el-button>
            <el-button @click="addBoxVisible = false">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
    <!--     客片绑定页列表-->
    <el-dialog
      title="客片绑定页列表"
      :visible.sync="imgShareBindPageVisible"
      :close-on-click-modal="false"
      center
      :append-to-body="true"
      lock-scroll
      width="90%">
      <div style="overflow: hidden">
        <img-share-bind-page ref="imgShareBindPageRef" :obj="imgShareBindObj"></img-share-bind-page>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  getPage,
  addObj,
  putObj,
  delObj,
  getListByTypeList,
  copyPage,
} from '@/api/viewgen/pagedevise'
import {getList as getWxAppList} from '@/api/wxmp/wxapp'
import {tableOption} from '@/const/crud/viewgen/viewmanger.js'
import {mapGetters} from 'vuex'
import imgShareBindPage from "@/views/viewgen/pagemanager/imgsharebindpage";

export default {
  name: 'pagemanager',

  components: {
    imgShareBindPage,
  },
  data() {
    return {
      appIdList: [],//所选公众号
      wxAppList: [],
      addBoxVisible: false,
      imgShareBindPageVisible: false,
      imgShareBindObj: '',// 客片分享的页面Id
      pageForm: {
        appId: '',
        pageName: '',
        pageType: '',
        templateFlag: false,
      },
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      formRules: {
        pageName: [
          {required: true, message: '请输入页面名称', trigger: 'submit'},
        ],
        pageType: [
          {required: true, message: '请选择页面类型', trigger: 'submit'},
        ],
        appId: [
          {required: true, message: '请选择公众号', trigger: 'submit'}
        ],
      },
      currentPageTypeList: [],//当前页面类型
      pageTypeList: [{
        value: '1',
        label: '首页'
      }, {
        value: '2',
        label: '拼团活动'
      }, {
        value: '3',
        label: '作品详情'
      }, {
        value: '4',
        label: '图文表单'
      }, {
        value: '5',
        label: '客照分享'
      }, {
        value: '6',
        label: '档期选择'
      }]
    }
  },
  created() {
    getWxAppList({
    }).then(res => {
      let data = res.data
      this.wxAppList = data;
      // //默认加载第一个公众号的素材
      for (let i = 0; i < data.length; i++) {
        tableOption.column[2].dicData.push({label: data[i].name, value: data[i].id});
      }
    }).catch(() => {
    })
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.permissions['weixin:wxpagedevise:add'] ? true : false,
        delBtn: this.permissions['weixin:wxpagedevise:del'] ? true : false,
        editBtn: this.permissions['weixin:wxpagedevise:edit'] ? true : false,
        viewBtn: this.permissions['weixin:wxpagedevise:get'] ? true : false
      };
    }
  },
  methods: {
    openAddBox() {
      this.addBoxVisible = true;
    },
    searchChange(params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    sortChange(val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    sizeChange(val) {
      this.page.pageSize = val;
    },
    getPage(page, params) {
      this.tableLoading = true
      // if (!page) {
      //   page = this.page;
      // }
      let obj = Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
        appIdList: this.appIdList,
        pageTypeList: this.currentPageTypeList,
      }, params, this.paramsSearch)
      getPage(obj).then(res => {
        this.tableData = res.data.data.records
        this.page.total = res.data.data.total
        this.page.currentPage = res.data.data.current
        this.page.pageSize = res.data.data.size
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel(row, index) {
      console.log(row);
      this.$confirm('是否确认删除此数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        return delObj(row.id)
      }).then(() => {
        this.$message({
          showClose: true,
          message: '删除成功',
          type: 'success'
        })
        this.getPage(this.page)
      }).catch(function (err) {
      })
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate(row, index, done) {
      putObj(row).then(res => {
        this.$message({
          showClose: true,
          message: '修改成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
      })
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave (row, done, loading) {
      addObj(row).then(res => {
        this.$message({
          showClose: true,
          message: '添加成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page)
    },
    //编辑页面
    editView(row) {
      console.log(row)
      //路由跳转
      this.$router.push({name: '页面装修', params: {id: row.id}});
    },
    //查看表单数据
    getFormData(row) {
      console.log(row)
      //路由跳转
      this.$router.push({name: '表单数据', params: {id: row.id}});
    },
    //查看拼团数据
    getGrouponData(row) {
      console.log(row)
      //路由跳转
      this.$router.push({name: '拼团数据', params: {id: row.id}});
    },
    //查看客片绑定页面
    openBindPageList(row) {
      console.log("查看客片绑定页面",row)
      this.imgShareBindObj = row;
      this.imgShareBindPageVisible = true;
    },
    saveFrom() {
      console.log(this.pageForm)

      this.$refs[this.pageForm].validate((valid) => {
        if (valid) {
          addObj(this.pageForm).then(res => {
            this.$message({
              showClose: true,
              message: '添加成功',
              type: 'success'
            })
            this.addBoxVisible = false;
            this.refreshChange()
          }).catch(() => {
          })
        }
      });
    },
    resetForm() {
      this.$refs[this.pageForm].resetFields();
    },
    //公众号类型更改
    appChange() {
      this.getPageByType()
    },
    //页面类型更改
    pageTypeChange() {
      this.getPageByType()
    },
    //拿取具体页面
    getPageByType() {
      if (!this.currentPageTypeList || this.currentPageTypeList.length == 0) {
        this.pageList = [];
        this.pageId = '';
        return;
      }
      let params = {
        appIdList: this.appIdList,
        pageTypeList: this.currentPageTypeList,
      }
      console.log("拿取类型1", params);
      getListByTypeList(Object.assign(params)).then(res => {
        console.log("拿取类型", res.data.data);
        this.pageList = res.data.data;
      }).catch()
    },
    //重置页面
    reset() {
      this.appIdList = [];
      this.currentPageTypeList = [];
      this.currentOrderTypeList = [];
      this.pageList = []
      this.pageId = '';
      this.getPage(this.page)
    },
    //复制页面
    copyPage(obj) {
      this.$confirm('是否确认复制此页面', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let data = {
          id: obj.id
        }
        return copyPage(data)
      }).then(data => {
        this.$message({
          showClose: true,
          message: '复制成功',
          type: 'success'
        })
        this.getPage(this.page)
      }).catch(function (err) {
      })
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
