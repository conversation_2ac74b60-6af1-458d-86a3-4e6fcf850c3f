<template>
  <div class="textComponent" :style="{background: `${setData.backgroundColor}`, marginBottom: `${setData.pageSpacing}px`}">
    <div style="width: 100%;text-align: center;"
         :style="{height: `${setData.height}px`,lineHeight: `${setData.height}px`,color: `${setData.color}`,fontSize: `${setData.fontSize}px`}">
      {{setData.title}}
    </div>
  </div>
</template>

<script>
import { mapState , mapGetters , mapMutations , mapActions } from 'vuex';
import placeholderImg from "../../pages/page-components/placeholderImg";

export default {
  data() {
    return {};
  },
  components: { placeholderImg },
  props: {
    theme : { type: Object | Array },
    setData : { type: Object | Array },
    cId     : { type: Number },
    noEditor: {
      type: <PERSON>olean,
      default: false,
    }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {
  },
  mounted() {
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
  },
  watch:{
    setData(newVal, oldVal){},
    componentsList(newVal, oldVal){          //添加的时候出发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    }
  },
  beforeDestroy(){
    // this.$root.Bus.$off('addHotSpot')
  }
};
</script>
<style lang='less' scoped>
.el-carousel__item--card{

}
.textComponent {
  position: relative;
  display: block;
  width: 100%;
  background: #ffffff;
}
</style>
