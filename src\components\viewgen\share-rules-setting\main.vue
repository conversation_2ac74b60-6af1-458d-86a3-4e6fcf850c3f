<template>
  <div>
    <el-form ref="form" class="share_form" :model="shareForm" label-width="100px">
      <el-divider>基础属性</el-divider>
      <el-form-item label="参与时长">
        <el-input-number size="mini" v-model="shareForm.durationTime"></el-input-number> 小时
      </el-form-item>
      <el-form-item label="到期遮照" prop="expireCoverImg">
        <MaterialList :value="shareForm.expireCoverImg?[shareForm.expireCoverImg]:[]"  @sureSuccess="shareForm.expireCoverImg = $event?$event[0]:''" @deleteMaterial="shareForm.expireCoverImg = ''"
                      type="image"  :divStyle="'width:100%;margin-bottom:0px;height:90px;line-height: 90px;'" :num=1 ></MaterialList>
<!--        <div class="image_preview">-->
<!--          <el-image-->
<!--            v-show="shareForm.expireCoverImg"-->
<!--            class="image_preview_image"-->
<!--            fit="fill"-->
<!--            :src="shareForm.expireCoverImg"-->
<!--            :preview-src-list="[shareForm.expireCoverImg]">-->
<!--          </el-image>-->
<!--          <el-upload-->
<!--            :file-list="uploadExpireCover.uploadList"-->
<!--            :show-file-list="false"-->
<!--            v-show="!shareForm.expireCoverImg"-->
<!--            :action="serverUrl"-->
<!--            :headers="header"-->
<!--            :before-upload="beforeUpload"-->
<!--            accept=".jpg,.jpeg,.png,.bmp,.JPG,.JPEG,.PNG,.BMP"-->
<!--            :on-success="(res,file,fileList)=>{return uploadSuccess(res,file,fileList,'expireCover',uploadExpireCover)}"-->
<!--            :on-error="(res)=>{return uploadError(res,uploadExpireCover)}"-->
<!--            :on-change="(file,fileList)=>{return handleUploadChange(file,fileList,uploadExpireCover)}"-->
<!--          >-->
<!--            <el-image class="waite_upload_img">-->
<!--              <div style="margin-top:32%" slot="error">-->
<!--                <i v-show="uploadExpireCover.uploadIconVisible" class="el-icon-upload2">点击上传</i>-->
<!--                <el-progress v-show="uploadExpireCover.processIconVisible" type="circle" :width="55"-->
<!--                             :percentage="uploadExpireCover.percentage"></el-progress>-->
<!--              </div>-->
<!--            </el-image>-->
<!--          </el-upload>-->
<!--          <div v-show="shareForm.expireCoverImg" class="img_dialog">-->
<!--                <span @click="handleRemove('expireCover',uploadExpireCover,null)">-->
<!--                  <i class="el-icon-delete"></i>-->
<!--                </span>-->
<!--          </div>-->
<!--        </div>-->
      </el-form-item>
      <el-form-item label="未发布遮照" prop="unPubCoverImg">
        <MaterialList :value="shareForm.unPubCoverImg?[shareForm.unPubCoverImg]:[]"  @sureSuccess="shareForm.unPubCoverImg = $event?$event[0]:''" @deleteMaterial="shareForm.unPubCoverImg = ''"
                      type="image"  :divStyle="'width:100%;margin-bottom:0px;height:90px;line-height: 90px;'" :num=1 ></MaterialList>
      </el-form-item>
      <el-form-item label="达标票数">
        <el-input-number v-model="shareForm.aimNum" type="number" :precision="0" :min="0" :max="99999999"
                         size="mini"></el-input-number>
      </el-form-item>
<!--      <el-divider>好友首次动作赠送积分</el-divider>-->
<!--      <el-form-item label="首次分享">-->
<!--        <el-input-number v-model="shareForm.firstShare" type="number" :min="0" :max="999999999"-->
<!--                         size="mini"></el-input-number>-->
<!--      </el-form-item>-->
      <el-divider>模版消息</el-divider>
      <el-form-item label="发布通知">
        <div @click="openMessageBox('releaseMsgId')">
          <el-input v-model="shareForm.releaseMsgName"  style="margin-top: 5px;" size="mini" :readonly="true"
                    placeholder="客片发布后的通知">
            <el-button @click.stop="delMsg('releaseMsgId')"  slot="append" icon="el-icon-delete">删除</el-button>
          </el-input>
        </div>
      </el-form-item>
      <el-form-item label="达标通知">
        <div @click="openMessageBox('completeMsgId')">
          <el-input v-model="shareForm.completeMsgName"  style="margin-top: 5px;" size="mini" :readonly="true"
                    placeholder="祝福达标后的通知">
            <el-button @click.stop="delMsg('completeMsgId')"  slot="append" icon="el-icon-delete">删除</el-button>
          </el-input>
        </div>
      </el-form-item>
      <el-form-item label="祝福通知">
        <div @click="openMessageBox('voteMsgId')">
          <el-input v-model="shareForm.voteMsgName"  style="margin-top: 5px;" size="mini" :readonly="true"
                    placeholder="客片分享人收到的通知">
            <el-button @click.stop="delMsg('voteMsgId')"  slot="append" icon="el-icon-delete">删除</el-button>
          </el-input>
        </div>
      </el-form-item>
      <el-divider>点赞设置</el-divider>
      <el-tabs v-model="tabActiveName">
        <el-tab-pane label="已关注粉丝" name="0">
          <el-form-item label="每日次数">
            <el-input-number v-model="shareForm.subDayNum" size="small" controls-position="right" :min="1" :max="100"></el-input-number>
          </el-form-item>
          <el-form-item label="每次积分">
            <el-input-number v-model="shareForm.subScore" size="small" controls-position="right" :min="1" :max="100"></el-input-number>
          </el-form-item>
          <el-form-item label="间隔限制">
            <el-input-number v-model="shareForm.subInterval" size="small" controls-position="right" :min="1" :max="100"></el-input-number> 秒
          </el-form-item>
          <el-form-item label="成功提示">
            <MaterialList :value="shareForm.subSucMsg?[shareForm.subSucMsg]:[]"  @sureSuccess="shareForm.subSucMsg = $event?$event[0]:''" @deleteMaterial="shareForm.subSucMsg = ''"
                          type="image"  :divStyle="'width:100%;margin-bottom:0px;height:90px;line-height: 90px;'" :num=1 ></MaterialList>
          </el-form-item>
          <el-form-item label="上限提示">
            <MaterialList :value="shareForm.subFailMsg?[shareForm.subFailMsg]:[]"  @sureSuccess="shareForm.subFailMsg = $event?$event[0]:''" @deleteMaterial="shareForm.subFailMsg = ''"
                          type="image"  :divStyle="'width:100%;margin-bottom:0px;height:90px;line-height: 90px;'" :num=1 ></MaterialList>
          </el-form-item>
          <el-form-item label="频率提示">
            <MaterialList :value="shareForm.subLimitMsg?[shareForm.subLimitMsg]:[]"  @sureSuccess="shareForm.subLimitMsg = $event?$event[0]:''" @deleteMaterial="shareForm.subLimitMsg = ''"
                          type="image"  :divStyle="'width:100%;margin-bottom:0px;height:90px;line-height: 90px;'" :num=1 ></MaterialList>
          </el-form-item>
        </el-tab-pane>
        <el-tab-pane label="未关注粉丝" name="1">
          <el-form-item label="每日次数">
            <el-input-number v-model="shareForm.unSubDayNum"  size="small" controls-position="right" :min="1" :max="100"></el-input-number>
          </el-form-item>
          <el-form-item label="每次积分">
            <el-input-number v-model="shareForm.unSubScore"  size="small" controls-position="right" :min="1" :max="100"></el-input-number>
          </el-form-item>
          <el-form-item label="间隔限制">
            <el-input-number v-model="shareForm.unSubInterval"  size="small" controls-position="right" :min="1" :max="100"></el-input-number>
          </el-form-item>
          <el-form-item label="成功提示">
            <MaterialList :value="shareForm.unSubSucMsg?[shareForm.unSubSucMsg]:[]"  @sureSuccess="shareForm.unSubSucMsg = $event?$event[0]:''" @deleteMaterial="shareForm.unSubSucMsg = ''"
                          type="image"  :divStyle="'width:100%;margin-bottom:0px;height:90px;line-height: 90px;'" :num=1 ></MaterialList>
          </el-form-item>
          <el-form-item label="上限提示">
            <MaterialList :value="shareForm.unSubFailMsg?[shareForm.unSubFailMsg]:[]"  @sureSuccess="shareForm.unSubFailMsg = $event?$event[0]:''" @deleteMaterial="shareForm.unSubFailMsg = ''"
                          type="image"  :divStyle="'width:100%;margin-bottom:0px;height:90px;line-height: 90px;'" :num=1 ></MaterialList>
          </el-form-item>
          <el-form-item label="频率提示">
            <MaterialList :value="shareForm.unSubLimitMsg?[shareForm.unSubLimitMsg]:[]"  @sureSuccess="shareForm.unSubLimitMsg = $event?$event[0]:''" @deleteMaterial="shareForm.unSubLimitMsg = ''"
                          type="image"  :divStyle="'width:100%;margin-bottom:0px;height:90px;line-height: 90px;'" :num=1 ></MaterialList>
          </el-form-item>
        </el-tab-pane>
      </el-tabs>
      <el-divider>内部数据</el-divider>
      <el-alert
        title="提示："
        description="数值为0则代表对应的分享不赠送内部助力；下方的赠送人数不得超过设定的投票人数。"
        type="success"
        :closable="false"
        style="margin-bottom: 10px">
      </el-alert>
<!--      <el-form-item label="首次分享">-->
<!--        <el-input-number v-model="shareForm.internalDataFirst" type="number" :min="0" :max="999999999"-->
<!--                         size="mini"></el-input-number>-->
<!--      </el-form-item>-->
      <el-form-item label="10小时">
        <el-input-number v-model="shareForm.internalDataTen" type="number" :min="0" :max="999999999"
                         size="mini"></el-input-number>
      </el-form-item>
      <el-form-item label="16小时">
        <el-input-number v-model="shareForm.internalDataSixteen" type="number" :min="0" :max="999999999"
                         size="mini"></el-input-number>
      </el-form-item>
      <el-form-item label="32小时">
        <el-input-number v-model="shareForm.internalDataThirtyTwo " type="number" :min="0" :max="999999999"
                         size="mini"></el-input-number>
      </el-form-item>
      <el-form-item label="38小时">
        <el-input-number v-model="shareForm.internalDataThirtyEight" type="number" :min="0" :max="999999999"
                         size="mini"></el-input-number>
      </el-form-item>
    </el-form>

    <!--      微信消息-->
    <el-dialog
      title="订阅消息"
      :visible.sync="qrMessageBoxVisible"
      :close-on-click-modal="false"
      center
      :append-to-body="true"
      lock-scroll
      width="80%">
      <div style="overflow: hidden">
        <qrMessage :selectedAppId="this.appId" :type="'1'" v-on:ensureMsg="ensureMsg" @backFun="ensureMsg"></qrMessage>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {getObjByPage, putObj} from '@/api/viewgen/imgsharerules'
import store from "@/store";
import MaterialList from '@/components/material/wxlist.vue'
import qrMessage from '@/views/wxmp/wxmpqrcodemessage' // 扫码消息

export default {
  name: "shareRulesSetting",
  props: {
    pageId: [String],
    appId: [String],
  },
  components: {MaterialList,qrMessage},
  data() {
    return {
      qrMessageBoxVisible:false, //扫码消息框
      qrMessageBoxFlag:'', //扫码消息标志
      tabActiveName:0,
      shareForm: {
        startTime: '',
        endTime: '',
        durationTime: 0,
        expireCoverImg: '',
        unPubCoverImg: '',
        aimNum: 0,
        releaseMsgId:'',
        releaseMsgName:'',
        completeMsgId:'',
        completeMsgName:'',
        voteMsgId:'',
        voteMsgName:'',
        firstShare: 0,
        firstSub: 0,
        internalDataFirst: 0,
        internalDataTen: 0,
        internalDataSixteen: 0,
        internalDataThirtyTwo: 0,
        internalDataThirtyEight: 0,
      },
    }
  },
  created() {
  },
  mounted() {
    this.get();
  },
  computed: {},
  methods: {
    //获取数据
    get() {
      if (this.pageId && this.pageId != 'null') {
        getObjByPage(this.pageId).then(res => {
          this.shareForm = res.data.data;
          console.log("查询结果",this.shareForm);
        });
      }
    },
    update() {
      console.log("客片分享修改",this.shareForm)
      putObj(this.shareForm).then(res => {
      });
    },
    //上传图片
    beforeUpload(file, item) {
      const isLt1M = file.size / 1024 / 1024 < 1;
      if (!isLt1M) {
        this.$message.error('上传图片大小不能超过 1MB!');
      }
      return isLt1M;
    },
    uploadSuccess(res, file, fileList, type, item) {
      if (type == 'expireCover') {
        this.shareForm.expireCoverImg = res.link;
      }
      if (type == 'unPubCover') {
        this.shareForm.unPubCoverImg = res.link;
      }
      this.$message.success("上传成功")
    },
    uploadError(res, item) {
      item.uploadIconVisible = true;
      item.processIconVisible = false;
      item.uploadDisabled = false;
      this.$message.error("上传失败")
    },
    handleUploadChange(file, fileList, item) {
      if (file.status == "ready") {
        item.percentage = file.percentage;
        item.uploadDisabled = true;
        item.uploadIconVisible = false;
        item.processIconVisible = true;
      } else if (file.status == "success") {
        item.uploadIconVisible = true;
        item.processIconVisible = false;
        item.uploadDisabled = false;
      }
    },
    handleRemove(type, item, index) {
      if (type == 'expireCover') {
        this.shareForm.expireCoverImg = '';
        return
      }
      if (type == "unPubCover") {
        this.shareForm.unPubCoverImg = '';
        return
      }
    },
    openMessageBox(flag) {
      this.qrMessageBoxFlag = flag;
      this.qrMessageBoxVisible = true;
    },
    delMsg(type) {
      if (type == 'releaseMsgId') {
        this.shareForm.releaseMsgId = '';
        this.shareForm.releaseMsgName = '';
      } else if (type == 'completeMsgId') {
        this.shareForm.completeMsgId = '';
        this.shareForm.completeMsgName = '';
      } else if (type == 'voteMsgId') {
        this.shareForm.voteMsgId = '';
        this.shareForm.voteMsgName = '';
      }
      this.$forceUpdate();
    },
    ensureMsg(obj) {
      console.log("确认消息", obj)
      if (this.qrMessageBoxFlag == 'releaseMsgId') {
        this.shareForm.releaseMsgId = obj.id;
        this.shareForm.releaseMsgName = obj.name;
      } else if (this.qrMessageBoxFlag == 'completeMsgId') {
        this.shareForm.completeMsgId = obj.id;
        this.shareForm.completeMsgName = obj.name;
      } else if (this.qrMessageBoxFlag == 'voteMsgId') {
        this.shareForm.voteMsgId = obj.id;
        this.shareForm.voteMsgName = obj.name;
      }
      this.qrMessageBoxVisible = false;
    },
  }
};
</script>

<style lang="scss" scoped>

.share_form{
  overflow: scroll;
  height:80vh;
}

.el-form-item {
  margin-bottom: 0;
}

.img_dialog {
  position: absolute;
  left: 80%;
  top: 0;
  width: 30px;
  opacity: 0;
}

.image_preview {
  position: relative;
  float: left;
  display: inline;
  margin: 0px 15px 10px 0px;

  .image_preview_image {
    border: 1px solid transparent;
    width: 150px;
    height: 150px;
  }

  &:hover .img_dialog {
    text-align: center;
    cursor: default;
    text-align: center;
    color: #fff;
    opacity: 1;
    font-size: 20px;
    background-color: rgba(0, 0, 0, .5);
    transition: opacity .3s;
  }
}

.waite_upload_img {
  border: 1px #8c939d dashed;
  border-radius: 6px;
  width: 150px;
  height: 150px
}
</style>
