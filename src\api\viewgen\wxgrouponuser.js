import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/wxgrouponuser/page',
    method: 'get',
    params: query
  })
}
export function getGroupPage(query) {
  return request({
    url: '/weixin/wxgrouponuser/grouppage',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/weixin/wxgrouponuser',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/weixin/wxgrouponuser/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/wxgrouponuser/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/wxgrouponuser',
    method: 'put',
    data: obj
  })
}

export function quicklyFinish(obj) {
  return request({
    url: '/weixin/wxgrouponuser/quicklyfinish',
    method: 'post',
    data: obj
  })
}

export function dynamicFinish(obj) {
  return request({
    url: '/weixin/wxgrouponuser/dynamicfinish',
    method: 'post',
    data: obj
  })
}

/**
 * 填充团员 （也可成团）
 * @param obj
 * @returns {*}
 */
export function bathFill(obj) {
  return request({
    url: '/weixin/wxgrouponuser/bathfill',
    method: 'post',
    data: obj
  })
}
