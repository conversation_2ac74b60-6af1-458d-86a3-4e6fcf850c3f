.avue-sidebar {
    padding-top: 120px;
    height: 100%;
    position: relative;
    background-color: #20222a;
    transition: width .6s;
    box-sizing: border-box;
    .el-menu{
    border-right: 0 !important;
    }
    .el-menu-item,
    .el-submenu__title {
        font-size: 14px;
        height: 56px;
        line-height: 56px;
    }
    .el-menu-item {
        &:hover {
            background-color: transparent !important;
            color: #fff;
            span,
            i {
                color: #fff;
            }
        }
        &.is-active {
            background-color: rgba(0, 0, 0, .8) !important;
            span,
            i {
                color: #fff;
            }
            &:hover {
                background-color: rgba(0, 0, 0, .8) !important;
            }
            &::before {
                content: " ";
                top: 0;
                left: 0;
                bottom: 0;
                width: 4px;
                background: $mainBg;
                position: absolute
            }
        }
    }
    .el-submenu__title {
        &:hover {
            i,
            span {
                color: #fff;
            }
            background-color:transparent !important;
        }
    }
    .el-submenu .el-menu-item {
        height: 50px;
        line-height: 50px;
        &.is-active {
            background-color: rgba(0, 0, 0, .8) !important;
            span,
            i {
                color: #fff
            }
            &:hover {
                background-color: rgba(0, 0, 0, .8) !important;
            }
        }
        &:hover {
            background-color: transparent !important;
            span,
            i {
                color: #fff;
            }
        }
    }
}
