import request from '@/router/axios'

export function getList(query) {
  return request({
    url: '/weixin/wxgoodstag/wxlist',
    method: 'get',
    params: query
  })
}

export function getSysList(query) {
  return request({
    url: '/weixin/wxgoodstag/list',
    method: 'get',
    params: query
  })
}

export function getTagAndType(query) {
  return request({
    url: '/weixin/wxgoodstag/getTagAndType',
    method: 'get',
    params: query
  })
}


export function addObj(obj) {
  return request({
    url: '/weixin/wxgoodstag/add',
    method: 'post',
    data: obj
  })
}

export function addAttention(obj) {
  return request({
    url: '/weixin/wxgoodstag/add/attention',
    method: 'post',
    data: obj
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/wxgoodstag/put',
    method: 'put',
    data: obj
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/wxgoodstag/'+ id,
    method: 'delete',
  })
}

