<template>
  <div class="singleLineInputComponent" :style="{background: `${setData.backgroundColor}`, marginBottom: `${setData.pageSpacing}px`}">
    <div :style="{color: `${setData.titleColor}`, fontSize: `${setData.titleSize}px`,fontWeight:`${setData.titleWeight?'bold':'normal'}`}">{{setData.title}}<i v-show="setData.required" style="color: #FF0000">*</i></div>
    <div :style="{color: `${setData.describeColor}`, fontSize: `${setData.describeSize}px`,fontWeight:`${setData.describeWeight?'bold':'normal'}`}">{{setData.describe}}</div>
    <div class="single_line_font_input_box padding">
      <div class="single_line_font_icon" :style="{color: `${setData.titleColor}`}" :class="setData.titleIcon" ></div>
      <input class="single_line_font_input" disabled="true" :placeholder="'请输入'+setData.title">
    </div>
  </div>
</template>

<script>
import { mapState , mapGetters , mapMutations , mapActions } from 'vuex';
export default {
  data() {
    return {};
  },
  components: { },
  props: {
    theme : { type: Object | Array },
    setData : { type: Object | Array },
    cId     : { type: Number },
    noEditor: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {
  },
  mounted() {
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
  },
  watch:{
    setData(newVal, oldVal){},
    componentsList(newVal, oldVal){          //添加的时候出发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    }
  },
  beforeDestroy(){
    // this.$root.Bus.$off('addHotSpot')
  }
};
</script>
<style lang='less' scoped>
.singleLineInputComponent {
  position: relative;
  display: block;
  width: 100%;
  padding: 5px;
  background: #ffffff;

  .single_line_font_input_box{
    padding: 2px;
    border: 1px solid rgba(118, 118, 118, 0.3);
  }
  .single_line_font_icon{
    display: inline-block;
    padding: 0 5px;
  }
  .single_line_font_input{
    flex: 1 1 0%;
    display: inline-block;
    border: 0;
    color:  #ffffff;
    width: 80%;
    position: relative;
    overflow: hidden;
  }
}

</style>
