<template>
  <div class="execution">
    <basic-container>
      <div>
        <el-row :gutter="20" style="height: 840px;">
          <el-col :span="16">
            <el-tabs v-model="activeName" @tab-click="activeName==$event.name">
              <el-tab-pane label="主题设置" name="name1"></el-tab-pane>
              <el-tab-pane label="首页底部TabBar设置" name="name5"></el-tab-pane>
            </el-tabs>
            <!-- 主题设置 -->
            <div v-show="activeName=='name1'">
              <el-form ref="form" :model="form" label-width="0px" :rules="rules">
                <el-row>
                  <el-col :span="12" style="padding-left: 20px; margin-top: 50px;">
                    <div style="font-size: 16px;color: #303133;">背景颜色</div>
                    <div style="font-size: 12px;color: #909399;">（导航栏、模块背景色值，可支持渐变色）</div>
                  </el-col>
                  <el-col :span="12" style="margin-top: 50px; margin-left: -100px;">
                    <el-form-item label="" prop="backgroundColor">
                      <el-tooltip effect="dark" content="colorUI中的类名" placement="top">
                        <el-input v-model="form.backgroundColor" size="small" @focus="appTheme.bgType='background',appTheme.showBgDialog=true">
                          <template slot="append">
                            <div style="background:#fff;width: 24px;padding-left: 5px;">
                              <div :class="'bg-' + form.backgroundColor" style="height: 20px;width: 20px;float: left;"></div>
                            </div>
                          </template>
                        </el-input>
                      </el-tooltip>
                    </el-form-item>
                  </el-col>

                  <el-col :span="12" style="padding-left: 20px; margin-top: 20px;">
                    <div style="font-size: 16px;color: #303133;">主题颜色</div>
                    <div style="font-size: 12px;color: #909399;">（页面中图标、按钮、元素色值，暂不支持渐变色）</div>
                  </el-col>
                  <el-col :span="12" style="margin-top: 20px; margin-left: -100px;">
                    <el-form-item label="" prop="themeColor">
                      <el-tooltip effect="dark" content="colorUI中的类名" placement="top">
                        <el-input v-model="form.themeColor" size="small" @focus="appTheme.bgType='theme',appTheme.showBgDialog=true">
                          <template slot="append">
                            <div style="background:#fff;width: 24px;padding-left: 5px;">
                              <div :class="'bg-' + form.themeColor" style="height: 20px;width: 20px;float: left;"></div>
                            </div>
                          </template>
                        </el-input>
                      </el-tooltip>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" style="padding-left: 20px; margin-top: 20px;">
                    <div style="font-size: 16px;color: #303133;">tabBar文字颜色</div>
                  </el-col>
                  <el-col :span="12" style="margin-top: 20px; margin-left: -100px;">
                    <el-form-item label="" prop="tabbarColor">
                      <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
                        <el-input v-model="form.tabbarColor" size="small">
                          <template slot="append">
                            <el-color-picker size="mini" v-model="form.tabbarColor"></el-color-picker>
                          </template>
                        </el-input>
                      </el-tooltip>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" style="padding-left: 20px; margin-top: 20px;">
                    <div style="font-size: 16px;color: #303133;">tabBar文字选中颜色</div>
                  </el-col>
                  <el-col :span="12" style="margin-top: 20px; margin-left: -100px;">
                    <el-form-item label="" prop="tabbarSelectedColor">
                      <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
                        <el-input v-model="form.tabbarSelectedColor" size="small">
                          <template slot="append">
                            <el-color-picker size="mini" v-model="form.tabbarSelectedColor"></el-color-picker>
                          </template>
                        </el-input>
                      </el-tooltip>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" style="padding-left: 20px; margin-top: 20px;">
                    <div style="font-size: 16px;color: #303133;">tabBar背景色</div>
                  </el-col>
                  <el-col :span="12" style="margin-top: 20px; margin-left: -100px;">
                    <el-form-item label="" prop="tabbarBackgroundColor">
                      <el-tooltip effect="dark" content="色值代码，如#ffffff" placement="top">
                        <el-input v-model="form.tabbarBackgroundColor" size="small">
                          <template slot="append">
                            <el-color-picker size="mini" v-model="form.tabbarBackgroundColor"></el-color-picker>
                          </template>
                        </el-input>
                      </el-tooltip>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" style="padding-left: 20px; margin-top: 20px;">
                    <div style="font-size: 16px;color: #303133;">tabBar上边框颜色</div>
                  </el-col>
                  <el-col :span="12" style="margin-top: 20px; margin-left: -100px;">
                    <el-form-item label="" prop="tabbarBorderStyle">
                      <el-tooltip effect="dark" content="请选择tabBar上边框颜色" placement="top">
                        <el-select size="small" style="width: 100%;" v-model="form.tabbarBorderStyle" placeholder="请选择tabBar上边框颜色">
                          <el-option label="黑色" value="black"></el-option>
                          <el-option label="白色" value="white"></el-option>
                        </el-select>
                      </el-tooltip>
                    </el-form-item>
                  </el-col>
                </el-row>

              </el-form>
            </div>
            <!-- 底部tabBar -->
            <div v-show="activeName=='name5'" >
              <div style="font-size: 13px;color: #8c939d;height: 20px;">提示：目前已经提供4套图标可以选择配置。</div>
            <!--  注意：图标路径请按已有方式进行统一存放  -->
            <!--  web图标路径示例：'public/img/thememobile/icon-1/1-001.png' 'public/img/thememobile/icon-1/1-002.png' -->
            <!--  app图标路径示例：'/static/public/img/icon-1/1-001.png' '/static/public/img/icon-1/1-002.png' -->
              <el-form :model="form" label-width="60px" v-if="form.tabbarItem&&form.tabbarItem.info"  >
                <el-form-item :label="'图标' + (index + 1)" v-for="(item,index) in 4" :key="index" class="tabbar-item"
                              :style="index==curTabBarIndex?'background:#efefef':'background:#fff'" style="margin-top: 40px;">
                  <div  @click="tabbarChange(index)">
                    <el-row >
                      <el-col :span="4" v-for="(item2, index2) in 5" :key="index2" style="text-align: center;margin-left: 20px;">
                        <div>
                          <img :src="'/img/thememobile/icon-'+(index+1)+'/' + (index2+1) + '-001.png'" style="width: 40px;height: 40px;" />
                          <img :src="'/img/thememobile/icon-'+(index+1)+'/' + (index2+1) + '-002.png'" style="width: 40px;height: 40px;margin-left: 10px;"/>
                        </div>
                        <div ><el-input size="mini" v-model="form.tabbarItem.info[index2].text" style="width: 86px;text-align: center;"></el-input></div>
                      </el-col>
                    </el-row>
                  </div>
                </el-form-item>
              </el-form>
            </div>
            <!-- 底部tabBar（可以使用网络图片的tabbar暂时保留，因为UniApp的原生App暂不支持网络图片所以先隐藏保留） -->
<!--            <div v-show="activeName=='name5'">-->
<!--              <el-form :model="form" label-width="100px" v-if="form.tabbarItem&&form.tabbarItem.info">-->
<!--                <el-row style="margin-top: 30px;">-->
<!--                  <el-col :span="12" v-for="(item,index) in tabBarData" :key="index">-->
<!--                    <div>-->
<!--                      <el-form-item :label="'TabBar'+(index+1)">-->
<!--                        <el-input size="mini" v-model="form.tabbarItem.info[index].text"></el-input>-->
<!--                      </el-form-item>-->
<!--                      <el-row :gutter="20" v-if="form.tabbarItem&&form.tabbarItem.info">-->
<!--                        <el-col :span="12">-->
<!--                          <el-form-item :label="''" label-width="100px">-->
<!--                            <MaterialList :value="[form.tabbarItem.info[index].selectedIconPath]" @sureSuccess="form.tabbarItem.info[index].selectedIconPath=$event?$event[0]:''"-->
<!--                                          @deleteMaterial="form.tabbarItem.info[index].selectedIconPath=tabBarData[index].selectedIconPath"-->
<!--                                          type="image" shopId="-1" :num=1 :width=60 :height=60></MaterialList>-->
<!--                            <div style="margin-top: -20px;">选中时图片</div>-->
<!--                          </el-form-item>-->
<!--                        </el-col>-->
<!--                        <el-col :span="12">-->
<!--                          <el-form-item :label="''" label-width="50px">-->
<!--                            <MaterialList :value="[form.tabbarItem.info[index].iconPath]" @sureSuccess="form.tabbarItem.info[index].iconPath=$event?$event[0]:''"-->
<!--                                          @deleteMaterial="form.tabbarItem.info[index].iconPath=tabBarData[index].iconPath" type="image"-->
<!--                                          shopId="-1" :num=1 :width=60 :height=60></MaterialList>-->
<!--                            <div style="margin-top: -20px;">未选中时图片</div>-->
<!--                          </el-form-item>-->
<!--                        </el-col>-->
<!--                      </el-row>-->
<!--                    </div>-->
<!--                  </el-col>-->
<!--                </el-row>-->
<!--                <div style="font-size: 12px; color: #666666; margin: 0 0 0 40px; color: #ff0000;">*图片/icon建议尺寸（66x66）px</div>-->
<!--              </el-form>-->
<!--            </div>-->

            <div style="margin-top: 20px; text-align: center">
              <el-button type="primary" @click="submitForm('form')" style="width: 168px;">提交</el-button>
              <el-button @click="resetForm('form')" style="width: 168px; margin-left: 24px;">清空</el-button>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-dialog title="背景颜色" :visible.sync="appTheme.showBgDialog" width="40%">
        <el-row :gutter="20">
          <el-col :span="6" v-for="(item,index) in colorList" :key="index" class="tm-select-bg" v-show="!(appTheme.bgType=='theme'&&item.name.indexOf('-')!=-1)">
            <div @click="onBgColor(item)" >
              <div :class="'bg-' + item.name" style="width: 30px;height: 30px;margin: 0 auto;"></div>
              <div style="margin-top: 5px;">{{item.title}}&nbsp;{{item.name}}</div>
            </div>
          </el-col>
        </el-row>
      </el-dialog>
      <!-- 首页轮播组件及通知组件，不显示，仅用作调用方法 -->
    </basic-container>
    <div class="preview">
      <div class="screen">
        <div class="notch"></div>
        <div class="backgroundColor" :class="'bg-'+form.backgroundColor">
          <div class="cu-list menu-avatar top-title" :class="'bg-'+form.backgroundColor">
            <div>个人中心</div>
            <div class="message">
              <div class="setting flex justify-end margin-right">
                <span class="text-lg margin-top">
                  <text class="cuIcon-settingsfill text-white"></text>
                </span>
              </div>
              <div class="cu-item personal-information">
                <div class="cu-avatar round head flex bg-white head-portrait"></div>
                <div class="text-center margin-top-sm text-xl text-white text-bold">JooLun</div>
              </div>
            </div>
            <div class="account padding-bottom-xs margin-top-sm">
              <div class="padding flex text-center text-white" >
                <div class="flex flex-sub flex-direction">
                  <div class="text-xl text-bold">192</div>
                  <div class="text-sm">
                    <span class="cuIcon-barcode"></span>
                    <span class="margin-left-xs">会员编号</span>
                  </div>
                </div>
                <span class="flex flex-sub flex-direction" >
                  <div class="text-xl text-bold">1635</div>
                  <div class="text-sm text-white">
                    <span class="cuIcon-medal"></span>
                    <span class="margin-left-xs">当前积分</span>
                  </div>
                </span>
                <span class="flex flex-sub flex-direction">
                  <div class="text-xl text-bold">26</div>
                  <div class="text-sm text-white">
                    <span class="cuIcon-ticket"></span>
                    <span class="margin-left-xs">优惠券</span>
                  </div>
                </span>
              </div>
            </div>
          </div>

          <div class="cu-list menu card-menu margin-top-xs all-orders">
            <div class="cu-bar bg-white solid-bottom">
              <div class="action">
                <span class="cuIcon-titles" :class="'text-'+form.themeColor"></span>
                <div class="text-df">我的订单</div>
              </div>
              <span class="action text-sm">全部订单<span class="cuIcon-right"></span></span>
            </div>
            <div class="cu-list grid col-4 no-border padding-xs padding-bottom-xs">
              <div class="cu-item">
                <div>
                  <span class="cuIcon-pay" :class="'text-'+form.themeColor"></span>
                  <div class="order-text">待付款</div>
                </div>
              </div>
              <div class="cu-item">
                <div>
                  <span class="cuIcon-send" :class="'text-'+form.themeColor"></span>
                  <div class="order-text">待发货</div>
                </div>
              </div>
              <div class="cu-item">
                <div>
                  <span class="cuIcon-deliver" :class="'text-'+form.themeColor"></span>
                  <div class="order-text">待收货</div>
                </div>
              </div>
              <div class="cu-item">
                <div>
                  <span :class="'text-'+form.themeColor" class="cuIcon-evaluate" ></span>
                  <div class="order-text">待评价</div>
                </div>
              </div>
            </div>
          </div>
          <div class="margin-top-sm bg-white radius margin-lr-sm">
            <div class="flex solid-bottom padding-sm" >
              <div class="flex-twice text-df">
                <span class="cuIcon-write text-red" ></span>
                <span class="margin-left-xs order-text">我的签到</span>
              </div>
              <div class="flex-sub text-right text-gray">
                <span class="cuIcon-right text-sm"></span>
              </div>
            </div>
            <div class="flex solid-bottom padding-sm">
              <div class="flex-twice text-df">
                <span class="cuIcon-footprint text-green"></span>
                <span class="margin-left-xs order-text">我的足迹</span>
              </div>
              <div class="flex-sub text-right text-gray">
                <span class="cuIcon-right text-sm"></span>
              </div>
            </div>
            <div class="flex solid-bottom padding-sm">
              <div class="flex-twice text-df">
                <span class="cuIcon-ticket text-orange"></span>
                <span class="margin-left-xs order-text">我的卡券</span>
              </div>
              <div class="flex-sub text-right text-gray">
                <span class="cuIcon-right text-sm"></span>
              </div>
            </div>
            <div class="flex solid-bottom padding-sm">
              <div class="flex-twice text-df">
                <span class="cuIcon-group text-blue"></span>
                <span class="margin-left-xs order-text">我的拼团</span>
              </div>
              <div class="flex-sub text-right text-gray">
                <span class="cuIcon-right text-sm"></span>
              </div>
            </div>
            <div class="flex solid-bottom padding-sm">
              <div class="flex-twice text-df">
                <span class="cuIcon-cardboardforbid text-orange"></span>
                <span class="margin-left-xs order-text">我的砍价</span>
              </div>
              <div class="flex-sub text-right text-gray">
                <span class="cuIcon-right text-sm"></span>
              </div>
            </div>
          </div>
        </div>
        <div class="tabbar">
          <div class="tabbar-top-solid" :style="'background: '+(form.tabbarBorderStyle?form.tabbarBorderStyle:'white')"></div>
          <div class="cu-bar tabbar bg-white shadow foot" :style="{backgroundColor: form.tabbarBackgroundColor}">
            <div class="action btm-bar-item">
              <div class='cuIcon-cu-image'>
                <img :src="'/img/thememobile/icon-'+(curTabBarIndex+1)+'/1-001.png'"/>
              </div>
              <div class="text-unselected" :style="{color: form.tabbarColor}">{{form.tabbarItem.info[0].text}}</div>
            </div>
            <div class="action btm-bar-item" >
              <div class='cuIcon-cu-image'>
                <img :src="'/img/thememobile/icon-'+(curTabBarIndex+1)+'/2-001.png'"/>
              </div>
              <div class="text-unselected" :style="{color: form.tabbarColor}">{{form.tabbarItem.info[1].text}}</div>
            </div>
            <div class="action btm-bar-item" >
              <div class='cuIcon-cu-image'>
                <img :src="'/img/thememobile/icon-'+(curTabBarIndex+1)+'/3-001.png'"/>
              </div>
              <div class="text-unselected" :style="{color: form.tabbarColor}">{{form.tabbarItem.info[2].text}}</div>
            </div>
            <div class="action btm-bar-item" >
              <div class='cuIcon-cu-image'>
                <img :src="'/img/thememobile/icon-'+(curTabBarIndex+1)+'/4-001.png'"/>
              </div>
              <div class="text-unselected" :style="{color: form.tabbarColor}">{{form.tabbarItem.info[3].text}}</div>
            </div>
            <div class="action btm-bar-item" >
              <div class='cuIcon-cu-image'>
                <img :src="'/img/thememobile/icon-'+(curTabBarIndex+1)+'/5-002.png'"/>
              </div>
              <div class="text-selected" :style="{color: form.tabbarSelectedColor}">{{form.tabbarItem.info[4].text}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import {
    getPage,
    getObj,
    addObj,
    putObj,
    delObj,
    getObj2
  } from '@/api/mall/thememobile'
  import {
    tableOption
  } from '@/const/crud/mall/thememobile'
  import {
    mapGetters
  } from 'vuex'

  import MaterialList from '@/components/material/list.vue'
  import AppPageSelect from '@/components/app-page-select/Index.vue'

  import pagePreview from "@/views/mall/pagedevise/component-library/pages/page-components/pagePreview.vue";

  export default {
    name: 'thememobileform',
    components: {
      MaterialList,
      AppPageSelect,
    },
    data() {
      return {
        noticeTextCurIndex: -1, //通知编 辑时索引
        noticeItemCurIndex: -1, //轮播图 编辑时索引
        navButtonCurIndex: -1, //首页中间导航按钮 编辑时索引
        noticeTextList: [], //通知DATA数据
        noticeItemList: [], //轮播图数据
        appTheme: { //主题设置显示
          bgType: 'theme', //类型 theme background
          showForm: false, //显示
          showBgDialog: false,
        },

        activeName: "name1", //页面tabs设置显示
        tabBarItemActive: 0, //激活的tab
        tabBar: [{}, {}, {}, {}, ],
        rules: {
          themeColor: [{
            required: true,
            message: '请选择主题颜色',
            trigger: 'blur'
          },
            {
              max: 50,
              message: '长度在不能超过10个字符'
            },
          ],
          backgroundColor: [{
            required: true,
            message: '请选择背景颜色',
            trigger: 'blur'
          },
            {
              max: 50,
              message: '长度在不能超过10个字符'
            },
          ],
          tabbarColor: [{
            required: true,
            message: '请选择tabBar文字颜色',
            trigger: 'blur'
          },
            {
              max: 50,
              message: '长度在不能超过10个字符'
            },
          ],
          tabbarSelectedColor: [{
            required: true,
            message: '请选择tabBar文字选中颜色',
            trigger: 'blur'
          },
            {
              max: 50,
              message: '长度在不能超过10个字符'
            },
          ],
          tabbarBackgroundColor: [{
            required: true,
            message: '请选择tabBar背景色',
            trigger: 'blur'
          },
            {
              max: 50,
              message: '长度在不能超过10个字符'
            },
          ],
          tabbarBorderStyle: [{
            required: true,
            message: '请选择tabBar上边框颜色',
            trigger: 'blur'
          }],
        },
        navButtonModal:false,
        navButtonForm: {
          name:'',
          url:'',
          img:null,
        },
        form: {
          "themeColor": "scarlet",
          "backgroundColor": "gradual-scarlet",
          "tabbarColor": "#666666",
          "tabbarSelectedColor": "#e53c43",
          "tabbarBackgroundColor": "#ffffff",
          "tabbarBorderStyle": "white",
          tabbarItem: {
            info: [
              {
                text: '首页',
                index:0,
                iconPath: '/img/thememobile/icon-1/1-001.png',
                selectedIconPath: '/img/thememobile/icon-1/1-002.png'
              },
              {
                text: '类别',
                index:1,
                iconPath: '/img/thememobile/icon-1/2-001.png',
                selectedIconPath: '/img/thememobile/icon-1/2-002.png'
              },
              {
                text: '消息',
                index:2,
                iconPath: '/img/thememobile/icon-1/3-001.png',
                selectedIconPath: '/img/thememobile/icon-1/3-002.png'
              },
              {
                text: '购物车',
                index:3,
                iconPath: '/img/thememobile/icon-1/4-001.png',
                selectedIconPath: '/img/thememobile/icon-1/4-002.png'
              },
              {
                text: '我的',
                index:4,
                iconPath: '/img/thememobile/icon-1/5-001.png',
                selectedIconPath: '/img/thememobile/icon-1/5-002.png'
              },
            ]
          },
          navButton: {
            info: []
          }
        },

        colorList: [
          {
            title: '默认',
            name: 'gradual-scarlet',
            color: '#ffffff'
          },{
            title: '嫣红',
            name: 'red',
            color: '#e54d42'
          },
          {
            title: '桔橙',
            name: 'orange',
            color: '#f37b1d'
          },
          {
            title: '明黄',
            name: 'yellow',
            color: '#fbbd08'
          },
          {
            title: '橄榄',
            name: 'olive',
            color: '#8dc63f'
          },
          {
            title: '森绿',
            name: 'green',
            color: '#39b54a'
          },
          {
            title: '天青',
            name: 'cyan',
            color: '#1cbbb4'
          },
          {
            title: '海蓝',
            name: 'blue',
            color: '#0081ff'
          },
          {
            title: '深蓝',
            name: 'darkblue',
            color: '#0055ff'
          },
          {
            title: '姹紫',
            name: 'purple',
            color: '#6739b6'
          },
          {
            title: '木槿',
            name: 'mauve',
            color: '#9c26b0'
          },
          {
            title: '桃粉',
            name: 'pink',
            color: '#e03997'
          },
          {
            title: '棕褐',
            name: 'brown',
            color: '#a5673f'
          },
          {
            title: '玄灰',
            name: 'grey',
            color: '#8799a3'
          },
          {
            title: '草灰',
            name: 'gray',
            color: '#aaaaaa'
          },
          {
            title: '墨黑',
            name: 'black',
            color: '#333333'
          },
          {
            title: '雅白',
            name: 'white',
            color: '#ffffff'
          },
          {
            title: '粉红',
            name: 'gradual-red',
            color: '#ffffff'
          },
          {
            title: '橙红',
            name: 'gradual-orange',
            color: '#ffffff'
          },
          {
            title: '绿青',
            name: 'gradual-green',
            color: '#ffffff'
          },
          {
            title: '紫红',
            name: 'gradual-purple',
            color: '#ffffff'
          },
          {
            title: '粉紫',
            name: 'gradual-pink',
            color: '#ffffff'
          },
          {
            title: '蓝绿',
            name: 'gradual-blue',
            color: '#ffffff'
          },
          {
            title: '黑灰',
            name: 'gradual-gray',
            color: '#ffffff'
          },
          {
            title: '淡蓝',
            name: 'gradual-darkblue',
            color: '#ffffff'
          },

          {
            title: '默认',
            name: 'scarlet',
            color: '#ffffff'
          },
        ],
        tableOption: tableOption,

        previewHei: 667, //预览窗口高度
        previewWid: 375, //预览窗口宽度
        pageTopHei: 0, //页面顶部title高度
        navButtonData: [],
        curTabBarIndex: 0,
        tabBarData: [
          {
            text: '首页',
            iconPath: '/img/thememobile/icon-1/1-001.png',//web的地址
            selectedIconPath: '/img/thememobile/icon-1/1-002.png',
          },
            {
              text: '类别',
              iconPath: '/img/thememobile/icon-1/2-001.png',
              iconPathApp: '/static/public/img/2-001.png',//app中的地址
              selectedIconPath: '/img/thememobile/icon-1/2-002.png',
              selectedIconPathApp: '/static/public/img/2-002.png',//app中的地址
            },
            {
              text: '消息',
              iconPath: '/img/thememobile/icon-1/2-001.png',
              selectedIconPath: '/img/thememobile/icon-1/2-002.png'
            },
            {
              text: '购物车',
              iconPath: '/img/thememobile/icon-1/3-001.png',
              selectedIconPath: '/img/thememobile/icon-1/3-002.png'
            },
            {
              text: '我的',
              iconPath: '/img/thememobile/icon-1/4-001.png',
              selectedIconPath: '/img/thememobile/icon-1/4-002.png'
            }
        ]
      }
    },
    created() {
      this.handleGet()
    },
    mounted: function() {},
    watch: {

    },
    computed: {
      ...mapGetters(['permissions']),

    },
    methods: {
      onEditView(type, index) {
        if (type == 'appTheme') { //主题
          this.activeName = 'name1';
        } else if (type == 'noticeItem') { //轮播
          this.activeName = 'name2';
          this.noticeItemCurIndex = index;
        } else if (type == 'noticeText') { //公告
          this.activeName = 'name3';
          this.noticeTextCurIndex = index;
        } else if (type == 'navButton') { //中间导航
          this.activeName = 'name4';
          this.navButtonCurIndex = index;
        } else if (type == 'tabBar') { //tabBar
          this.activeName = 'name5';
        }
      },
      onSortBottom(dataTemp, index, type) { //往下移动一格 0 + 1
        if (index != dataTemp.length - 1) {
          dataTemp[index] = dataTemp.splice(index + 1, 1, dataTemp[index])[0];
        } else {
          dataTemp.unshift(dataTemp.splice(index, 1)[0]);
        }
        dataTemp[index].sort = index;
        dataTemp[index + 1].sort = index + 1;
        if (type == 'noticeItem') { //轮播
          this.$refs.noticeItemRef.changeSort(dataTemp[index]);
          this.$refs.noticeItemRef.changeSort(dataTemp[index + 1]);
        } else if (type == 'noticeText') { //公告
          this.$refs.noticeTextRef.changeSort(dataTemp[index]);
          this.$refs.noticeTextRef.changeSort(dataTemp[index + 1]);
        }
      },
      onSortTop(dataTemp, index, type) { //往上移动一格
        if (index != 0) {
          dataTemp[index] = dataTemp.splice(index - 1, 1, dataTemp[index])[0];
        } else {
          dataTemp.push(dataTemp.shift());
        }
        dataTemp[index].sort = index;
        dataTemp[index - 1].sort = index - 1;
        if (type == 'noticeItem') { //轮播
          this.$refs.noticeItemRef.changeSort(dataTemp[index]);
          this.$refs.noticeItemRef.changeSort(dataTemp[index - 1]);
        } else if (type == 'noticeText') { //公告
          this.$refs.noticeTextRef.changeSort(dataTemp[index]);
          this.$refs.noticeTextRef.changeSort(dataTemp[index - 1]);
        }
      },
      onAddNoticeItemButton(startIndex) { //新增 轮播图
        this.$refs.noticeItemRef.openAddForm();
      },
      onEditNoticeItemButton(row, index) { //编辑 轮播图
        this.$refs.noticeItemRef.openEditForm(row, index);
      },
      onChangeNoticeItemButton(item) { //启用 轮播图
        this.$refs.noticeItemRef.changeEnable(item);
      },
      onDelNoticeItemButton(item, startIndex) { //删除 轮播图
        this.$refs.noticeItemRef.handleDel(item);
      },

      onAddNoticeTextButton(startIndex) { //新增 通知
        this.$refs.noticeTextRef.openAddForm();
      },
      onEditNoticeTextButton(row, index) { //编辑 通知
        this.$refs.noticeTextRef.openEditForm(row, index);
      },
      onChangeNoticeTextButton(item) { //启用 通知
        this.$refs.noticeTextRef.changeEnable(item);
      },
      onDelNoticeTextButton(item, startIndex) { //删除 通知
        this.$refs.noticeTextRef.handleDel(item);
      },
      onAddNavButton(startIndex) { //新增 NavBtn
        this.$refs['navButtonForm'].validate((valid) => {
          if (valid) {
            this.form.navButton.info.push(this.navButtonForm);
            this.navButtonModal = false;
            this.navButtonForm = {
              name:'',
              url:'',
              img:null,
            };
          } else {
            return false;
          }
        });

      },
      onDeleteButton(dataTemp, index) { //删除
        dataTemp.splice(index, 1);
      },
      onBgColor(item) {
        if (this.appTheme.bgType === 'theme') {
          this.form.themeColor = item.name;
        } else {
          this.form.backgroundColor = item.name;
        }
        this.appTheme.showBgDialog = false;
      },
      tabbarChange(index){ // 选择tabBar
        this.curTabBarIndex = index
        this.form.tabbarItem.info.map((item, index2)=>{
          item.iconPath = '/static/public/img/icon-' + (index+1) + '/'+(index2+1)+ '-001.png'
          item.selectedIconPath = '/static/public/img/icon-' + (index+1) + '/' + (index2+1) + '-002.png'
        })
      },
      initResponseData(response) {
        response.data.data = response.data.data ? response.data.data : null;
        //初始化数据 tabBar数据 curTabBarIndex
        if (response.data.data.tabbarItem && response.data.data.tabbarItem.info) {
          let info = response.data.data.tabbarItem.info;
          let iconPathTemp = info[0].iconPath
          for (let i = 0; i < 4; i++) {// 确认第几套图标
            // /static/public/img/icon-2/1-001.png
            if (iconPathTemp.indexOf('icon-'+(i+1))!=-1){
              this.curTabBarIndex = i

            }
          }
        }
        //初始化数据 tabBar数据
        // if (response.data.data.tabbarItem && response.data.data.tabbarItem.info) {
        //   let info = response.data.data.tabbarItem.info;
        //   let infoIndex = [];
        //   let infoTemp = [];
        //   info.map(item => {
        //     infoIndex.push(item.index)
        //   });
        //   // 默认tabBar图片
        //   for (let i = 0; i < 5; i++) {
        //     let indexTemp = infoIndex.indexOf(i);
        //     if (indexTemp == -1) {
        //       infoTemp.push({
        //         "selectedIconPath": this.tabBarData[i].selectedIconPath,
        //         "iconPath": this.tabBarData[i].iconPath,
        //         "index": i,
        //         "text": this.tabBarData[i].text,
        //       })
        //     } else {
        //       infoTemp.push({
        //         "selectedIconPath": info[indexTemp].selectedIconPath ? info[indexTemp].selectedIconPath : this.tabBarData[
        //           i].selectedIconPath,
        //         "iconPath": info[indexTemp].iconPath ? info[indexTemp].iconPath : this.tabBarData[i].iconPath,
        //         "index": i,
        //         "text": info[indexTemp].text ? info[indexTemp].text : this.tabBarData[i].text,
        //       })
        //     }
        //   }
        //   response.data.data.tabbarItem.info = infoTemp;
        // }
        if(response.data.data){
          this.form = response.data.data;
        }
      },
      handleGet: function() {
        getObj2().then(response => {
          this.initResponseData(response)
        })
      },
      //提交
      submitForm(formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            this.handleUpdate(this.form);
          } else {
            return false;
          }
        });
      },
      resetForm(formName) {
        this.$refs[formName].resetFields();
      },
      /**
       * @title 数据更新
       * @param row 为当前的数据
       * @param index 为当前更新数据的行数
       * @param done 为表单关闭函数
       *
       **/
      handleUpdate(form) {
        // 过滤数据
        // this.form.tabbarItem.info.map(item => {
        //   if (item.iconPath.indexOf("http") == -1) {
        //     item.iconPath = ''
        //   }
        //   if (item.selectedIconPath.indexOf("http") == -1) {
        //     item.selectedIconPath = ''
        //   }
        // });
        putObj(this.form).then(response => {
          // done()
          this.initResponseData(response)
          this.$message({
            showClose: true,
            message: '修改成功',
            type: 'success'
          })
        }).catch(() => {
          // done()
        })
      },
    }
  }
</script>
<style lang='less' scoped>

@import '../pagedevise/component-library/colorui/main.css';
@import '../pagedevise/component-library/colorui/icon.css';
  .preview{
    width: 428px;
    height: 780px;
    background-image: url(/img/preview/preview-phone.png);
    background-repeat: no-repeat;
    position: absolute;
    top: 60px;
    left: 1100px;
    font-weight: 300;
  }

  .screen{
    width: 298px;
    height: 626px;
    background-color: #f0f0f0;
    margin-top: 10px;
    margin-left: 10px;
    border-radius: 40px;
  }
  .top-title{
    border-radius: 40px 40px 0px 0px;
    text-align: center;
    padding-top: 30px;
  }

  .head-portrait{
    width: 50px;
    height: 50px;
    background-image: url(/img/preview/joolun-logo.png);
    background-size: 36px 36px;
    background-repeat: no-repeat;
  }

  .order-text{
    font-size: 12px !important;
  }

  .notch{
    width: 268px;
    height: 21px;
    background-image: url(/img/preview/preview-notch.png);
    background-size: 268px 21px;
    background-repeat: no-repeat;
    position: absolute;
    top:10px;
    left: 26px;
    z-index: 1;
  }

  .backgroundColor{
    width: 298px;
    height: 160px;
    background-color: #d34e41;
    border-radius: 40px 40px 0px 0px;
  }

  .tabbar-top-solid{
    position: absolute;
    height: 1px;
    width: 100%;
    bottom: 120px;
  }

  .tabbar{
    width: 298px;
    height: 60px;
    border-radius: 0px 0px 40px 40px;
    position: absolute;
    bottom: 72px;
    padding-bottom: 20px;
  }

  .btm-bar-item{
    width: 50px!important;
    border-radius: 0px 0px 40px 40px;
    .cuIcon-cu-image{
      width: 50px!important;
    }
  }

  .icon-list {
    overflow: hidden;
    list-style: none;
    padding: 0 !important;
    border: 1px solid #eaeefb;
    border-radius: 4px;

    li {

      float: left;
      width: 16.66%;
      text-align: center;
      height: 120px;
      line-height: 120px;
      color: #666;
      font-size: 13px;
      border-right: 1px solid #eee;
      border-bottom: 1px solid #eee;
      margin-right: -1px;
      margin-bottom: -1px;

      span {
        line-height: normal;
        color: #99a9bf;
        transition: color .15s linear;

      }

    }
  }

  .el-col-5 {
    width: 20%;

  }

  .icon-name {
    display: inline-block;
    padding: 0 3px;
    height: 1em;
  }



  /*color ui*/
  .grid {
    display: flex;
    flex-wrap: wrap;
  }

  .grid.grid-square {
    overflow: hidden;
  }

  .grid.grid-square .cu-tag {
    position: absolute;
    right: 0;
    top: 0;
    border-bottom-left-radius: 6px;
    padding: 6px 12px;
    height: auto;
    background-color: rgba(0, 0, 0, 0.5);
  }

  .grid.grid-square>view>text[class*="cuIcon-"] {
    font-size: 52px;
    position: absolute;
    color: #8799a3;
    margin: auto;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }

  .grid.grid-square>view {
    margin-right: 20px;
    margin-bottom: 20px;
    border-radius: 6px;
    position: relative;
    overflow: hidden;
  }

  .grid.grid-square>view.bg-img image {
    width: 100%;
    height: 100%;
    position: absolute;
  }

  .grid.col-1.grid-square>view {
    padding-bottom: 100%;
    height: 0;
    margin-right: 0;
  }

  .grid.col-2.grid-square>view {
    padding-bottom: calc((100% - 20px)/2);
    height: 0;
    width: calc((100% - 20px)/2);
  }

  .grid.col-3.grid-square>view {
    padding-bottom: calc((100% - 40px)/3);
    height: 0;
    width: calc((100% - 40px)/3);
  }


  .bg-red {
    background-color: #e54d42;
    color: #ffffff;
  }

  .bg-orange {
    background-color: #f37b1d;
    color: #ffffff;
  }

  .bg-yellow {
    background-color: #fbbd08;
    color: #333333;
  }

  .bg-olive {
    background-color: #8dc63f;
    color: #ffffff;
  }

  .bg-green {
    background-color: #39b54a;
    color: #ffffff;
  }

  .bg-cyan {
    background-color: #1cbbb4;
    color: #ffffff;
  }

  .bg-darkblue {
    background-color: #0055ff;
    color: #ffffff;
  }

  .bg-blue {
    background-color: #0081ff;
    color: #ffffff;
  }

  .bg-purple {
    background-color: #6739b6;
    color: #ffffff;
  }

  .bg-mauve {
    background-color: #9c26b0;
    color: #ffffff;
  }

  .bg-pink {
    background-color: #e03997;
    color: #ffffff;
  }

  .bg-brown {
    background-color: #a5673f;
    color: #ffffff;
  }

  .bg-grey {
    background-color: #8799a3;
    color: #ffffff;
  }

  .bg-gray {
    background-color: #f0f0f0;
    color: #333333;
  }

  .bg-black {
    background-color: #333333;
    color: #ffffff;
  }

  .bg-white {
    background-color: #ffffff;
    color: #666666;
  }

  .bg-shadeTop {
    background-image: linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 0.01));
    color: #ffffff;
  }

  .bg-shadeBottom {
    background-image: linear-gradient(rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 1));
    color: #ffffff;
  }

  .bg-red.light {
    color: #e54d42;
    background-color: #fadbd9;
  }

  .bg-orange.light {
    color: #f37b1d;
    background-color: #fde6d2;
  }

  .bg-yellow.light {
    color: #fbbd08;
    background-color: #fef2ced2;
  }

  .bg-olive.light {
    color: #8dc63f;
    background-color: #e8f4d9;
  }

  .bg-green.light {
    color: #39b54a;
    background-color: #d7f0dbff;
  }

  .bg-cyan.light {
    color: #1cbbb4;
    background-color: #d2f1f0;
  }

  .bg-blue.light {
    color: #0081ff;
    background-color: #cce6ff;
  }

  .bg-purple.light {
    color: #6739b6;
    background-color: #e1d7f0;
  }

  .bg-mauve.light {
    color: #9c26b0;
    background-color: #ebd4ef;
  }

  .bg-pink.light {
    color: #e03997;
    background-color: #f9d7ea;
  }

  .bg-brown.light {
    color: #a5673f;
    background-color: #ede1d9;
  }

  .bg-grey.light {
    color: #8799a3;
    background-color: #e7ebed;
  }
  .bg-scarlet {
    background-color: #e53c43;
    color: #ffffff;
  }
  .bg-gradual-scarlet {
    background-image: linear-gradient(45deg, #e5432e, #e53c43);
    color: #ffffff;
  }
  .bg-gradual-red {
    background-image: linear-gradient(45deg, #f43f3b, #ec008c);
    color: #ffffff;
  }

  .bg-gradual-orange {
    background-image: linear-gradient(45deg, #ff9700, #ed1c24);
    color: #ffffff;
  }

  .bg-gradual-green {
    background-image: linear-gradient(45deg, #39b54a, #8dc63f);
    color: #ffffff;
  }

  .bg-gradual-purple {
    background-image: linear-gradient(45deg, #9000ff, #5e00ff);
    color: #ffffff;
  }

  .bg-gradual-pink {
    background-image: linear-gradient(45deg, #ec008c, #6739b6);
    color: #ffffff;
  }

  .bg-gradual-blue {
    background-image: linear-gradient(45deg, #0081ff, #1cbbb4);
    color: #ffffff;
  }


  .bg-gradual-gray {
    background-image: linear-gradient(45deg, #99a6c3, #444c5e);
    color: #ffffff;
  }

  .bg-gradual-darkblue {
    background-image: linear-gradient(45deg, #339eec, #1b7bde);
    color: #ffffff;
  }

  .tm-select-bg {
    text-align: center;
    cursor: pointer;
    padding: 10px 0;
  }

  .tm-select-bg:hover {
    background: #efefef;
  }

  .tabbar-item{
    padding-top: 10px;
    cursor: pointer;
    background: #efefef;
  }
  .tabbar-item:hover {
    background: rgba(189, 238, 255, 0.91);
  }


</style>

<style>
.personal-information {
  margin-top: -30px;
}

.cu-avatar.xs {
  width: 30px;
  height: 30px;
  font-size: 1em;
}
.head {
  margin: auto;
  border: #FFFFFF 1px solid;
  margin-top: 35px;
}

.all-orders {
  width: 94% !important;
  margin: auto !important;
  margin-top: 10px !important;
  border-radius: 5px !important;
}

.mine {
  width: 94% !important;
  margin: auto !important;
  margin-top: 10px !important;
  border-radius: 5px !important;
}

</style>
