<!--商品分类标签-->
<template>
  <div class="cuttingLineSetting">
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">地图控件</p>
      <div slot="mainContent">
        <el-form ref="form" label-width="100px" :model="formData">
          <el-divider>基础属性</el-divider>
          <el-form-item label="组件上边距">
            <el-input v-model="formData.paddingTop" size="mini" type="number" style="margin-top: 5px"
                      placeholder="页面下边距">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="组件下边距">
            <el-input v-model="formData.paddingBottom" size="mini" type="number" style="margin-top: 5px"
                      placeholder="页面下边距">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="背景颜色">
            <el-input v-model="formData.background" size="small" style="margin-top: 5px">
              <template slot="append">
                <el-color-picker size="mini" v-model="formData.background"></el-color-picker>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="显示宽度" v-show="formData.type==0">
            <el-input v-model="formData.width" size="mini" type="number" placeholder="宽度" :min="0" :max="100"
                      style="margin-top: 5px">
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
          <el-form-item label="显示高度" v-show="formData.type==0">
            <el-input v-model="formData.height" size="mini" type="number" placeholder="宽度" :min="1"
                      style="margin-top: 5px">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-divider>地图标记</el-divider>
          <el-form-item label="选择地区">
            <el-button @click="openRegionBox" size="small" type="primary">
              {{ formData.selectedRegion ? formData.selectedRegion.name : '地区选择' }}
            </el-button>
          </el-form-item>
          <el-form-item label="具体地址" v-show="formData.type==0" >
            <el-autocomplete
              size="small"
              style="width: 100%"
              v-model="formData.marker"
              :fetch-suggestions="querySearchAsync"
              placeholder="请输入内容"
              @select="selectMarket"
            ></el-autocomplete>
          </el-form-item>
        </el-form>
      </div>
    </settingSlot>

    <!--    页面选择框-->
    <el-dialog
      title="页面选择"
      :visible.sync="regionVisible"
      :close-on-click-modal="false"
      append-to-body>
      <div class="block">
        <el-cascader-panel
          v-model="selectRegion"
          :options="options"
          :props="{ expandTrigger: 'hover' ,value: 'code',label: 'name'}"
          @change="regionChange"></el-cascader-panel>
      </div>
    </el-dialog>
  </div>

</template>
<script>

import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';
import {getLocation} from '@/api/third-party/Tmap/TMap';
import region from '/public/json/region.json';
import settingSlot from '../settingSlot';
import bgColorSelect from "../../pages/page-components/bgColorSelect";

export default {
  components: {settingSlot, bgColorSelect},
  data() {
    return {
      regionVisible: false,
      selectRegion: [],
      queryAddressList: [],//地图查询地址
      options: region, //地区选择数组
      formDataCopy: {
        paddingTop: 0,
        paddingBottom: 0,
        background: "#ffffff",
        type: 0,
        width: 100,
        height: 400,
        selectedRegion: "", //地区选择数组
        location: {value:"",obj:{}}, //定位
      },
      formData: {}
    };
  },
  props: {
    thememobile: {type: Object | Array},
    showData: {
      type: Object,
      default: () => {
      }
    },
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
      clickComIndex: state => state.divpage.clickComIndex,
    })
  },
  mounted() {
    let that = this;
    if (that.IsEmptyObj(that.showData)) {
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
    } else {
      that.formData = that.showData
    }
    that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
    // that.updateData({
    //   componentsList: that.componentsList
    // })
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    // 删除按钮
    delBtn(index) {
      let that = this;
      that.$confirm('是否删除该按钮?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
        that.updateData({componentsList: that.componentsList});
      }).catch(() => {
      })
    },
    cancel() {
      this.$emit('cancel')
    },
    reset() {
      let that = this;
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      that.componentsList[that.clickComIndex]['data'] = this.formData;
    },
    confirm() {
      this.$emit('confirm', this.formData)
    },
    delete() {
      this.$emit('delete')
    },
    querySearchAsync(queryString, cb) {
      if (!this.formData.selectedRegion ) {
        this.$message.warning("请先选择一个地区");
        return;
      }
      //拿取地址
      let obj = {
        keyword: queryString,
        region: this.formData.selectedRegion.name,
      }
      //请求地址
      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        getLocation(obj).then(res => {
          // console.log("地图反馈", res)
          cb(this.formatAddress(res.data.data.data))
        }).catch()
      }, 2000 * Math.random());

    },
    //格式化请求地址
    formatAddress(addressList) {
      // console.log("格式化",addressList)
      let list = []
      if(!addressList){
        return list;
      }

      for (let i = 0; i <addressList.length ; i++) {
        list.push({
          value:addressList[i].address,
          obj:addressList[i],
        })
      }
        return list;
    },
    selectMarket(value) {
      // console.log("选中了地图",value)
      this.formData.location = value;
    },
    openRegionBox() {
      if(!this.selectRegion){
        this.selectRegion = this.formData.selectRegion.code
      }
      this.regionVisible = true;
    },
    regionChange(val) {
      let provinceList = this.options;
      for (let i = 0; i < provinceList.length; i++) {
        if (val[0] == provinceList[i].code) {
          let cityList = provinceList[i].children;
          for (let j = 0; j < cityList.length; j++) {
            if (val[1] == cityList[j].code) {
              let areaList = cityList[j].children;
              for (let k = 0; k < areaList.length; k++) {
                if (val[2] == areaList[k].code) {
                  this.formData.selectedRegion = {
                    name: provinceList[i].name + cityList[j].name + areaList[k].name,
                    code:val,
                  }
                  break;
                }
              }
              break;
            }
          }
          break;
        }
      }
      this.regionVisible = false;
    }
  },
  watch: {
    showData: {
      handler(newVal, oldVal) {
        this.formData = newVal ? newVal : this.formData;
      },
      deep: true
    },
    thememobile() {
    },
  }
};
</script>
<style lang='less' scoped>

.el-form-item {
  margin-bottom: 0;
}

</style>
