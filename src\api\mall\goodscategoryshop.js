import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/mall/goodscategoryshop/page',
        method: 'get',
        params: query
    })
}

export function fetchTree(query) {
  return request({
    url: '/mall/goodscategoryshop/tree',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
    return request({
        url: '/mall/goodscategoryshop',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/mall/goodscategoryshop/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/mall/goodscategoryshop/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/mall/goodscategoryshop',
        method: 'put',
        data: obj
    })
}
