<template>
  <div class="thumbsUpButtonSetting">
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">投票按钮</p>
      <div slot="mainContent">
        <el-form ref="form" label-width="100px" :model="formData">
          <el-divider>基础属性</el-divider>
          <el-form-item label="上边界">
            <el-input v-model="formData.pageMarginTop" size="mini" :mini="0" type="number" style="margin-top: 5px"
                      placeholder="距离上方组件的长度">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="下边界">
            <el-input v-model="formData.pageMarginBottom" size="mini" :mini="0" type="number" style="margin-top: 5px"
                      placeholder="距离下方组件的长度">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="左边界">
            <el-input v-model="formData.pageMarginLeft" size="mini" :mini="0" type="number" style="margin-top: 5px"
                      placeholder="距离左边框的长度">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="右边界">
            <el-input v-model="formData.pageMarginRight" size="mini" :mini="0" type="number" style="margin-top: 5px"
                      placeholder="距离右边框的长度">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="组件背景">
            <el-input v-model="formData.backColor" size="small" style="margin-top: 5px">
              <template slot="append">
                <el-color-picker size="mini" v-model="formData.backColor"></el-color-picker>
              </template>
            </el-input>
          </el-form-item>
          <el-divider>内容属性</el-divider>
          <el-form-item label="按钮类型">
            <el-radio-group v-model="formData.type">
              <el-radio :label="0">文字</el-radio>
              <el-radio :label="1">图片</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="上边距">
            <el-input v-model="formData.contentPaddingTop" size="mini" :mini="0" type="number" style="margin-top: 5px"
                      placeholder="距离上方组件的长度">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="下边距">
            <el-input v-model="formData.contentPaddingBottom" size="mini" :mini="0" type="number" style="margin-top: 5px"
                      placeholder="距离下方组件的长度">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="左边距">
            <el-input v-model="formData.contentPaddingLeft" size="mini" :mini="0" type="number" style="margin-top: 5px"
                      placeholder="距离左边框的长度">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="右边距">
            <el-input v-model="formData.contentPaddingRight" size="mini" :mini="0" type="number" style="margin-top: 5px"
                      placeholder="距离右边框的长度">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <div v-if="formData.type==0">
            <el-form-item label="按钮内容">
              <el-input v-model="formData.content" size="small" style="margin-top: 5px"></el-input>
            </el-form-item>
            <el-form-item label="字体大小">
              <el-input-number v-model="formData.fontSize" size="small" controls-position="right" :min="1"
                               :max="30"></el-input-number>
            </el-form-item>
            <el-form-item label="字体间距">
              <el-input-number v-model="formData.spacing" size="small" controls-position="right" :min="1"
                               :max="30"></el-input-number>
            </el-form-item>
            <el-form-item label="字体粗细">
              <el-input-number v-model="formData.fontWeight" size="small" controls-position="right"
                               :step-strictly="true" :step="100" :min="100" :max="900"></el-input-number>
            </el-form-item>
            <el-form-item label="按钮大小">
              <el-radio-group v-model="formData.size">
                <el-radio :label="'100%'">整体</el-radio>
                <el-radio :label="'default'">普通</el-radio>
                <el-radio :label="'mini'">小型</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="按钮位置">
              <el-radio-group v-model="formData.location">
                <el-radio :label="'flex-start'">靠左</el-radio>
                <el-radio :label="'center'">居中</el-radio>
                <el-radio :label="'flex-end'">靠右</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="字体颜色">
              <el-input v-model="formData.fontColor" size="small" style="margin-top: 5px">
                <template slot="append">
                  <el-color-picker size="mini" v-model="formData.fontColor"></el-color-picker>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="按钮颜色">
              <el-input v-model="formData.buttonColor" size="small" style="margin-top: 5px">
                <template slot="append">
                  <el-color-picker size="mini" v-model="formData.buttonColor"></el-color-picker>
                </template>
              </el-input>
            </el-form-item>
          </div>
          <div v-if="formData.type==1">
            <el-form-item label="按钮图片">
              <MaterialList :value="formData.imgUrl?[formData.imgUrl]:[]"
                            @sureSuccess="formData.imgUrl = $event?$event[0]:''"
                            @deleteMaterial="formData.imgUrl = ''"
                            type="image" shopId="-1"
                            :num=1
                            :divStyle="'width:50%;height:95px;margin-bottom:8px;line-height: 100px;'"></MaterialList>
            </el-form-item>
            <el-form-item label="图片宽度">
              <el-input-number v-model="formData.imgWidth" size="mini" type="number" :min="0" :max="100"
                               placeholder="开始位置">
              </el-input-number>
              %
            </el-form-item>
          </div>
          <el-form-item label="圆角设置">
            <el-slider v-model="formData.borderRadius" :max="40"></el-slider>
          </el-form-item>
        </el-form>
      </div>
    </settingSlot>
    <el-dialog
      title="内部数据"
      :visible.sync="interiorBoxVisible "
      :close-on-click-modal="false"
      center
      :append-to-body="true"
      lock-scroll
      width="80%">
      <div style="overflow: hidden">
        <interiorData :pageId="pageId" v-on:ensureInterior="ensureInterior" @backFun="ensureInterior"></interiorData>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';
import draggable from "vuedraggable";
import settingSlot from '../settingSlot'
import MaterialList from '@/components/material/wxlist.vue'
import interiorData from "@/components/interior-data/main";

export default {
  components: {settingSlot, draggable, interiorData, MaterialList},
  data() {
    return {
      tabActiveName: 0,
      formDataCopy: {
        pageMarginTop: 0,
        pageMarginBottom: 0,
        pageMarginLeft: 0,
        pageMarginRight: 0,
        backColor: '#FFFFFF',
        type: 0,
        contentPaddingTop: 0,
        contentPaddingBottom: 0,
        contentPaddingLeft: 0,
        contentPaddingRight: 0,

        content: '送上祝福',
        fontSize: 15,
        spacing: 4,
        fontWeight: 400,
        location: 'center',
        size: "default",
        fontColor: '#FFFFFF',
        buttonColor: '#fa5151',

        borderRadius: 0,
        imgWidth: 100,
        imgUrl: '',
      },
      formData: {},
      interiorBoxVisible: false
    };
  },
  props: {
    thememobile: {type: Object | Array},
    showData: {
      type: Object,
      default: () => {
      }
    },
  },
  watch: {
    showData: {
      handler(newVal, oldVal) {
        this.formData = newVal ? newVal : this.formData;
      },
      deep: true
    },

  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
      clickComIndex: state => state.divpage.clickComIndex,
    })
  },
  created() {
    this.pageId = window.localStorage.getItem('viewEditId');
    console.log("滚动列表", this.pageId)
  },
  mounted() {
    let that = this;
    if (that.IsEmptyObj(that.showData)) {

      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
    } else {
      that.formData = that.showData
    }

    that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
    // that.updateData({
    //   componentsList: that.componentsList
    // })
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    // 删除按钮
    cancel() {
      this.$emit('cancel')
    },
    reset() {
      let that = this;
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      that.componentsList[that.clickComIndex]['data'] = this.formData;
    },
    confirm() {
      this.$emit('confirm', this.formData)
    },
    delete() {
      this.$emit('delete')
    },
    openInteriorBox() {
      this.interiorBoxVisible = true;
    },
    ensureInterior() {
    },
  },
};
</script>
<style lang='less' scoped>

.el-form-item {
  margin-bottom: 0;
}
</style>
