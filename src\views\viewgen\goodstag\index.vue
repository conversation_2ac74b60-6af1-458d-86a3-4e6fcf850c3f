<template>
  <div class="execution">
    <basic-container>

<!--      <el-row  type="flex" :gutter="20">-->
<!--        <el-col :span="24">-->
<!--          <div  class="selected_app">-->
<!--            所选公众号:-->
<!--            <el-select  v-model="appId"  placeholder="请选择" @change="getTagTypeList" >-->
<!--              <el-option-->
<!--                v-for="item in wxAppList"-->
<!--                :key="item.id"-->
<!--                :label="item.name"-->
<!--                :value="item.id">-->
<!--              </el-option>-->
<!--            </el-select>-->
<!--          </div>-->
<!--        </el-col>-->
<!--      </el-row>-->

      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            placeholder="请输入要搜索的标签"
            size="small"
            @keyup.enter.native="search"
            v-model="searchValue">
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" size="small" @click="search">确认搜索</el-button>
          <el-button type="primary" size="small" @click="resetSearch">重置</el-button>
        </el-col>
        <el-col :span="15">
          <el-button type="primary" size="small" @click="openTagBox('add')"><i class="el-icon-circle-plus"></i>新建标签</el-button>
        </el-col>
      </el-row>

      <el-row >
        <el-col :span="3">
          <el-card shadow="never">
            <div slot="header">
              <span>公众号名称</span>
            </div>
            <el-tree
              style="margin-top: 5px"
              :data="treeWxAppData"
              :props="treeWxAppProps"
              :filter-node-method="filterNode"
              node-key="id"
              default-expand-all
              ref="tree"
              @node-click="nodeClick">
            </el-tree>
          </el-card>
        </el-col>
        <el-col :span="3">
          <div class="aside_tag_type">
            <div>
              <el-button type="success" style="margin: 20px 0px 0px 0px " @click="openTagTypeManagerBox" size="small"><i></i>分类管理</el-button>
            </div>
            <ul v-for="(item,index) in tagTypeList" :key="index">
              <li class="tag_type_li" @click="ensureLocation(item.id)" >
                <h4 class="tag_type_name">{{item.name}}</h4>
              </li>
            </ul>
          </div>
        </el-col>
        <el-col :span="18">
          <div class="main_tag_show">
            <div v-for="(tagType,index) in tagTypeList" v-show="tagList[tagType.id]" :key="index">
              <div :id="'tagTypeId'+tagType.id">
                <el-divider content-position="left">{{ tagType.name }}</el-divider>
                <div class="main_tag" v-for="(tag,index) in tagList[tagType.id]" :key="index">
                  <el-popover
                    :ref="tag.id"
                    placement="bottom"
                    width="150px"
                    trigger="hover">
                    <div style=" text-align: right; margin: 0;padding: 0">
                      <el-button class="main_popover_button" icon="el-icon-setting" type="mini" size="mini" @click="openDrawerVisible(tag)">管理作品</el-button>
                      <el-button class="main_popover_button" icon="el-icon-edit" type="mini" size="mini" @click="openTagBox('put',tag)">修改标签</el-button>
                      <el-button class="main_popover_button" icon="el-icon-delete-solid" type="mini" size="mini" @click="openDelTag(tag.id)">删除标签</el-button>
                    </div>
                  </el-popover>
                  <el-tag v-show="tag.typeId == tagType.id"  v-popover="tag.id"  size="small" :color="tag.backColor" :style="getFontColor(tag.fontColor)">{{tag.name}}</el-tag>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </basic-container>

    <!--    添加标签框-->
    <el-dialog
      :title="tagBox.title"
      :visible.sync="tagBox.visible"
      width="30%"
      :close-on-click-modal="false"
      center>
      <el-form ref="form" :model="tag" label-width="80px">
        <el-form-item label="标签名称">
          <el-input v-model="tag.name"  :maxlength="25"  show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="标签分类">
          <el-select v-model="tag.typeId" clearable placeholder="请选择">
            <el-option
              v-for="item in tagTypeList"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
          <el-button type="primary" size="mini" @click="openTagTypeBox">添加分类</el-button>
        </el-form-item>
        <el-row  >
          <el-col :span="12">
            <el-form-item label="背景色">
              <el-color-picker v-model="tag.backColor"></el-color-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="字体色">
              <el-color-picker v-model="tag.fontColor"></el-color-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="是否展示">
          <el-switch v-model="tag.showFlag" active-value="0" inactive-value="1"></el-switch>
        </el-form-item>
        <el-form-item label="效果展示" v-show="tag.name">
          <el-tag   size="small" :color="tag.backColor" :style="getFontColor(tag.fontColor)">{{tag.name}}</el-tag>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="confirmTag">确认</el-button>
          <el-button @click="tagBox.visible = false">取消</el-button>
        </el-form-item>
      </el-form>

      <!--     嵌套添加分类框-->
      <el-dialog
        width="30%"
        title="添加分类"
        :visible.sync="tagTypeBoxVisible"
        :close-on-click-modal="false"
        append-to-body>
        <el-form ref="form" :model="tagType" label-width="80px">
          <el-form-item label="分类名称">
            <el-input v-model="tagType.name"  :maxlength="25"  show-word-limit></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="addTagType">立即创建</el-button>
            <el-button @click="tagTypeBoxVisible = false">取消</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </el-dialog>

    <!--    分类管理-->
    <el-dialog title="分类管理" :visible.sync="openTagTypeManagerBoxVisible">
      <template>
        <div class="execution">
          <basic-container>
            <avue-crud ref="crud"
                       :page="page"
                       :data="tableData"
                       :permission="permissionList"
                       :table-loading="tableLoading"
                       :option="goodsTagTypeOption"
                       @row-save="handleSave"
                       @on-load="getTagTypePage"
                       @refresh-change="refreshChange"
                       @row-update="handleUpdate"
                       @row-del="handleDel">
            </avue-crud>
          </basic-container>
        </div>
      </template>
    </el-dialog>
    <!--    作品抽屉管理-->
    <el-drawer
      title="相关用户"
      :visible.sync="drawerVisible"
      :direction="'rtl'"
      :size="'80%'"
      :before-close="drawerClose">
      <goods-list :selectedTagId="drawerSelectedTagId" :appId="currentApp.id"></goods-list>
    </el-drawer>
  </div>
</template>

<script>
import { getSysList as getTagList,addObj as addTag,putObj as putTag, delObj as delTag,addAttention} from '@/api/viewgen/wxgoodstag'
import { getPage as getTagTypePage,getList as getTagTypeList,addObj as addTagType, putObj as putTagType, delObj as delTagType} from '@/api/viewgen/wxgoodstagtype'
import { goodsTagTypeOption } from '@/const/crud/viewgen/wxgoodstagtype'
import { mapGetters } from 'vuex'
import {getList as getWxAppList} from "@/api/wxmp/wxapp";
import goodsList from '@/views/viewgen/goodstag/tagGoods.vue'
export default {
  name: 'goodstag',
  components: {
    goodsList,
  },
  data() {
    return {
      treeWxAppData: [],
      treeWxAppProps: {
        label: 'name',
        value: 'id'
      },
      wxAppList: [],
      currentApp: "",//当前公众号
      tableLoading: false,
      goodsTagTypeOption: goodsTagTypeOption,
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      tagBox: {
        title: '',
        visible: false,
        type: ''
      },
      tagTypeBoxVisible: false,//内部分类框
      openTagTypeManagerBoxVisible: false,//分类管理
      searchValue: "",//标签搜索值
      tagTypeList: [],//标签类别数组
      tagList: {},//标签数组
      tag:{
        fontColor: '',
        showFlag: '1',
      },
      tagType:{},
      drawerSelectedTagId:'',//打开抽屉选中的标签id
      drawerVisible:false,//打开抽屉
    };
  },
  watch: {

  },
  created() {
    this.getWxApp()

  },
  mounted: function() { },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.permissions['wxmp:wxusertags:add'] ? true : false,
        delBtn: this.permissions['wxmp:wxusertags:del'] ? true : false,
        editBtn: this.permissions['wxmp:wxusertags:edit'] ? true : false,
        viewBtn: this.permissions['wxmp:wxusertags:get'] ? true : false
      }
    }
  },
  methods: {
    //点击查询
    nodeClick(data) {
      console.log("data",data)
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(data.id)
      })
      this.currentApp = data;
      this.tableData = []
      this.page.total = 0
      this.page.currentPage = 1
      this.tagTypeList= [];//标签类别数组
      this.tagList = [];
      this.getTagTypeList();
      this.getTagList();
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    getWxApp(){
      getWxAppList({
        appType: '2'
      }).then(res => {
        let data = res.data
        this.treeWxAppData = data
        if(data && data.length > 0){
          //默认加载第一个公众号的素材

          this.nodeClick({
            id: data[0].id
          })
          this.currentApp = data[0];
        }
        this.wxAppList = data;
        this.getTagTypeList();
      }).catch(() => {
      })
    },
    //加载标签类别
    getTagTypeList() {
      getTagTypeList({
        appId:this.currentApp.id
      }).then(res=>{
        this.tagTypeList = res.data.data;
        this.getTagList();
      }).catch(err=>{
        console.log(err)
      })
    },
    //加载标签
    getTagList() {
      getTagList().then(res=>{
        console.log("加载标签", res);
        //再分组
        this.groupTag(res.data.data)
      }).catch(err=>{
        console.log(err)
      })
    },
    //搜索标签
    search() {
      getTagList({
        name: this.searchValue
      }).then(res=>{
        console.log("搜索标签",(res.data.data))
        //再分组
        this.groupTag(res.data.data)
      }).catch(err=>{
        console.log(err)
      })
    },
    //重置搜索
    resetSearch() {
      this.searchValue ="";
      getTagList({
      }).then(res=>{
        console.log("搜索标签",(res.data.data))
        //再分组
        this.groupTag(res.data.data)
      }).catch(err=>{
        console.log(err)
      })
    },
    //标签分组
    groupTag(list){
      this.tagList = {};
      for (let i in list) {
        let key = list[i].typeId;
        if (!this.tagList.hasOwnProperty(key)) {
          this.$set(this.tagList, key, []);
        }
        this.tagList[key].push(list[i]);
      }
    },
    openTagBox(type,obj) {
      if(type == 'add') {
        this.tag = {
          name: "",
          backColor: '#409eff',
          fontColor: '#fff',
          showFlag: "0",
        }
        this.tagBox.type = "add";
        this.tagBox.title = "添加标签";
      }
      if(type == 'put'){
        this.tagBox.type = "put";
        this.tagBox.title = "修改标签";
        this.tag = {
          id: obj.id,
          name: obj.name,
          backColor: obj.backColor,
          fontColor: obj.fontColor,
          typeId: obj.typeId,
          showFlag: obj.showFlag,
        }
      }
      this.tagBox.visible = true;
    },
    openTagTypeBox() {
      this.tagType = {};
      this.tagTypeBoxVisible = true;
    },
    //转化得到字体颜色
    getFontColor(val){
      if(!val){
        return;
      }
      return "color:" + val;
    },
    // 添加标签
    confirmTag(){
      if (!this.tag.name) {
        this.$message.warning("请填写标签名称");
        return;
      }
      if (!this.tag.typeId) {
        this.$message.warning("请选择标签类型");
        return;
      }
      if(this.tagBox.attention && this.tagBox.type =='add') {
        console.log("保存参数", this.tag)
        addAttention({
          name: this.tag.name,
          backColor: this.tag.backColor,
          fontColor: this.tag.fontColor,
          typeId: this.tag.typeId,
        }).then(res => {
          if (res.data.code == 0) {
            this.$message.success("添加成功")
            this.getTagList();
            this.tagBox.visible = false;
          }
        }).catch()
      }
      if(this.tagBox.type =='add') {
        console.log("保存参数", this.tag)
        addTag({
          name: this.tag.name,
          backColor: this.tag.backColor,
          fontColor: this.tag.fontColor,
          typeId: this.tag.typeId,
          showFlag: this.tag.showFlag,
        }).then(res => {
          if (res.data.code == 0) {
            this.$message.success("添加成功")
            this.getTagList();
            this.tagBox.visible = false;
          }
        }).catch()
      }
      if(this.tagBox.type =='put') {
        console.log("保存参数", this.tag)
        putTag({
          id: this.tag.id,
          name: this.tag.name,
          backColor: this.tag.backColor,
          fontColor: this.tag.fontColor,
          typeId: this.tag.typeId,
          showFlag: this.tag.showFlag,
        }).then(res => {
          if (res.data.code == 0) {
            this.$message.success("修改成功")
            this.getTagList();
            this.tagBox.visible = false;
          }
        }).catch();
      }
    },
    // 添加分类
    addTagType(){
      if (!this.tagType.name) {
        this.$message.warning("请填写分类名称");
        return;
      }
      addTagType({
        name:this.tagType.name,
        appId:this.currentApp.id
      }).then(res=>{
        if(res.data.code ==0){
          this.$message.success("添加成功")
          this.getTagTypeList();
          this.tagTypeBoxVisible = false;
        }
      }).catch()
    },
    // 点击确定定位
    ensureLocation(id){
      // todo 吸顶问题的解决
      // console.log(id)
      let name = ("tagTypeId" + id);
      document.getElementById(name).scrollIntoView({behavior: "smooth",block: "center", inline: "nearest"});
      // document.getElementById(name).scroll({
      //   top: height, //向上移动的距离，如果有fixede布局， 直接减去对应距离即可
      //   behavior: 'smooth', // 平滑移动
      // });
    },
    openDelTag(id) {
      this.$confirm('此操作将永久删除该标签, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delTag(id).then(res => {
          if (res.data.code == 0) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
            this.refreshChange();
          }
        }).catch()
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    openTagTypeManagerBox() {
      this.openTagTypeManagerBoxVisible = true;
      this.getTagTypePage(this.page);
      console.log(this.tagTypeList);
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel (row, index) {
      let _this = this
      this.$confirm('是否确认删除此数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then( ()=>{
        delTagType(row.id).then(()=>{
          this.refreshChange();
        })
      }).then(data => {
        _this.$message({
          showClose: true,
          message: '删除成功',
          type: 'success'
        })
        this.getTagTypePage(this.page)
      }).catch(function (err) {
      })
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate (row, index, done, loading) {
      putTagType(row).then(response => {
        this.$message({
          showClose: true,
          message: '修改成功',
          type: 'success'
        })
        done()
        this.getTagTypePage(this.page);
        this.getTagTypeList();
      }).catch(() => {
        loading()
      })
    },
    /**
     * 刷新回调
     */
    refreshChange() {
      this.getTagTypePage(this.page)
      this.getTagTypeList();
      this.getTagList();
    },
    getTagTypePage(page, params) {
      this.tableLoading = true
      getTagTypePage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
        appId:this.currentApp.id
      }, params, this.paramsSearch)).then(res => {
        this.tableData = res.data.data.records
        this.page.total = res.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    handleSave(row, done, loading) {
      addTagType({
        name:row.name,
        appId:this.currentApp.id
      }).then(res => {
        this.$message({
          showClose: true,
          message: '添加成功',
          type: 'success'
        })
        done()
        this.refreshChange()
      }).catch(() => {
        loading()
      })
    },
    //管理作品抽屉打开
    openDrawerVisible(tag){
      this.drawerSelectedTagId = tag.id;
      console.log('点击表气啊',tag.id)
      this.drawerVisible = true;
    },
    //抽屉关闭
    drawerClose(done) {
      done();
    },
  }
}
</script>

<style lang="scss" scoped>
.selected_app{
  margin-bottom: 10px;;
}

.main_popover_button {
  display: block;
  margin: 0px 0px 5px 5px;
  display: block;
  width: 140px
}

.aside_tag_type{
  //position: fixed;
  text-align: center;
}
.main_tag_show{
  padding-left: 10px;
}

.tag_type_li{
  padding-top: 10px;
  border:1px solid white;
}
.tag_type_li:hover{
  color: #409eff;;
  border-bottom:1px solid #409eff;;
  cursor: pointer;
}

.tag_type_name{
  overflow: hidden;
  text-overflow: ellipsis;
  padding-bottom: 15px;
}
.main_tag{
  display: inline-block;
  padding-right: 20px;
  padding-bottom: 30px;
}
</style>
