<!--用户选择组件-->
<template>
  <div class="execution">

    <avue-crud ref="crud"
               :page.sync="page"
               :data="tableData"
               :table-loading="tableLoading"
               :option="tableOption"
               :permission="permissionList"
               @on-load="getPage"
               @refresh-change="refreshChange"
               @row-update="handleUpdate"
               @row-click="rowClick"
               @row-save="handleSave"
               @row-del="handleDel"
               @sort-change="sortChange"
               @search-change="searchChange"
               @search-reset="searchReset">
      <template slot-scope="scope" slot="searchBtnSearch">{{" "}}</template>
      <template slot-scope="scope" slot="search">
        <el-form ref="form" style="width: 100%; display: inline-block;"  :model="userSearchForm" label-width="85px" size="mini">
          <el-form-item label="所选公众号" style="width: 400px">
            <el-select  v-model="userSearchForm.appIdList" multiple collapse-tags placeholder="请选择" >
              <el-option
                v-for="item in wxAppList"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
<!--          <el-form-item label="搜索类型">-->
<!--            <el-radio-group v-model="userGetTypeRadio">-->
<!--              <el-radio :label="1">用户属性</el-radio>-->
<!--              <el-radio :label="2">用户行为</el-radio>-->
<!--              <el-radio :label="3">接待客服</el-radio>-->
<!--              <el-radio :label="4">关注来源</el-radio>-->
<!--              <el-radio :label="5">微信广告</el-radio>-->
<!--            </el-radio-group>-->
<!--          </el-form-item>-->
          <el-row v-if="userGetTypeRadio">
            <el-col :span="10">
              <el-form-item label="关注时间">
                <el-date-picker
                  v-model="userSearchForm.subscribeTimeList"
                  type="daterange"
                  align="right"
                  unlink-panels
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd 00:00:00"
                  @change="subscribeTimeOnPick"
                  :picker-options="attentionDateOptions">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="userGetTypeRadio==1">
            <el-col :span="5">
              <el-form-item label="关注来源">
                <el-select v-model="userSearchForm.subscribeScene" placeholder="请选择">
                  <el-option
                    v-for="item in attentionFromOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="是否关注">
                <el-select v-model="userSearchForm.subscribe" placeholder="请选择">
                  <el-option
                    v-for="item in isAttentionOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="是否黑名单">
                <el-select v-model="userSearchForm.inBackList" placeholder="请选择">
                  <el-option
                    v-for="item in blackListOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="是否有手机号" label-width="100px" >
                <el-select v-model="userSearchForm.phoneType" placeholder="请选择">
                  <el-option
                    v-for="item in phoneNumberOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="userGetTypeRadio==1">
            <el-col :span="5">
              <el-form-item label="昵称">
                <el-input v-model="userSearchForm.nickName" placeholder="请输入昵称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="openId">
                <el-input v-model="userSearchForm.openId" placeholder="请输入openId"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="手机号码">
                <el-input v-model="userSearchForm.phone" placeholder="请输入手机号"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
<!--          <el-row v-if="userGetTypeRadio==2">-->
<!--            <el-form-item label="行为节点">-->
<!--              <el-select v-model="userSearchForm.behaviorNodes" multiple placeholder="请选择">-->
<!--                <el-option-group-->
<!--                  v-for="group in behaviorNodeList"-->
<!--                  :key="group.label"-->
<!--                  :label="group.label">-->
<!--                  <el-option-->
<!--                    v-for="item in group.options"-->
<!--                    :key="item.value"-->
<!--                    :label="item.label"-->
<!--                    :value="item.value">-->
<!--                  </el-option>-->
<!--                </el-option-group>-->
<!--              </el-select>-->
<!--            </el-form-item>-->
<!--          </el-row>-->
<!--          <el-row v-if="userGetTypeRadio==2">-->
<!--            <el-col :span="6">-->
<!--              <el-form-item label="记录时间">-->
<!--                <el-date-picker-->
<!--                  v-model="behaviorNodeTimeList"-->
<!--                  type="daterange"-->
<!--                  align="right"-->
<!--                  unlink-panels-->
<!--                  range-separator="至"-->
<!--                  start-placeholder="开始日期"-->
<!--                  end-placeholder="结束日期"-->
<!--                  value-format="yyyy-MM-dd 00:00:00"-->
<!--                  @change="behaviorNodeTimeOnPick"-->
<!--                  :picker-options="attentionDateOptions">-->
<!--                </el-date-picker>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
<!--          </el-row>-->
<!--          <el-row v-if="userGetTypeRadio==3">-->
<!--            <el-form-item label="行为节点">-->
<!--              <el-select v-model="userSearchForm.appIdList" multiple placeholder="请选择">-->
<!--                <el-option-->
<!--                  v-for="item in wxAppList"-->
<!--                  :key="item.id"-->
<!--                  :label="item.name"-->
<!--                  :value="item.id">-->
<!--                </el-option>-->
<!--              </el-select>-->
<!--            </el-form-item>-->
<!--          </el-row>-->
<!--          <el-row v-if="userGetTypeRadio==4">-->
<!--            <el-form-item label="来源类型">-->
<!--              <el-select v-model="userSearchForm.appIdList" multiple placeholder="请选择">-->
<!--                <el-option-->
<!--                  v-for="item in wxAppList"-->
<!--                  :key="item.id"-->
<!--                  :label="item.name"-->
<!--                  :value="item.id">-->
<!--                </el-option>-->
<!--              </el-select>-->
<!--            </el-form-item>-->
<!--          </el-row>-->
<!--          <el-row v-if="userGetTypeRadio==4">-->
<!--            <el-col :span="6">-->
<!--              <el-form-item label="关注时间">-->
<!--                <el-date-picker-->
<!--                  v-model="userSearchForm.attentionDate"-->
<!--                  type="daterange"-->
<!--                  align="right"-->
<!--                  unlink-panels-->
<!--                  range-separator="至"-->
<!--                  start-placeholder="开始日期"-->
<!--                  end-placeholder="结束日期"-->
<!--                  :picker-options="attentionDateOptions">-->
<!--                </el-date-picker>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
<!--          </el-row>-->
<!--          <el-row v-if="userGetTypeRadio==5">-->
<!--            <el-col :span="3">-->
<!--              <el-form-item label="投放计划">-->
<!--                <el-select v-model="userSearchForm.subscribeScene" placeholder="请选择">-->
<!--                  <el-option-->
<!--                    v-for="item in attentionFromOptions"-->
<!--                    :key="item.value"-->
<!--                    :label="item.label"-->
<!--                    :value="item.value">-->
<!--                  </el-option>-->
<!--                </el-select>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
<!--            <el-col :span="3">-->
<!--              <el-form-item label="广告id">-->
<!--                <el-select v-model="userSearchForm.attentionFrom" placeholder="请选择">-->
<!--                  <el-option-->
<!--                    v-for="item in isAttentionOptions"-->
<!--                    :key="item.value"-->
<!--                    :label="item.label"-->
<!--                    :value="item.value">-->
<!--                  </el-option>-->
<!--                </el-select>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
<!--          </el-row>-->
          <el-row v-if="userGetTypeRadio==5">
            <el-col :span="6">
              <el-form-item label="关注时间">
                <el-date-picker
                  v-model="userSearchForm.attentionDate"
                  type="daterange"
                  align="right"
                  unlink-panels
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :picker-options="attentionDateOptions">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </template>
      <template slot="subscribe" slot-scope="scope">
        <el-tag size="mini" effect="dark"
                :type="scope.row.subscribe == '1' ? 'success' : scope.row.subscribe == '0' ? 'danger' : 'warning'">
          {{ scope.row.$subscribe }}
        </el-tag>
      </template>
      <template slot="sex" slot-scope="scope">
        <el-tag size="mini" effect="light"
                :type="scope.row.sex == '1' ? '' : scope.row.sex == '2' ? 'danger' : 'warning'">{{ scope.row.$sex }}
        </el-tag>
      </template>
      <template slot="latitude" slot-scope="scope">
        <el-link v-if="scope.row.longitude" type="primary" target="_blank"
                 :href="'https://map.qq.com/?type=marker&isopeninfowin=1&markertype=1&pointx='+scope.row.longitude+'&pointy='+scope.row.latitude+'&name='+scope.row.nickName+'&ref=gocreateone'">
          <i class="el-icon-map-location"></i>
        </el-link>
      </template>
    </avue-crud>
  </div>
</template>

<script>
import {getPageAndCon} from '@/api/wxmp/wxuser'
import {getList as getWxAppList} from '@/api/wxmp/wxapp'
import {userSelectTableOption} from '@/const/crud/wxmp/wxuser'
import {mapGetters} from 'vuex'

export default {
  name: 'userSelect',
  components: {},
  data() {
    return {
      wxAppList:[],
      //用户关注来源参数
      attentionFromOptions: [
        {
        value: 'ADD_SCENE_SEARCH',
        label: '公众号搜索'
      }, {
        value: 'ADD_SCENE_ACCOUNT_MIGRATION',
        label: '公众号迁移'
      }, {
        value: 'ADD_SCENE_QR_CODE',
        label: '扫描二维码'
      }, {
        value: 'ADD_SCENE_PROFILE_CARD',
        label: '名片分享'
      }, {
        value: 'ADD_SCENEPROFILE_LINK ',
        label: '图文页内名称点击'
      }, {
        value: 'ADD_SCENE_PROFILE_ITEM',
        label: '图文页右上角菜单'
      }, {
        value: 'ADD_SCENE_PAID',
        label: '支付后关注'
      }, {
        value: 'ADD_SCENE_OTHERS',
        label: '其他'
      }],
      userGetTypeRadio: 1,
      //时间参数
      attentionDateOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      // 用户是否关注参数
      isAttentionOptions: [{
        value: '0',
        label: '未关注'
      }, {
        value: '1',
        label: '已关注'
      }, {
        value: '2',
        label: '网页授权'
      }
      ],
      //用户黑名单参数
      blackListOptions: [{
        value: '0',
        label: '是黑名单用户'
      }, {
        value: '1',
        label: '非黑名单用户'
      }
      ],
      //是否存有手机号码参数
      phoneNumberOptions: [{
        value: '0',
        label: '有手机号码'
      }, {
        value: '1',
        label: '无手机号码'
      }],
      //用户查询参数
      userSearchForm: {
        appIdList: [],//选中公众号查询
      },
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],
        descs: 'subscribe_time'
      },
      tableData: [],
      paramsSearch: {},
      tableLoading: false,
      tableOption: userSelectTableOption,
      dialogTagging: false,
      dialogMsgVisible: false,
      wxUserId: '',
      bathPutPhoneBoxVisible: false,
      idAndPhoneValue: "",
    }
  },
  watch: {},
  created() {
    this.getWxAppList()
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.permissions['wxmp:wxuser:add'] ? true : false,
        delBtn: this.permissions['wxmp:wxuser:del'] ? true : false,
        editBtn: this.permissions['wxmp:wxuser:edit'] ? true : false,
        viewBtn: this.permissions['wxmp:wxuser:get'] ? true : false,
      }
    }
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    //加载公众号列表
    getWxAppList() {
      getWxAppList({
        appType: '2'
      }).then(response => {
        let data = response.data
        this.wxAppList = data;
        this.appId = data[0].id;
        this.appChange(data[0].id);
      }).catch(() => {

      })
    },
    appChange(data) {
      if (this.appId != data.id) {
        // this.$nextTick(() => {
        //   this.$refs.tree.setCurrentKey(data.id)
        // })
        this.tableData = []
        this.page.total = 0
        this.page.currentPage = 1
        this.appId = data
        this.paramsSearch = {}
        this.$refs.crud.searchReset()
        this.$refs.crud.DIC.tagidList = []
        this.userTagsData = []
      }
    },
    searchChange(params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.paramsSearch={
        subscribeScene:this.userSearchForm.subscribeScene,
        subscribeTimeBegin:this.userSearchForm.subscribeTimeList && this.userSearchForm.subscribeTimeList.length > 0 ? this.userSearchForm.subscribeTimeList[0] : '',
        subscribeTimeEnd:this.userSearchForm.subscribeTimeList && this.userSearchForm.subscribeTimeList.length > 1 ? this.userSearchForm.subscribeTimeList[1] : '',
        subscribe:this.userSearchForm.subscribe,
        inBackList:this.userSearchForm.inBackList,
        phoneType:this.userSearchForm.phoneType,
        openId:this.userSearchForm.openId,
        nickName:this.userSearchForm.nickName,
        phone:this.userSearchForm.phone,
      }
      this.getPage(this.page, params)
      done()
    },
    searchReset(params) {
      console.log("重置表单",params);
      this.userSearchForm = {
        appIdList: []
      };
      this.paramsSearch = {};
      this.getPage(this.page);
    },
    sortChange(val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : '';
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage(page, params) {
      if (this.appId) {
        this.tableLoading = true
        let obj = Object.assign({
          current: page.currentPage,
          size: page.pageSize,
          descs: this.page.descs,
          ascs: this.page.ascs,
          appType: '2',
        }, params, this.paramsSearch);
        console.log("q请求参数，",obj)
        getPageAndCon(obj).then(response => {

          this.tableData = response.data.data.records
          this.page.total = response.data.data.total
          this.page.currentPage = page.currentPage
          this.page.pageSize = page.pageSize
          this.tableLoading = false
        }).catch(() => {
          this.tableLoading = false
        })
      }
    },
    handleDel: function (row, index) {
      var _this = this
      this.$confirm('是否确认删除', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        return delObj(row.id)
      }).then(data => {
        _this.$message({
          showClose: true,
          message: '删除成功',
          type: 'success'
        })
        this.getPage(this.page)
      }).catch(function (err) {
      })
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      putObj(row).then(data => {
        this.$message({
          showClose: true,
          message: '修改成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      addObj(row).then(data => {
        this.$message({
          showClose: true,
          message: '添加成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * @title 行点击
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    rowClick: function (row, done, loading) {
      console.log("点击",row)
      this.$emit("ensureUser", row);
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page)
    },
    //关注时间
    subscribeTimeOnPick() {
      if (this.userSearchForm.subscribeTimeList && this.userSearchForm.subscribeTimeList.length > 0) {
        this.userSearchForm.subscribeTimeBegin = this.userSearchForm.subscribeTimeList[0];
        this.userSearchForm.subscribeTimeEnd = this.userSearchForm.subscribeTimeList.length > 1 ? this.userSearchForm.subscribeTimeList[1] : '';
      } else {
        this.userSearchForm.subscribeTimeBegin = "";
        this.userSearchForm.subscribeTimeEnd = "";
      }
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
