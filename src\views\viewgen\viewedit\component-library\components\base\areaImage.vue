<!-- 地图组件 -->
<template>
  <div  class="flex justify-center align-center" :style="{background:`${setData.background}`, paddingTop:`${setData.paddingTop}px`,paddingBottom:`${setData.paddingBottom}px`}"   >
    <div v-if="!IsEmptyObj(setData)&&setData.imageUrl" class="imgBlock" ref="imageEle" style="display: flex;justify-content: center">
      <el-image :fit="'fit'" :src="setData.imageUrl" style="width: 100%;" :style="{ width: `${setData.width}%`, borderRadius: `${setData.borderRadius}px`}" >
        <div slot="placeholder" class="image-slot">
          加载中 <span class="dot">...</span>
        </div>
      </el-image>
    </div>
    <placeholderImg v-else></placeholderImg>
  </div>
</template>


<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';
import placeholderImg from "../../pages/page-components/placeholderImg";
export default {
  data() {
    return {
    };
  },
  components: {placeholderImg},
  props: {
    thememobile: {type: Object | Array},
    setData: {type: Object | Array},
  },
  watch: {
    componentsList(newVal, oldVal) {          //添加的时候触发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    },

  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {

  },
  mounted() {
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
  },

};
</script>
<style lang='less' scoped>

@import '../../colorui/main.css';
@import '../../colorui/icon.css';

.t_map_style{
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
}
</style>
