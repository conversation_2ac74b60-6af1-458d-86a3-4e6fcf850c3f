<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.gocreateone.com
  - 注意：
  - 本软件为www.gocreateone.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
  <div class="execution">
    <basic-container>
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>业务配置</span>
        </div>
        <avue-form ref="form" v-model="form" :option="tableOption" @submit="handleUpdate" :permission="permissionList">
        </avue-form>
      </el-card>
    </basic-container>
  </div>
</template>

<script>
  import { getObj,putObj} from '@/api/viewgen/business-config'
  import {tableOption} from '@/const/crud/viewgen/business-config'
  import {mapGetters} from 'vuex'

  export default {
    name: 'businessconfigform',
    components: {

    },
    data() {
      return {
        form: {},
        tableOption: tableOption
      }
    },
    created() {
      this.handleGet()
    },
    mounted: function () {
    },
    computed: {
      ...mapGetters(['permissions']),
      permissionList() {
        return {
          addBtn: this.permissions['weixin:businessconfig:add'] ? true : false,
          delBtn: this.permissions['weixin:businessconfig:del'] ? true : false,
          editBtn: this.permissions['weixin:businessconfig:edit'] ? true : false,
          viewBtn: this.permissions['weixin:businessconfig:get'] ? true : false
        };
      }
    },
    methods: {
      handleGet: function () {
        getObj().then(response => {
          this.form = response.data.data ? response.data.data : {}
        })
      },
      /**
       * @title 数据更新 - 更新超时档期退款处理设置
       * @description 提交业务配置数据到后端接口 /weixin/business-config
       * @param form 表单数据
       * @param done 表单关闭函数
       *
       * 提交示例:
       * PUT /weixin/business-config
       * {
       *   "timeoutRefundHandle": "contact_service" | "force_delete"
       * }
       **/
      handleUpdate(form, done) {
        // 更新超时档期退款处理设置
        putObj(this.form).then(response => {
          done()
          this.$message({
            showClose: true,
            message: '业务配置修改成功',
            type: 'success'
          })
        }).catch(() => {
          done()
        })
      },
    }
  }
</script>

<style lang="scss" scoped>
</style>
