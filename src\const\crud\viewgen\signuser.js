/**
 * 客片分享 签约用户
 */
export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  menuWidth: 320,
  searchShow: false,
  excelBtn: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  printBtn: true,
  viewBtn: false,
  columnBtn: false,//列的显隐按钮
  searchMenuSpan: 6,
  column: [
    {
      label: '主键',
      prop: 'id',
    },
    {
      label: '单号',
      prop: 'orderNo',
    },
    {
      label: '姓名',
      prop: 'name',
    },
    {
      label: '当前票数' ,
      prop: 'voteNum',
    },
    {
      label: '创建时间',
      prop: 'createTime',
      sortable: true,
      rules: []
    },
    {
      label: '绑定时间',
      prop: 'bandingTime',
    },
    {
      label: '页面id',
      prop: 'pageId',
      hide: true,
    },
    {
      label: '页面id',
      prop: 'releaseFlag',
      hide: true,
    },
    {
      label: '客片列表',
      prop: 'shareImgList',
      hide: true,
    },
    {
      label: '分享小图',
      prop: 'titleImgUrl',
      hide: true,
    },
    {
      label: '展示页面',
      prop: 'pageName',
    },
    {
      label: '发布时间',
      prop: 'releaseTime',
      sortable: true,
    },
    {
      label: '页面标题',
      prop: 'title',
    },
    {
      label: '女方手机',
      prop: 'womanPhone',
    },
    {
      label: '男方手机',
      prop: 'manPhone',
    },
    {
      label: 'openId',
      prop: 'openId',
    },
    {
      label: '公众号' ,
      prop: 'appId',
      dicData:[],
    },

  ]
}
