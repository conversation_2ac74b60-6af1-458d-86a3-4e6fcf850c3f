import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/shopinfo/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/weixin/shopinfo',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/weixin/shopinfo/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/shopinfo/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/shopinfo',
    method: 'put',
    data: obj
  })
}

export function getShopAndRoomTree() {
  return request({
    url: '/weixin/shopinfo/votree',
    method: 'get',
  })
}
