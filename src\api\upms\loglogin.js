import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/upms/loglogin/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/upms/loglogin',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/upms/loglogin/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/upms/loglogin/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/upms/loglogin',
        method: 'put',
        data: obj
    })
}
