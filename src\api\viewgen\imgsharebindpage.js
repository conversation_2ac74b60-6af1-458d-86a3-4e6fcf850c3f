import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/weixin/imgsharebindpage/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/weixin/imgsharebindpage',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/weixin/imgsharebindpage/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/weixin/imgsharebindpage/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/weixin/imgsharebindpage',
    method: 'put',
    data: obj
  })
}
