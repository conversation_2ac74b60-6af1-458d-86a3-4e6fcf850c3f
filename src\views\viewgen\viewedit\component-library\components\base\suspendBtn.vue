<!-- 悬浮按钮组件 -->
<template>
  <div class="suspendBtn" >
    <el-alert
      title="提示:"
      type="success"
      description="此组件悬浮显示，并不会占用页面位置，具体效果请在实际页面中查看～"
      center
      :closable="false">
    </el-alert>
  </div>
</template>

<script>
import {getList} from '@/api/viewgen/wxgoodstagtype'
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';

export default {
  data() {
    return {
      TabCur: 0,
      firstCategoryData: [],
      secondCategoryData: [],
      categoryData: [{
        id: '-1',
        name: '全部'
      }]
    };
  },
  components: {},
  props: {
    thememobile: {type: Object | Array},
    setData: {type: Object | Array},
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {

  },
  mounted() {
    this.getTagList();
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    getTagList() {
      getList().then(res => {
        console.log("查询list",res);
        this.categoryData = [...this.categoryData, ...res.data.data]
      }).catch(() => {

      })
    },

  },
  watch: {
    componentsList(newVal, oldVal) {          //添加的时候触发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    }
  }
};
</script>
<style lang='less' scoped>

@import '../../colorui/main.css';
@import '../../colorui/icon.css';

.suspendBtn {
  .img-category {
    width: 50px;
    height: 50px;
  }

  .more {
    margin-top: 30px;
  }

  .img-category-banner {
    width: 100%;
    height: 90px;
  }
}
</style>
