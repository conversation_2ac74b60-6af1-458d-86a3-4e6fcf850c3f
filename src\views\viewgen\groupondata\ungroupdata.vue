<template>
  <div class="execution">

    <avue-crud ref="crud"
               :page.sync="groupPage"
               :data="tableData"
               :permission="permissionList"
               :table-loading="tableLoading"
               :option="tableOption"
               v-model="form"
               @on-load="getGroupPage"
               @refresh-change="refreshGroup"
               @sort-change="sortGroupChange"
               :row-style="rowStyle"
               @selection-change="selectionChange"
               @search-change="searchChange">
      <template slot-scope="scope" slot="menuLeft">
        <el-input v-model="searchValue" :maxlength="64" placeholder="可以输入电话号码、昵称、openId" style="width:250px;padding-right: 5px"
                  size="small">
        </el-input>
        <el-button type="primary"
                   icon="el-icon-search"
                   size="small"
                   @click.stop="search">搜索
        </el-button>
        <el-button type="primary"
                   icon="el-icon-refresh-left"
                   size="small"
                   @click.stop="reset">重置
        </el-button>
        <el-button type="success"
                   icon="el-icon-s-custom"
                   size="small"
                   @click.stop="openFill('bath',null)">批量填充
        </el-button>
        <el-button type="success"
                   icon="el-icon-s-custom"
                   size="small"
                   @click.stop="batchFinish">批量成团
        </el-button>
        <el-button type="success"
                   icon="el-icon-s-custom"
                   size="small"
                   @click.stop="openDynamicFinishVisible">动态成团
        </el-button>
      </template>
      <template slot-scope="scope" slot="menu">
        <el-button icon="el-icon-s-custom" v-if="scope.row.isLeader=='1'" size="small" type="text"
                   @click="openFill('one',scope.row)">填充队员
        </el-button>
        <el-button icon="el-icon-s-custom" v-if="scope.row.isLeader=='1'" size="small" type="text"
                   @click="quicklyFinish(scope.row)">快速成团
        </el-button>
        <!--        <el-button icon="el-icon-s-order" size="small" type="text" @click="showDistributionOrder(scope.row,scope.index)">推广订单</el-button>-->
      </template>
      <template slot-scope="scope" slot="status">
        <el-tag v-if="scope.row.status == '0'" type="warning" size="mini" effect="light">拼团中</el-tag>
        <el-tag v-if="scope.row.status == '1'" type="success" size="mini" effect="light">拼团成功</el-tag>
      </template>
      <template slot-scope="scope" slot="isLeader">
        <el-tag v-if="scope.row.isLeader == '0'" effect="light" size="mini">团员</el-tag>
        <el-tag v-if="scope.row.isLeader == '1'" type="success" effect="dark" size="mini">团主</el-tag>
      </template>
      <template slot-scope="scope" slot="trueFlag">
        <el-tag v-if="scope.row.trueFlag == '0'" type="success" size="mini">是</el-tag>
        <el-tag v-if="scope.row.trueFlag == '1'" type="danger" effect="dark" size="mini">否</el-tag>
      </template>
      <template slot-scope="scope" slot="grouponNum">
        {{getLackNum(scope.row)}}
      </template>

      <!-- 自定义订单状态列显示 -->
      <template slot="orderStatusDesc" slot-scope="scope">
        <el-tag
          :type="getOrderStatusTagType(scope.row.orderInfo ? scope.row.orderInfo.statusDesc : '')"
          size="small">
          {{ scope.row.orderInfo ? scope.row.orderInfo.statusDesc : '-' }}
        </el-tag>
      </template>

      <!-- 自定义退款状态列显示 -->
      <template slot="refundStatus" slot-scope="scope">
        <el-tag
          v-if="getLatestRefundStatus(scope.row)"
          :type="getRefundStatusTagType(getLatestRefundStatus(scope.row))"
          size="small">
          {{ getLatestRefundStatus(scope.row) }}
        </el-tag>
        <span v-else>-</span>
      </template>

      <!-- 自定义购买详情列显示 -->
      <template slot="purchaseDetail" slot-scope="scope">
        <span v-if="getPurchaseDetail(scope.row)">
          {{ getPurchaseDetail(scope.row) }}
        </span>
        <span v-else>-</span>
      </template>
    </avue-crud>


    <el-dialog title="填充队员" :visible.sync="batchFillBoxVisible" width="20%" center @close="closeFillFormVisible">
      <el-form :rules="fillFormRules" :ref="fillForm" :model="fillForm" label-width="80px">
        <el-form-item label="填充人数" prop="fillNum">
          <el-select v-model="fillForm.fillNum" placeholder="请选择填充人数" >
            <div v-for="(item,index) in fillForm.fillNumSize" :key="index">
              <el-option v-if="!(index+1 == fillForm.fillNumSize)" :label=" (index+1) +'人'" :value="index+1"></el-option>
              <el-option v-if="index+1 == fillForm.fillNumSize" :label=" (index+1) +'人(直接成团)'" :value="index+1"></el-option>
            </div>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="confirmBathFill">确认</el-button>
          <el-button @click="closeFillFormVisible">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog title="动态成团" :visible.sync="batchFinishBoxVisible" width="20%" center @close="closeDynamicFormVisible">
      <el-form :rules="dynamicFormRules" :ref="dynamicForm" :model="dynamicForm" label-width="80px">
        <el-form-item label="距满团数" prop="gapNum">
          <el-select v-model="dynamicForm.gapNum" placeholder="请选择差距人数">
            <div v-for="(item,index) in dynamicForm.gapNumSize" :key="index">
              <el-option :label="(index+1)+'人'" :value="index+1"></el-option>
            </div>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="dynamicFinish">确认</el-button>
          <el-button @click="closeDynamicFormVisible">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {getGroupPage, putObj, quicklyFinish, dynamicFinish, bathFill} from '@/api/viewgen/wxgrouponuser'
import {getByPageId} from '@/api/viewgen/spellGroup'
import {tableOption} from '@/const/crud/viewgen/wxungrouponuser'
import {mapGetters} from 'vuex'

export default {
  name: 'unwxgrouponuser',
  props: {
    id: String//页面Id来的
  },
  data() {
    return {
      fillForm: {
        fillNum:null,//实际填充的人数
        fillNumSize:0,//可以填充的人数
      },//补充人员表单
      fillFormRules: {
        fillNum: [
          {required: true, message: '请选择填充人数', trigger: 'blur'}
        ],
      },

      dynamicForm: {
        gapNumSize:0
      },
      dynamicFormRules: {
        gapNum: [
          {required: true, message: '请选择成团参数', trigger: 'blur'}
        ],
      },
      searchValue: "",//搜索值
      bathList: [],//批量选择列表
      status: "0",//团队状态  未成团
      batchFinishBoxVisible: false,//批量成团
      batchFillBoxVisible: false,//批量补充
      form: {},
      tableData: [],
      groupPage: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption
    }
  },
  created() {

  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.permissions['weixinapi:wxspellgroupuser:add'] ? true : false,
        delBtn: this.permissions['weixinapi:wxspellgroupuser:del'] ? true : false,
        editBtn: this.permissions['weixinapi:wxspellgroupuser:edit'] ? true : false,
        viewBtn: this.permissions['weixinapi:wxspellgroupuser:get'] ? true : false
      };
    }
  },
  methods: {
    //打开批量填充
    openFill(type,item) {

      this.fillForm.type= type;

      if("bath" == type) {
        if (!this.bathList || this.bathList.length == 0) {
          this.$message.warning("请先勾选至少一条数据")
          return;
        }
        let size = this.bathList[0].grouponNum - (this.bathList[0].children ? this.bathList[0].children.length + 1 : 1);
        for (let i = 0; i < this.bathList.length; i++) {
          if (this.bathList[i].grouponNum - (this.bathList[i].children ? this.bathList[i].children.length + 1 : 1) != size) {
            this.$message.warning("请保证选中的数据，缺成团人数一致")
            return;
          }
        }
        this.fillForm.fillNumSize = size;
      }else  if("one" == type) {
        this.fillForm.one = item;
        let size = item.grouponNum - (item.children ? item.children.length + 1 : 1);
        this.fillForm.fillNumSize = size;
      }
      this.batchFillBoxVisible = true;
      console.log("1222",this.fillForm)
    },
    //确认填充
    confirmBathFill() {
      this.$refs[this.fillForm].validate((valid) => {
        if (valid) {
          this.$confirm('该操作不可逆，请确认所选团队无误，是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let list =[]
            if("bath" == this.fillForm.type) {
              for (let i = 0; i < this.bathList.length; i++) {
                this.bathList[i].fillNum = this.fillForm.fillNum;
              }
              list = this.bathList;
            }else if("one" == this.fillForm.type) {
              this.fillForm.one.fillNum = this.fillForm.fillNum
              let obj ={
                fillNum: this.fillForm.fillNum,
                groupId: this.fillForm.one.groupId,
                grouponId: this.fillForm.one.grouponId,
              }
              list.push(obj);
            }
            console.log("参数",list)
            bathFill(list).then(res => {
              console.log("填充队员", res)
              this.$message.success('填充队员操作成功')
              this.refreshGroup();
              this.batchFillBoxVisible = false;
            });
          }).catch(() => {
          });
        }
      });
    },
    closeFillFormVisible() {
      this.batchFillBoxVisible = false;
      this.$refs[this.fillForm].resetFields();
    },
    //批量成团
    batchFinish() {
      if (!this.bathList || this.bathList.length == 0) {
        this.$message.warning("请先勾选至少一条数据")
        return;
      }
      this.$refs[this.fillForm].validate((valid) => {
        if (valid) {
          this.$confirm('该操作不可逆，请确认所选团队无误，是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            quicklyFinish(this.bathList).then(res => {
              console.log("快速成团", res)
              this.$message.success('快速成团操作成功')
              this.refreshGroup();
            });
          }).catch(() => {
          });

        }
      });
    },
    //快速成团
    quicklyFinish(obj) {
      this.$confirm('该操作不可逆，确认进行此操作，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let list = [];
        list.push({
          grouponId: obj.grouponId,
          groupId: obj.groupId,
        });
        quicklyFinish(list).then(res => {
          console.log("快速成团", res)
          this.$message.success('快速成团操作成功')
          this.refreshGroup();
        });
      }).catch(() => {
      });
    },
    //缺少一位用户团队成团
    openDynamicFinishVisible() {
      getByPageId(this.id).then(res => {
        this.dynamicForm.gapNumSize = res.data.data.number-1;
        this.batchFinishBoxVisible = true;
      }).catch(err => {
        console.log("拼团信息err",err)
      })
    },
    closeDynamicFormVisible() {
      this.batchFinishBoxVisible = false;
      this.$refs[this.dynamicForm].resetFields();
    },
    //动态成团
    dynamicFinish() {
      this.$refs[this.dynamicForm].validate((valid) => {
        if (valid) {
          this.$confirm('优先让用户自行成团，该操作不可逆，请确认所有该类型团队无法成团，是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let params = {
              pageId: this.id,
              gapNum:this.dynamicForm.gapNum
            }
            console.log("211w12", params)
            dynamicFinish(params).then(res => {
              this.$message.success('快速成团操作成功')
              this.closeDynamicFormVisible();
              this.refreshGroup();
            });
          }).catch(() => {
          });
        }
      });
    },
    search() {
      this.paramsSearch = {
        id: this.id,
        status: this.status,
        nickName: this.searchValue,
        phone: this.searchValue,
        openId: this.searchValue
      }
      this.groupPage.currentPage = 1
      this.getGroupPage(this.groupPage)
    },
    reset() {
      this.searchValue = "";
      this.paramsSearch = {}
      this.groupPage.currentPage = 1
      this.getGroupPage(this.groupPage)
    },
    searchChange(params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.groupPage.currentPage = 1
      this.getGroupPage(this.groupPage, params)
      done()
    },
    sortGroupChange(val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.groupPage.descs = []
        this.groupPage.ascs = prop
      } else if (val.order == 'descending') {
        this.groupPage.ascs = []
        this.groupPage.descs = prop
      } else {
        this.groupPage.ascs = []
        this.groupPage.descs = []
      }
      this.getGroupPage(this.groupPage)
    },
    getGroupPage(page, params) {
      this.tableLoading = true
      if (!params) {
        params = {id: this.id, status: this.status};
      }
      getGroupPage(Object.assign({
        current: this.groupPage.currentPage,
        size: this.groupPage.pageSize,
        descs: this.groupPage.descs,
        ascs: this.groupPage.ascs,
      }, params, this.paramsSearch)).then(res => {
        this.tableData = res.data.data.records
        this.groupPage.total = res.data.data.total
        this.groupPage.currentPage = page.currentPage
        this.groupPage.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },

    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleJoinUpdate: function (row, index, done, loading) {
      putObj(row).then(res => {
        this.$message({
          showClose: true,
          message: '修改成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * 刷新回调
     */
    refreshGroup(page) {
      // console.log('刷新')
      this.getGroupPage(this.groupPage, {id: this.id, status: this.status})
    },
    selectionChange(list) {
      let res = [];
      list.forEach(obj => {
        res.push({
          grouponId: obj.grouponId,
          groupId: obj.groupId,
          grouponNum: obj.grouponNum,
          children: obj.children,
        });
      })
      this.bathList = res;
    },
    rowStyle ({ row, rowIndex }) {
      if (row.isLeader == '1' ) {
        return {
          backgroundColor: "#b8cfef",
        };
      }
      return "";
    },
    getLackNum(row){
      if(row.isLeader =='1') {
        let num = row.grouponNum -( row.children ? row.children.length+1 : 1);
        return num;
      }else if(row.isLeader =='0') {
        return "";
      }
    },
    // 根据订单状态返回对应的标签类型
    getOrderStatusTagType(statusDesc) {
      if (statusDesc === '已完成') {
        return 'success'
      } else if (statusDesc === '已取消') {
        return 'danger' // Element UI的el-tag使用danger表示错误状态
      } else {
        return '' // 默认样式
      }
    },
    // 获取最新的退款状态
    getLatestRefundStatus(row) {
      // 从订单数据中获取退款状态
      if (row.orderInfo && row.orderInfo.listOrderRefunds && row.orderInfo.listOrderRefunds.length > 0) {
        const latestRefund = row.orderInfo.listOrderRefunds[row.orderInfo.listOrderRefunds.length - 1]
        return latestRefund.statusDesc || latestRefund.status || '-'
      }
      return null
    },
    // 根据退款状态返回对应的标签类型
    getRefundStatusTagType(statusDesc) {
      if (statusDesc === '退款申请中') {
        return 'warning' // 黄色
      } else if (statusDesc === '同意退款') {
        return 'danger' // 红色
      } else if (statusDesc === '拒绝') {
        return '' // primary蓝色（默认）
      } else {
        return 'info' // 其他状态用info灰色
      }
    },
    // 获取购买详情
    getPurchaseDetail(row) {
      // 从订单数据中获取商品详情
      if (row.orderInfo && row.orderInfo.listOrderItem && row.orderInfo.listOrderItem.length > 0) {
        const firstItem = row.orderInfo.listOrderItem[0]
        return `${firstItem.spuName}|${firstItem.specInfo}`
      }
      return null
    }
  }
};
</script>

<style lang="scss" scoped>
</style>
