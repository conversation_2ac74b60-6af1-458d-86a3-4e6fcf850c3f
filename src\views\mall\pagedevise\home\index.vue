<template>
  <div class="execution">
    <basic-container>
      <avue-crud ref="crud"
                 :page.sync="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 :before-open="beforeOpen"
                 v-model="form"
                 @on-load="getPage"
                 @refresh-change="refreshChange"
                 @row-update="handleUpdate"
                 @row-save="handleSave"
                 @row-del="handleDel"
                 @sort-change="sortChange"
                 @search-change="searchChange">

        <template slot="menuLeft">
          <el-button v-if="showAddMobile" type="primary" icon="el-icon-plus" size="small"  @click.stop="addPage()">新增移动端</el-button>
          <el-button v-if="showAddPC" type="primary" icon="el-icon-plus" size="small"  @click.stop="addPCPage()">新增PC端</el-button>
<!--          <el-button type="primary" icon="el-icon-plus" size="small"  @click.stop="addPageShop()">新增店铺装修</el-button>-->
        </template>
        <template slot-scope="scope" slot="menu">
          <el-button icon="el-icon-edit" size="mini" type="text" @click.stop="editPage(scope.row,scope.index)">编辑</el-button>
        </template>
        <template slot="clientType" slot-scope="scope">
          <div> <i class="el-icon-s-promotion"></i> {{scope.row.clientType}}</div>
        </template>
        <template slot="clientTypeForm" slot-scope="scope">
          <el-select v-model="scope.row.clientType" placeholder="请选择" :disabled="opemType == 'edit'">
            <el-option
              v-for="item in clientTypeData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              :disabled="item.disabled">
            </el-option>
          </el-select>
        </template>
        <template slot="pageComponentForm" slot-scope="scope">
          <div>
            <!-- 商城首页装修预览 -->
<!--            <page-preview :componentsList="scope.row.pageComponent.componentsList"></page-preview>-->
            <!--{{scope.row.pageComponent}}-->
          </div>
        </template>
      </avue-crud>
    </basic-container>
  </div>
</template>

<script>
  import {getPage, getObj, addObj, putObj, delObj} from '@/api/mall/pagedevise'
  import {getObj2} from '@/api/mall/thememobile'
  import {tableOption} from '@/const/crud/mall/pagedevise'

  import { mapState , mapGetters , mapMutations , mapActions } from 'vuex';

  import pagePreview from "../component-library/pages/page-components/pagePreview.vue";

  export default {
    name: 'pagedevise',
    components: {
      pagePreview
    },
    data() {
      return {
        showAddMobile: true,//是否显示新增
        showAddPC: true,//是否显示新增
        thememobile: {},//必须配置主题才能新增页面
        form: {},
        tableData: [],
        page: {
          total: 0, // 总页数
          currentPage: 1, // 当前页数
          pageSize: 20, // 每页显示多少条
          ascs: [],//升序字段
          descs: []//降序字段
        },
        paramsSearch: {},
        tableLoading: false,
        tableOption: tableOption,
        opemType: null,
        clientTypeData: [{
          label: 'H5',
          value: 'H5'
        }, {
          label: 'APP',
          value: 'APP'
        }, {
          label: '小程序',
          value: 'MA'
        }]
      }
    },
    created() {
      //必须配置主题才能新增页面
      getObj2().then(response => {
        this.thememobile = response.data.data
      });
    },
    mounted: function () {
    },
    computed: {
      ...mapState({
        pageId     : state => state.divpage.pageId,//页面ID
        userInfo: state => state.user.userInfo,
      }),
      ...mapGetters(["permissions"]),
      permissionList() {
        return {
          addBtn: this.permissions['mall:pagedevise:add'] ? true : false,
          delBtn: this.permissions['mall:pagedevise:del'] ? true : false,
          editBtn: this.permissions['mall:pagedevise:edit'] ? true : false,
          viewBtn: this.permissions['mall:pagedevise:get'] ? true : false
        };
      }
    },
    methods: {
      ...mapMutations([
        'updateData'
      ]),
      addPCPage(){
        if(!this.thememobile){
          this.$message({
            showClose: true,
            message: '必须先配置主题才能新增',
            type: 'info'
          });
          return;
        }
        let pagePCdata=this.tableData.find(item=>item.clientType=='H5-PC')
        let query = ''
        if(pagePCdata){
          query = '?id=' + pagePCdata.id
        }
        //兼容
        window.localStorage.removeItem('PCdivPageId');
        // 用于获取缓存租户的装修id
        let tenantId = this.userInfo.switchTenantId || this.userInfo.tenantId
        window.localStorage.removeItem('PCdivPageId_'+tenantId+this.userInfo.username);
        window.open('/#/mall/config/decorate/home/<USER>/addPagePC'+query);
      },
      addPage(){
        if(!this.thememobile){
          this.$message({
            showClose: true,
            message: '必须先配置主题才能新增',
            type: 'info'
          });
          return;
        }
        let mobileTypes = this.tableData.filter((item)=>{
          return item.clientType==='H5' || item.clientType==='APP' || item.clientType==='MA'
        })
        if(mobileTypes.length>2){
          this.$message({
            showClose: true,
            message: '目前只能新增【H5】【APP】【小程序】3种不同类型。',
            type: 'warning'
          });
          return;
        }
        //兼容
        window.localStorage.removeItem('divPageId');
        // 用于获取缓存租户的装修id
        let tenantId = this.userInfo.switchTenantId || this.userInfo.tenantId
        window.localStorage.removeItem('divPageId_'+tenantId+this.userInfo.username);
        this.updateData({pageId: null});
        this.$router.push({name: '商城页面装修', });
      },
      editPage(row){
        if(row.clientType=='H5-PC'){
          this.addPCPage()
        }else{
          this.$router.push({name: '商城页面装修', params: {id: row.id}});
        }
      },
      changeEnable(row){
        putObj(row).then(response => {
          this.$message({
            showClose: true,
            message: '修改成功',
            type: 'success'
          })
          this.getPage(this.page)
        }).catch(() => {
        })
      },
      searchChange(params, done) {
        params = this.filterForm(params)
        this.paramsSearch = params
        this.page.currentPage = 1
        this.getPage(this.page, params)
        done()
      },
      sortChange(val) {
        let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
        if (val.order == 'ascending') {
          this.page.descs = []
          this.page.ascs = prop
        } else if (val.order == 'descending') {
          this.page.ascs = []
          this.page.descs = prop
        } else {
          this.page.ascs = []
          this.page.descs = []
        }
        this.getPage(this.page)
      },
      getPage(page, params) {
        this.tableLoading = true
        getPage(Object.assign({
          current: page.currentPage,
          size: page.pageSize,
          descs: this.page.descs,
          ascs: this.page.ascs,
          pageType: '1'
        }, params, this.paramsSearch)).then(response => {
          this.tableData = response.data.data.records
          this.page.total = response.data.data.total
          this.page.currentPage = page.currentPage
          this.page.pageSize = page.pageSize
          this.tableLoading = false
          let clientTypeData = this.clientTypeData
          let mobileCount = 0
          let pcCount = 0
          for (let i=0;i<clientTypeData.length;i++) {
            clientTypeData[i].disabled = false
            for(let j=0;j<this.tableData.length;j++){
              if(clientTypeData[i].value == this.tableData[j].clientType){
                clientTypeData[i].disabled = true
                mobileCount++
              }
              if(pcCount==0&&'H5-PC'==this.tableData[j].clientType ){
                pcCount++
              }
            }
          }
          if(mobileCount>2){
            this.showAddMobile = false
          }else{

            this.showAddMobile = true
          }
          if(pcCount>0){
            this.showAddPC = false
          }else{
            this.showAddPC = true
          }
        }).catch(() => {
          this.tableLoading = false
        })
      },
      /**
       * @title 数据删除
       * @param row 为当前的数据
       * @param index 为当前删除数据的行数
       *
       **/
      handleDel: function (row, index) {
        let _this = this
        this.$confirm('是否确认删除此数据', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return delObj(row.id)
        }).then(data => {
          _this.$message({
            showClose: true,
            message: '删除成功',
            type: 'success'
          })
          this.getPage(this.page)
        }).catch(function (err) {
        })
      },
      /**
       * @title 数据更新
       * @param row 为当前的数据
       * @param index 为当前更新数据的行数
       * @param done 为表单关闭函数
       *
       **/
      handleUpdate: function (row, index, done, loading) {
        row.pageType = '1'
        this.$refs.pageCompRef.submitData().then(pageComponent => {
          row.pageComponent = { componentsList: pageComponent?JSON.parse(pageComponent):[]}
          putObj(row).then(response => {
            this.$message({
              showClose: true,
              message: '修改成功',
              type: 'success'
            })
          done()
            this.getPage(this.page)
          }).catch(() => {
            loading()
          })
        }).catch(() => {
          loading()
        })
      },
      /**
       * @title 数据添加
       * @param row 为当前的数据
       * @param done 为表单关闭函数
       *
       **/
      handleSave: function (row, done, loading) {
        row.pageType = '1'
        row.pageName = '商城首页'
        this.$refs.pageCompRef.submitData().then(pageComponent => {
          row.pageComponent = { componentsList: pageComponent?JSON.parse(pageComponent):[]}
          addObj(row).then(response => {
            this.$message({
              showClose: true,
              message: '添加成功',
              type: 'success'
            })
            done()
            this.getPage(this.page)
          }).catch(() => {
            loading()
          })
        }).catch(() => {
          loading()
        })
      },
      /**
       * 刷新回调
       */
      refreshChange(page) {
        this.getPage(this.page)
      },
      beforeOpen(done,type){
        this.opemType = type
        done()
      },
    }
  }
</script>

